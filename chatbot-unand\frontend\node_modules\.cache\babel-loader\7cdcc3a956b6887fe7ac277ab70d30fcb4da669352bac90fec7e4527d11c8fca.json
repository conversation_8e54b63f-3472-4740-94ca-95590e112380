{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 2v2.343\",\n  key: \"15t272\"\n}], [\"path\", {\n  d: \"M14 2v6.343\",\n  key: \"sxr80q\"\n}], [\"path\", {\n  d: \"m2 2 20 20\",\n  key: \"1ooewy\"\n}], [\"path\", {\n  d: \"M20 20a2 2 0 0 1-2 2H6a2 2 0 0 1-1.755-2.96l5.227-9.563\",\n  key: \"k0duyd\"\n}], [\"path\", {\n  d: \"M6.453 15H15\",\n  key: \"1f0z33\"\n}], [\"path\", {\n  d: \"M8.5 2h7\",\n  key: \"csnxdl\"\n}]];\nconst FlaskConicalOff = createLucideIcon(\"flask-conical-off\", __iconNode);\nexport { __iconNode, FlaskConicalOff as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "FlaskConicalOff", "createLucideIcon"], "sources": ["D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\node_modules\\lucide-react\\src\\icons\\flask-conical-off.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M10 2v2.343', key: '15t272' }],\n  ['path', { d: 'M14 2v6.343', key: 'sxr80q' }],\n  ['path', { d: 'm2 2 20 20', key: '1ooewy' }],\n  ['path', { d: 'M20 20a2 2 0 0 1-2 2H6a2 2 0 0 1-1.755-2.96l5.227-9.563', key: 'k0duyd' }],\n  ['path', { d: 'M6.453 15H15', key: '1f0z33' }],\n  ['path', { d: 'M8.5 2h7', key: 'csnxdl' }],\n];\n\n/**\n * @component @name FlaskConicalOff\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAgMnYyLjM0MyIgLz4KICA8cGF0aCBkPSJNMTQgMnY2LjM0MyIgLz4KICA8cGF0aCBkPSJtMiAyIDIwIDIwIiAvPgogIDxwYXRoIGQ9Ik0yMCAyMGEyIDIgMCAwIDEtMiAySDZhMiAyIDAgMCAxLTEuNzU1LTIuOTZsNS4yMjctOS41NjMiIC8+CiAgPHBhdGggZD0iTTYuNDUzIDE1SDE1IiAvPgogIDxwYXRoIGQ9Ik04LjUgMmg3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/flask-conical-off\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst FlaskConicalOff = createLucideIcon('flask-conical-off', __iconNode);\n\nexport default FlaskConicalOff;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,aAAe;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC5C,CAAC,MAAQ;EAAED,CAAA,EAAG,aAAe;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC5C,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,yDAA2D;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxF,CAAC,MAAQ;EAAED,CAAA,EAAG,cAAgB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7C,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAU,GAC3C;AAaM,MAAAC,eAAA,GAAkBC,gBAAiB,sBAAqBJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}