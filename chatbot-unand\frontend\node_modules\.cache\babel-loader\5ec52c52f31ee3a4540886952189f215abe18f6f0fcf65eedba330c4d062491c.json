{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m13 21-3-3 3-3\",\n  key: \"s3o1nf\"\n}], [\"path\", {\n  d: \"M20 18H10\",\n  key: \"14r3mt\"\n}], [\"path\", {\n  d: \"M3 11h.01\",\n  key: \"1eifu7\"\n}], [\"rect\", {\n  x: \"6\",\n  y: \"3\",\n  width: \"5\",\n  height: \"8\",\n  rx: \"2.5\",\n  key: \"v9paqo\"\n}]];\nconst DecimalsArrowLeft = createLucideIcon(\"decimals-arrow-left\", __iconNode);\nexport { __iconNode, DecimalsArrowLeft as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "x", "y", "width", "height", "rx", "DecimalsArrowLeft", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\node_modules\\lucide-react\\src\\icons\\decimals-arrow-left.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm13 21-3-3 3-3', key: 's3o1nf' }],\n  ['path', { d: 'M20 18H10', key: '14r3mt' }],\n  ['path', { d: 'M3 11h.01', key: '1eifu7' }],\n  ['rect', { x: '6', y: '3', width: '5', height: '8', rx: '2.5', key: 'v9paqo' }],\n];\n\n/**\n * @component @name DecimalsArrowLeft\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTMgMjEtMy0zIDMtMyIgLz4KICA8cGF0aCBkPSJNMjAgMThIMTAiIC8+CiAgPHBhdGggZD0iTTMgMTFoLjAxIiAvPgogIDxyZWN0IHg9IjYiIHk9IjMiIHdpZHRoPSI1IiBoZWlnaHQ9IjgiIHJ4PSIyLjUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/decimals-arrow-left\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst DecimalsArrowLeft = createLucideIcon('decimals-arrow-left', __iconNode);\n\nexport default DecimalsArrowLeft;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,gBAAkB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/C,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAKC,CAAG;EAAKC,KAAO;EAAKC,MAAA,EAAQ,GAAK;EAAAC,EAAA,EAAI,KAAO;EAAAL,GAAA,EAAK;AAAU,GAChF;AAaM,MAAAM,iBAAA,GAAoBC,gBAAiB,wBAAuBT,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}