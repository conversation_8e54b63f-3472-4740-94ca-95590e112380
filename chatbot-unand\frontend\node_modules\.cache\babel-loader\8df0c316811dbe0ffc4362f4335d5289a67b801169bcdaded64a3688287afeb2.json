{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M13.5 8h-3\",\n  key: \"xvov4w\"\n}], [\"path\", {\n  d: \"m15 2-1 2h3a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h3\",\n  key: \"16uttc\"\n}], [\"path\", {\n  d: \"M16.899 22A5 5 0 0 0 7.1 22\",\n  key: \"1d0ppr\"\n}], [\"path\", {\n  d: \"m9 2 3 6\",\n  key: \"1o7bd9\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"15\",\n  r: \"3\",\n  key: \"g36mzq\"\n}]];\nconst IdCardLanyard = createLucideIcon(\"id-card-lanyard\", __iconNode);\nexport { __iconNode, IdCardLanyard as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "cx", "cy", "r", "IdCardLanyard", "createLucideIcon"], "sources": ["D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\node_modules\\lucide-react\\src\\icons\\id-card-lanyard.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M13.5 8h-3', key: 'xvov4w' }],\n  [\n    'path',\n    {\n      d: 'm15 2-1 2h3a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h3',\n      key: '16uttc',\n    },\n  ],\n  ['path', { d: 'M16.899 22A5 5 0 0 0 7.1 22', key: '1d0ppr' }],\n  ['path', { d: 'm9 2 3 6', key: '1o7bd9' }],\n  ['circle', { cx: '12', cy: '15', r: '3', key: 'g36mzq' }],\n];\n\n/**\n * @component @name IdCardLanyard\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTMuNSA4aC0zIiAvPgogIDxwYXRoIGQ9Im0xNSAyLTEgMmgzYTIgMiAwIDAgMSAyIDJ2MTRhMiAyIDAgMCAxLTIgMkg3YTIgMiAwIDAgMS0yLTJWNmEyIDIgMCAwIDEgMi0yaDMiIC8+CiAgPHBhdGggZD0iTTE2Ljg5OSAyMkE1IDUgMCAwIDAgNy4xIDIyIiAvPgogIDxwYXRoIGQ9Im05IDIgMyA2IiAvPgogIDxjaXJjbGUgY3g9IjEyIiBjeT0iMTUiIHI9IjMiIC8+Cjwvc3ZnPg==) - https://lucide.dev/icons/id-card-lanyard\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst IdCardLanyard = createLucideIcon('id-card-lanyard', __iconNode);\n\nexport default IdCardLanyard;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CACE,QACA;EACED,CAAG;EACHC,GAAK;AAAA,EAET,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,6BAA+B;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC5D,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKH,GAAK;AAAU,GAC1D;AAaM,MAAAI,aAAA,GAAgBC,gBAAiB,oBAAmBP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}