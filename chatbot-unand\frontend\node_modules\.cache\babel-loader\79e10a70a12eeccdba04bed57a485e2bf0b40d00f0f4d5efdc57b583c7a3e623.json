{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"18\",\n  r: \"4\",\n  key: \"m3r9ws\"\n}], [\"path\", {\n  d: \"M16 18V2\",\n  key: \"40x2m5\"\n}]];\nconst Music3 = createLucideIcon(\"music-3\", __iconNode);\nexport { __iconNode, Music3 as default };", "map": {"version": 3, "names": ["__iconNode", "cx", "cy", "r", "key", "d", "Music3", "createLucideIcon"], "sources": ["D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\node_modules\\lucide-react\\src\\icons\\music-3.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '18', r: '4', key: 'm3r9ws' }],\n  ['path', { d: 'M16 18V2', key: '40x2m5' }],\n];\n\n/**\n * @component @name Music3\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjE4IiByPSI0IiAvPgogIDxwYXRoIGQ9Ik0xNiAxOFYyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/music-3\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Music3 = createLucideIcon('music-3', __iconNode);\n\nexport default Music3;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAD,GAAA,EAAK;AAAU,GAC3C;AAaM,MAAAE,MAAA,GAASC,gBAAiB,YAAWP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}