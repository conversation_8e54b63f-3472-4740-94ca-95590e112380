{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M3 2h18\",\n  key: \"15qxfx\"\n}], [\"rect\", {\n  width: \"18\",\n  height: \"12\",\n  x: \"3\",\n  y: \"6\",\n  rx: \"2\",\n  key: \"1439r6\"\n}], [\"path\", {\n  d: \"M3 22h18\",\n  key: \"8prr45\"\n}]];\nconst GalleryVertical = createLucideIcon(\"gallery-vertical\", __iconNode);\nexport { __iconNode, GalleryVertical as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "width", "height", "x", "y", "rx", "GalleryVertical", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\node_modules\\lucide-react\\src\\icons\\gallery-vertical.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M3 2h18', key: '15qxfx' }],\n  ['rect', { width: '18', height: '12', x: '3', y: '6', rx: '2', key: '1439r6' }],\n  ['path', { d: 'M3 22h18', key: '8prr45' }],\n];\n\n/**\n * @component @name GalleryVertical\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyAyaDE4IiAvPgogIDxyZWN0IHdpZHRoPSIxOCIgaGVpZ2h0PSIxMiIgeD0iMyIgeT0iNiIgcng9IjIiIC8+CiAgPHBhdGggZD0iTTMgMjJoMTgiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/gallery-vertical\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst GalleryVertical = createLucideIcon('gallery-vertical', __iconNode);\n\nexport default GalleryVertical;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,QAAQ;EAAEC,KAAA,EAAO;EAAMC,MAAQ;EAAMC,CAAG;EAAKC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAL,GAAA,EAAK;AAAA,CAAU,GAC9E,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAU,GAC3C;AAaM,MAAAM,eAAA,GAAkBC,gBAAiB,qBAAoBT,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}