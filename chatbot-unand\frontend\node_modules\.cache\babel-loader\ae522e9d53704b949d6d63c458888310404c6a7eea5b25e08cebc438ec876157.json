{"ast": null, "code": "const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || \"http://localhost:8001\";\nexport const sendMessageToChatbot = async (query, sessionId = null) => {\n  try {\n    const response = await fetch(`${API_BASE_URL}/chat`, {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\"\n      },\n      body: JSON.stringify({\n        query,\n        session_id: sessionId\n      })\n    });\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.detail || \"Terjadi kesalahan pada server.\");\n    }\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error(\"Error sending message to chatbot:\", error);\n    throw error;\n  }\n};\nexport const uploadDocument = async file => {\n  try {\n    const formData = new FormData();\n    formData.append(\"file\", file); // 'file' harus cocok dengan nama parameter di FastAPI\n\n    const response = await fetch(`${API_BASE_URL}/upload-document`, {\n      method: \"POST\",\n      body: formData // FormData akan mengatur header Content-Type secara otomatis\n    });\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.detail || \"Gagal mengunggah dokumen.\");\n    }\n    const data = await response.json();\n    return data.message;\n  } catch (error) {\n    console.error(\"Error uploading document:\", error);\n    throw error; // Re-throw to be handled by the component\n  }\n};\n\n// Session management functions\nexport const createSession = async (title = null) => {\n  try {\n    const response = await fetch(`${API_BASE_URL}/sessions`, {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\"\n      },\n      body: JSON.stringify({\n        title\n      })\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error(\"Error creating session:\", error);\n    throw error;\n  }\n};\nexport const getSessions = async () => {\n  try {\n    const response = await fetch(`${API_BASE_URL}/sessions`);\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error(\"Error getting sessions:\", error);\n    throw error;\n  }\n};\nexport const getSessionMessages = async sessionId => {\n  try {\n    const response = await fetch(`${API_BASE_URL}/sessions/${sessionId}/messages`);\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error(\"Error getting session messages:\", error);\n    throw error;\n  }\n};\nexport const updateSession = async (sessionId, title) => {\n  try {\n    const response = await fetch(`${API_BASE_URL}/sessions/${sessionId}`, {\n      method: \"PUT\",\n      headers: {\n        \"Content-Type\": \"application/json\"\n      },\n      body: JSON.stringify({\n        title\n      })\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error(\"Error updating session:\", error);\n    throw error;\n  }\n};\nexport const deleteSession = async sessionId => {\n  try {\n    const response = await fetch(`${API_BASE_URL}/sessions/${sessionId}`, {\n      method: \"DELETE\"\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error(\"Error deleting session:\", error);\n    throw error;\n  }\n};", "map": {"version": 3, "names": ["API_BASE_URL", "process", "env", "REACT_APP_API_BASE_URL", "sendMessageToChatbot", "query", "sessionId", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "session_id", "ok", "errorData", "json", "Error", "detail", "data", "error", "console", "uploadDocument", "file", "formData", "FormData", "append", "message", "createSession", "title", "status", "getSessions", "getSessionMessages", "updateSession", "deleteSession"], "sources": ["C:/Users/<USER>/Downloads/website kp/chatbot-unand/frontend/src/api.js"], "sourcesContent": ["const API_BASE_URL =\n  process.env.REACT_APP_API_BASE_URL || \"http://localhost:8001\";\n\nexport const sendMessageToChatbot = async (query, sessionId = null) => {\n  try {\n    const response = await fetch(`${API_BASE_URL}/chat`, {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n      },\n      body: JSON.stringify({\n        query,\n        session_id: sessionId,\n      }),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.detail || \"Terjadi kesalahan pada server.\");\n    }\n\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error(\"Error sending message to chatbot:\", error);\n    throw error;\n  }\n};\n\nexport const uploadDocument = async (file) => {\n  try {\n    const formData = new FormData();\n    formData.append(\"file\", file); // 'file' harus cocok dengan nama parameter di FastAPI\n\n    const response = await fetch(`${API_BASE_URL}/upload-document`, {\n      method: \"POST\",\n      body: formData, // FormData akan mengatur header Content-Type secara otomatis\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.detail || \"Gagal mengunggah dokumen.\");\n    }\n\n    const data = await response.json();\n    return data.message;\n  } catch (error) {\n    console.error(\"Error uploading document:\", error);\n    throw error; // Re-throw to be handled by the component\n  }\n};\n\n// Session management functions\nexport const createSession = async (title = null) => {\n  try {\n    const response = await fetch(`${API_BASE_URL}/sessions`, {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n      },\n      body: JSON.stringify({ title }),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error(\"Error creating session:\", error);\n    throw error;\n  }\n};\n\nexport const getSessions = async () => {\n  try {\n    const response = await fetch(`${API_BASE_URL}/sessions`);\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error(\"Error getting sessions:\", error);\n    throw error;\n  }\n};\n\nexport const getSessionMessages = async (sessionId) => {\n  try {\n    const response = await fetch(\n      `${API_BASE_URL}/sessions/${sessionId}/messages`\n    );\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error(\"Error getting session messages:\", error);\n    throw error;\n  }\n};\n\nexport const updateSession = async (sessionId, title) => {\n  try {\n    const response = await fetch(`${API_BASE_URL}/sessions/${sessionId}`, {\n      method: \"PUT\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n      },\n      body: JSON.stringify({ title }),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error(\"Error updating session:\", error);\n    throw error;\n  }\n};\n\nexport const deleteSession = async (sessionId) => {\n  try {\n    const response = await fetch(`${API_BASE_URL}/sessions/${sessionId}`, {\n      method: \"DELETE\",\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error(\"Error deleting session:\", error);\n    throw error;\n  }\n};\n"], "mappings": "AAAA,MAAMA,YAAY,GAChBC,OAAO,CAACC,GAAG,CAACC,sBAAsB,IAAI,uBAAuB;AAE/D,OAAO,MAAMC,oBAAoB,GAAG,MAAAA,CAAOC,KAAK,EAAEC,SAAS,GAAG,IAAI,KAAK;EACrE,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGR,YAAY,OAAO,EAAE;MACnDS,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB,CAAC;MACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QACnBR,KAAK;QACLS,UAAU,EAAER;MACd,CAAC;IACH,CAAC,CAAC;IAEF,IAAI,CAACC,QAAQ,CAACQ,EAAE,EAAE;MAChB,MAAMC,SAAS,GAAG,MAAMT,QAAQ,CAACU,IAAI,CAAC,CAAC;MACvC,MAAM,IAAIC,KAAK,CAACF,SAAS,CAACG,MAAM,IAAI,gCAAgC,CAAC;IACvE;IAEA,MAAMC,IAAI,GAAG,MAAMb,QAAQ,CAACU,IAAI,CAAC,CAAC;IAClC,OAAOG,IAAI;EACb,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;IACzD,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAME,cAAc,GAAG,MAAOC,IAAI,IAAK;EAC5C,IAAI;IACF,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEH,IAAI,CAAC,CAAC,CAAC;;IAE/B,MAAMjB,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGR,YAAY,kBAAkB,EAAE;MAC9DS,MAAM,EAAE,MAAM;MACdE,IAAI,EAAEc,QAAQ,CAAE;IAClB,CAAC,CAAC;IAEF,IAAI,CAAClB,QAAQ,CAACQ,EAAE,EAAE;MAChB,MAAMC,SAAS,GAAG,MAAMT,QAAQ,CAACU,IAAI,CAAC,CAAC;MACvC,MAAM,IAAIC,KAAK,CAACF,SAAS,CAACG,MAAM,IAAI,2BAA2B,CAAC;IAClE;IAEA,MAAMC,IAAI,GAAG,MAAMb,QAAQ,CAACU,IAAI,CAAC,CAAC;IAClC,OAAOG,IAAI,CAACQ,OAAO;EACrB,CAAC,CAAC,OAAOP,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACjD,MAAMA,KAAK,CAAC,CAAC;EACf;AACF,CAAC;;AAED;AACA,OAAO,MAAMQ,aAAa,GAAG,MAAAA,CAAOC,KAAK,GAAG,IAAI,KAAK;EACnD,IAAI;IACF,MAAMvB,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGR,YAAY,WAAW,EAAE;MACvDS,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB,CAAC;MACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QAAEiB;MAAM,CAAC;IAChC,CAAC,CAAC;IAEF,IAAI,CAACvB,QAAQ,CAACQ,EAAE,EAAE;MAChB,MAAM,IAAIG,KAAK,CAAC,uBAAuBX,QAAQ,CAACwB,MAAM,EAAE,CAAC;IAC3D;IAEA,OAAO,MAAMxB,QAAQ,CAACU,IAAI,CAAC,CAAC;EAC9B,CAAC,CAAC,OAAOI,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IAC/C,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMW,WAAW,GAAG,MAAAA,CAAA,KAAY;EACrC,IAAI;IACF,MAAMzB,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGR,YAAY,WAAW,CAAC;IAExD,IAAI,CAACO,QAAQ,CAACQ,EAAE,EAAE;MAChB,MAAM,IAAIG,KAAK,CAAC,uBAAuBX,QAAQ,CAACwB,MAAM,EAAE,CAAC;IAC3D;IAEA,OAAO,MAAMxB,QAAQ,CAACU,IAAI,CAAC,CAAC;EAC9B,CAAC,CAAC,OAAOI,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IAC/C,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMY,kBAAkB,GAAG,MAAO3B,SAAS,IAAK;EACrD,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAC1B,GAAGR,YAAY,aAAaM,SAAS,WACvC,CAAC;IAED,IAAI,CAACC,QAAQ,CAACQ,EAAE,EAAE;MAChB,MAAM,IAAIG,KAAK,CAAC,uBAAuBX,QAAQ,CAACwB,MAAM,EAAE,CAAC;IAC3D;IAEA,OAAO,MAAMxB,QAAQ,CAACU,IAAI,CAAC,CAAC;EAC9B,CAAC,CAAC,OAAOI,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACvD,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMa,aAAa,GAAG,MAAAA,CAAO5B,SAAS,EAAEwB,KAAK,KAAK;EACvD,IAAI;IACF,MAAMvB,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGR,YAAY,aAAaM,SAAS,EAAE,EAAE;MACpEG,MAAM,EAAE,KAAK;MACbC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB,CAAC;MACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QAAEiB;MAAM,CAAC;IAChC,CAAC,CAAC;IAEF,IAAI,CAACvB,QAAQ,CAACQ,EAAE,EAAE;MAChB,MAAM,IAAIG,KAAK,CAAC,uBAAuBX,QAAQ,CAACwB,MAAM,EAAE,CAAC;IAC3D;IAEA,OAAO,MAAMxB,QAAQ,CAACU,IAAI,CAAC,CAAC;EAC9B,CAAC,CAAC,OAAOI,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IAC/C,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMc,aAAa,GAAG,MAAO7B,SAAS,IAAK;EAChD,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGR,YAAY,aAAaM,SAAS,EAAE,EAAE;MACpEG,MAAM,EAAE;IACV,CAAC,CAAC;IAEF,IAAI,CAACF,QAAQ,CAACQ,EAAE,EAAE;MAChB,MAAM,IAAIG,KAAK,CAAC,uBAAuBX,QAAQ,CAACwB,MAAM,EAAE,CAAC;IAC3D;IAEA,OAAO,MAAMxB,QAAQ,CAACU,IAAI,CAAC,CAAC;EAC9B,CAAC,CAAC,OAAOI,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IAC/C,MAAMA,KAAK;EACb;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}