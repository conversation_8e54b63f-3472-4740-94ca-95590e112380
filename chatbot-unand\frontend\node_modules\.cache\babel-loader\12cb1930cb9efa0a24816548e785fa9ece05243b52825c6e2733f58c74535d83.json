{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M3.5 13h6\",\n  key: \"p1my2r\"\n}], [\"path\", {\n  d: \"m2 16 4.5-9 4.5 9\",\n  key: \"ndf0b3\"\n}], [\"path\", {\n  d: \"M18 16V7\",\n  key: \"ty0viw\"\n}], [\"path\", {\n  d: \"m14 11 4-4 4 4\",\n  key: \"1pu57t\"\n}]];\nconst AArrowUp = createLucideIcon(\"a-arrow-up\", __iconNode);\nexport { __iconNode, AArrowUp as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "AArrowUp", "createLucideIcon"], "sources": ["D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\node_modules\\lucide-react\\src\\icons\\a-arrow-up.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M3.5 13h6', key: 'p1my2r' }],\n  ['path', { d: 'm2 16 4.5-9 4.5 9', key: 'ndf0b3' }],\n  ['path', { d: 'M18 16V7', key: 'ty0viw' }],\n  ['path', { d: 'm14 11 4-4 4 4', key: '1pu57t' }],\n];\n\n/**\n * @component @name AArrowUp\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMy41IDEzaDYiIC8+CiAgPHBhdGggZD0ibTIgMTYgNC41LTkgNC41IDkiIC8+CiAgPHBhdGggZD0iTTE4IDE2VjciIC8+CiAgPHBhdGggZD0ibTE0IDExIDQtNCA0IDQiIC8+Cjwvc3ZnPg==) - https://lucide.dev/icons/a-arrow-up\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst AArrowUp = createLucideIcon('a-arrow-up', __iconNode);\n\nexport default AArrowUp;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,mBAAqB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAClD,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,gBAAkB;EAAAC,GAAA,EAAK;AAAU,GACjD;AAaM,MAAAC,QAAA,GAAWC,gBAAiB,eAAcJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}