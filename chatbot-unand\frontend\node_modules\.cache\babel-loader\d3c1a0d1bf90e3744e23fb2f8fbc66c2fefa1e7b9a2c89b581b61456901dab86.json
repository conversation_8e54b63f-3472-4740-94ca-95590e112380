{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m8 2 1.88 1.88\",\n  key: \"fmnt4t\"\n}], [\"path\", {\n  d: \"M14.12 3.88 16 2\",\n  key: \"qol33r\"\n}], [\"path\", {\n  d: \"M9 7.13v-1a3.003 3.003 0 1 1 6 0v1\",\n  key: \"d7y7pr\"\n}], [\"path\", {\n  d: \"M12 20c-3.3 0-6-2.7-6-6v-3a4 4 0 0 1 4-4h4a4 4 0 0 1 4 4v3c0 3.3-2.7 6-6 6\",\n  key: \"xs1cw7\"\n}], [\"path\", {\n  d: \"M12 20v-9\",\n  key: \"1qisl0\"\n}], [\"path\", {\n  d: \"M6.53 9C4.6 8.8 3 7.1 3 5\",\n  key: \"32zzws\"\n}], [\"path\", {\n  d: \"M6 13H2\",\n  key: \"82j7cp\"\n}], [\"path\", {\n  d: \"M3 21c0-2.1 1.7-3.9 3.8-4\",\n  key: \"4p0ekp\"\n}], [\"path\", {\n  d: \"M20.97 5c0 2.1-1.6 3.8-3.5 4\",\n  key: \"18gb23\"\n}], [\"path\", {\n  d: \"M22 13h-4\",\n  key: \"1jl80f\"\n}], [\"path\", {\n  d: \"M17.2 17c2.1.1 3.8 1.9 3.8 4\",\n  key: \"k3fwyw\"\n}]];\nconst Bug = createLucideIcon(\"bug\", __iconNode);\nexport { __iconNode, Bug as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "Bug", "createLucideIcon"], "sources": ["D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\node_modules\\lucide-react\\src\\icons\\bug.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm8 2 1.88 1.88', key: 'fmnt4t' }],\n  ['path', { d: 'M14.12 3.88 16 2', key: 'qol33r' }],\n  ['path', { d: 'M9 7.13v-1a3.003 3.003 0 1 1 6 0v1', key: 'd7y7pr' }],\n  [\n    'path',\n    {\n      d: 'M12 20c-3.3 0-6-2.7-6-6v-3a4 4 0 0 1 4-4h4a4 4 0 0 1 4 4v3c0 3.3-2.7 6-6 6',\n      key: 'xs1cw7',\n    },\n  ],\n  ['path', { d: 'M12 20v-9', key: '1qisl0' }],\n  ['path', { d: 'M6.53 9C4.6 8.8 3 7.1 3 5', key: '32zzws' }],\n  ['path', { d: 'M6 13H2', key: '82j7cp' }],\n  ['path', { d: 'M3 21c0-2.1 1.7-3.9 3.8-4', key: '4p0ekp' }],\n  ['path', { d: 'M20.97 5c0 2.1-1.6 3.8-3.5 4', key: '18gb23' }],\n  ['path', { d: 'M22 13h-4', key: '1jl80f' }],\n  ['path', { d: 'M17.2 17c2.1.1 3.8 1.9 3.8 4', key: 'k3fwyw' }],\n];\n\n/**\n * @component @name Bug\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtOCAyIDEuODggMS44OCIgLz4KICA8cGF0aCBkPSJNMTQuMTIgMy44OCAxNiAyIiAvPgogIDxwYXRoIGQ9Ik05IDcuMTN2LTFhMy4wMDMgMy4wMDMgMCAxIDEgNiAwdjEiIC8+CiAgPHBhdGggZD0iTTEyIDIwYy0zLjMgMC02LTIuNy02LTZ2LTNhNCA0IDAgMCAxIDQtNGg0YTQgNCAwIDAgMSA0IDR2M2MwIDMuMy0yLjcgNi02IDYiIC8+CiAgPHBhdGggZD0iTTEyIDIwdi05IiAvPgogIDxwYXRoIGQ9Ik02LjUzIDlDNC42IDguOCAzIDcuMSAzIDUiIC8+CiAgPHBhdGggZD0iTTYgMTNIMiIgLz4KICA8cGF0aCBkPSJNMyAyMWMwLTIuMSAxLjctMy45IDMuOC00IiAvPgogIDxwYXRoIGQ9Ik0yMC45NyA1YzAgMi4xLTEuNiAzLjgtMy41IDQiIC8+CiAgPHBhdGggZD0iTTIyIDEzaC00IiAvPgogIDxwYXRoIGQ9Ik0xNy4yIDE3YzIuMS4xIDMuOCAxLjkgMy44IDQiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/bug\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Bug = createLucideIcon('bug', __iconNode);\n\nexport default Bug;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,gBAAkB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/C,CAAC,MAAQ;EAAED,CAAA,EAAG,kBAAoB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACjD,CAAC,MAAQ;EAAED,CAAA,EAAG,oCAAsC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACnE,CACE,QACA;EACED,CAAG;EACHC,GAAK;AAAA,EAET,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,2BAA6B;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1D,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,2BAA6B;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1D,CAAC,MAAQ;EAAED,CAAA,EAAG,8BAAgC;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7D,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,8BAAgC;EAAAC,GAAA,EAAK;AAAU,GAC/D;AAaM,MAAAC,GAAA,GAAMC,gBAAiB,QAAOJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}