{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M8 3H5a2 2 0 0 0-2 2v14c0 1.1.9 2 2 2h3\",\n  key: \"1i73f7\"\n}], [\"path\", {\n  d: \"M16 3h3a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-3\",\n  key: \"saxlbk\"\n}], [\"path\", {\n  d: \"M12 20v2\",\n  key: \"1lh1kg\"\n}], [\"path\", {\n  d: \"M12 14v2\",\n  key: \"8jcxud\"\n}], [\"path\", {\n  d: \"M12 8v2\",\n  key: \"1woqiv\"\n}], [\"path\", {\n  d: \"M12 2v2\",\n  key: \"tus03m\"\n}]];\nconst FlipHorizontal = createLucideIcon(\"flip-horizontal\", __iconNode);\nexport { __iconNode, FlipHorizontal as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "FlipHorizontal", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\node_modules\\lucide-react\\src\\icons\\flip-horizontal.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M8 3H5a2 2 0 0 0-2 2v14c0 1.1.9 2 2 2h3', key: '1i73f7' }],\n  ['path', { d: 'M16 3h3a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-3', key: 'saxlbk' }],\n  ['path', { d: 'M12 20v2', key: '1lh1kg' }],\n  ['path', { d: 'M12 14v2', key: '8jcxud' }],\n  ['path', { d: 'M12 8v2', key: '1woqiv' }],\n  ['path', { d: 'M12 2v2', key: 'tus03m' }],\n];\n\n/**\n * @component @name FlipHorizontal\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNOCAzSDVhMiAyIDAgMCAwLTIgMnYxNGMwIDEuMS45IDIgMiAyaDMiIC8+CiAgPHBhdGggZD0iTTE2IDNoM2EyIDIgMCAwIDEgMiAydjE0YTIgMiAwIDAgMS0yIDJoLTMiIC8+CiAgPHBhdGggZD0iTTEyIDIwdjIiIC8+CiAgPHBhdGggZD0iTTEyIDE0djIiIC8+CiAgPHBhdGggZD0iTTEyIDh2MiIgLz4KICA8cGF0aCBkPSJNMTIgMnYyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/flip-horizontal\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst FlipHorizontal = createLucideIcon('flip-horizontal', __iconNode);\n\nexport default FlipHorizontal;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,yCAA2C;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxE,CAAC,MAAQ;EAAED,CAAA,EAAG,2CAA6C;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1E,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAU,GAC1C;AAaM,MAAAC,cAAA,GAAiBC,gBAAiB,oBAAmBJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}