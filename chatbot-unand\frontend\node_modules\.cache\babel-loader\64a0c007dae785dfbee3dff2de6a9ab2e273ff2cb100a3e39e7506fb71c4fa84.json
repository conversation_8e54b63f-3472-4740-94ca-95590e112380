{"ast": null, "code": "/* global __resourceQuery WorkerGlobalScope */\n\n// Send messages to the outside, so plugins can consume it.\n/**\n * @param {string} type\n * @param {any} [data]\n */\nfunction sendMsg(type, data) {\n  if (typeof self !== \"undefined\" && (typeof WorkerGlobalScope === \"undefined\" || !(self instanceof WorkerGlobalScope))) {\n    self.postMessage({\n      type: \"webpack\".concat(type),\n      data: data\n    }, \"*\");\n  }\n}\nexport default sendMsg;", "map": {"version": 3, "names": ["sendMsg", "type", "data", "self", "WorkerGlobalScope", "postMessage", "concat"], "sources": ["C:/Users/<USER>/Downloads/website kp/chatbot-unand/frontend/node_modules/webpack-dev-server/client/utils/sendMessage.js"], "sourcesContent": ["/* global __resourceQuery WorkerGlobalScope */\n\n// Send messages to the outside, so plugins can consume it.\n/**\n * @param {string} type\n * @param {any} [data]\n */\nfunction sendMsg(type, data) {\n  if (typeof self !== \"undefined\" && (typeof WorkerGlobalScope === \"undefined\" || !(self instanceof WorkerGlobalScope))) {\n    self.postMessage({\n      type: \"webpack\".concat(type),\n      data: data\n    }, \"*\");\n  }\n}\nexport default sendMsg;"], "mappings": "AAAA;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASA,OAAOA,CAACC,IAAI,EAAEC,IAAI,EAAE;EAC3B,IAAI,OAAOC,IAAI,KAAK,WAAW,KAAK,OAAOC,iBAAiB,KAAK,WAAW,IAAI,EAAED,IAAI,YAAYC,iBAAiB,CAAC,CAAC,EAAE;IACrHD,IAAI,CAACE,WAAW,CAAC;MACfJ,IAAI,EAAE,SAAS,CAACK,MAAM,CAACL,IAAI,CAAC;MAC5BC,IAAI,EAAEA;IACR,CAAC,EAAE,GAAG,CAAC;EACT;AACF;AACA,eAAeF,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}