{"ast": null, "code": "// Use environment variable or fallback to port 8000\nconst API_BASE_URL = process.env.REACT_APP_API_BASE_URL || \"http://localhost:8000\";\nconsole.log(\"API: Using API_BASE_URL:\", API_BASE_URL);\nconsole.log(\"API: Environment REACT_APP_API_BASE_URL:\", process.env.REACT_APP_API_BASE_URL);\n\n// Helper function to get auth headers\nconst getAuthHeaders = () => {\n  const token = localStorage.getItem(\"access_token\");\n  const headers = {\n    \"Content-Type\": \"application/json\"\n  };\n  console.log(\"API: Getting auth headers...\");\n  console.log(\"API: Token from localStorage:\", token ? `${token.substring(0, 20)}...` : \"null\");\n  if (token) {\n    // Check if token is expired before using it\n    try {\n      const payload = JSON.parse(atob(token.split(\".\")[1]));\n      const currentTime = Math.floor(Date.now() / 1000);\n      console.log(\"API: Token payload:\", payload);\n      console.log(\"API: Current time:\", currentTime);\n      console.log(\"API: Token expires at:\", payload.exp);\n\n      // If token is expired, don't include it\n      if (payload.exp < currentTime) {\n        console.log(\"API: Token expired, not including in headers\");\n        localStorage.removeItem(\"access_token\");\n        localStorage.removeItem(\"user\");\n        return headers;\n      }\n      headers.Authorization = `Bearer ${token}`;\n      console.log(\"API: Added Authorization header\");\n    } catch (error) {\n      console.error(\"API: Error checking token:\", error);\n      // Remove invalid token\n      localStorage.removeItem(\"access_token\");\n      localStorage.removeItem(\"user\");\n    }\n  } else {\n    console.log(\"API: No token found in localStorage\");\n  }\n  console.log(\"API: Final headers:\", headers);\n  return headers;\n};\nexport const sendMessageToChatbot = async (query, sessionId = null) => {\n  try {\n    const response = await fetch(`${API_BASE_URL}/chat`, {\n      method: \"POST\",\n      headers: getAuthHeaders(),\n      body: JSON.stringify({\n        query,\n        session_id: sessionId\n      })\n    });\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.detail || \"Terjadi kesalahan pada server.\");\n    }\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error(\"Error sending message to chatbot:\", error);\n    throw error;\n  }\n};\n\n// Session management functions\nexport const createSession = async (title = null) => {\n  try {\n    const response = await fetch(`${API_BASE_URL}/sessions`, {\n      method: \"POST\",\n      headers: getAuthHeaders(),\n      body: JSON.stringify({\n        title\n      })\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error(\"Error creating session:\", error);\n    throw error;\n  }\n};\nexport const getSessions = async () => {\n  try {\n    console.log(\"API: Getting sessions from\", `${API_BASE_URL}/sessions`);\n    const headers = getAuthHeaders();\n    console.log(\"API: Using headers:\", headers);\n    const response = await fetch(`${API_BASE_URL}/sessions`, {\n      headers: headers\n    });\n    console.log(\"API: Sessions response status:\", response.status);\n    if (!response.ok) {\n      const errorText = await response.text();\n      console.error(\"API: Sessions error response:\", errorText);\n      throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);\n    }\n    const data = await response.json();\n    console.log(\"API: Sessions data received:\", data);\n    return data;\n  } catch (error) {\n    console.error(\"Error getting sessions:\", error);\n    throw error;\n  }\n};\nexport const getSessionMessages = async sessionId => {\n  try {\n    const response = await fetch(`${API_BASE_URL}/sessions/${sessionId}/messages`, {\n      headers: getAuthHeaders()\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error(\"Error getting session messages:\", error);\n    throw error;\n  }\n};\nexport const updateSession = async (sessionId, title) => {\n  try {\n    const response = await fetch(`${API_BASE_URL}/sessions/${sessionId}`, {\n      method: \"PUT\",\n      headers: getAuthHeaders(),\n      body: JSON.stringify({\n        title\n      })\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error(\"Error updating session:\", error);\n    throw error;\n  }\n};\nexport const deleteSession = async sessionId => {\n  try {\n    const response = await fetch(`${API_BASE_URL}/sessions/${sessionId}`, {\n      method: \"DELETE\",\n      headers: getAuthHeaders()\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error(\"Error deleting session:\", error);\n    throw error;\n  }\n};", "map": {"version": 3, "names": ["API_BASE_URL", "process", "env", "REACT_APP_API_BASE_URL", "console", "log", "getAuthHeaders", "token", "localStorage", "getItem", "headers", "substring", "payload", "JSON", "parse", "atob", "split", "currentTime", "Math", "floor", "Date", "now", "exp", "removeItem", "Authorization", "error", "sendMessageToChatbot", "query", "sessionId", "response", "fetch", "method", "body", "stringify", "session_id", "ok", "errorData", "json", "Error", "detail", "data", "createSession", "title", "status", "getSessions", "errorText", "text", "getSessionMessages", "updateSession", "deleteSession"], "sources": ["C:/Users/<USER>/Downloads/website kp/chatbot-unand/frontend/src/api.js"], "sourcesContent": ["// Use environment variable or fallback to port 8000\nconst API_BASE_URL =\n  process.env.REACT_APP_API_BASE_URL || \"http://localhost:8000\";\n\nconsole.log(\"API: Using API_BASE_URL:\", API_BASE_URL);\nconsole.log(\n  \"API: Environment REACT_APP_API_BASE_URL:\",\n  process.env.REACT_APP_API_BASE_URL\n);\n\n// Helper function to get auth headers\nconst getAuthHeaders = () => {\n  const token = localStorage.getItem(\"access_token\");\n  const headers = {\n    \"Content-Type\": \"application/json\",\n  };\n\n  console.log(\"API: Getting auth headers...\");\n  console.log(\n    \"API: Token from localStorage:\",\n    token ? `${token.substring(0, 20)}...` : \"null\"\n  );\n\n  if (token) {\n    // Check if token is expired before using it\n    try {\n      const payload = JSON.parse(atob(token.split(\".\")[1]));\n      const currentTime = Math.floor(Date.now() / 1000);\n\n      console.log(\"API: Token payload:\", payload);\n      console.log(\"API: Current time:\", currentTime);\n      console.log(\"API: Token expires at:\", payload.exp);\n\n      // If token is expired, don't include it\n      if (payload.exp < currentTime) {\n        console.log(\"API: Token expired, not including in headers\");\n        localStorage.removeItem(\"access_token\");\n        localStorage.removeItem(\"user\");\n        return headers;\n      }\n\n      headers.Authorization = `Bearer ${token}`;\n      console.log(\"API: Added Authorization header\");\n    } catch (error) {\n      console.error(\"API: Error checking token:\", error);\n      // Remove invalid token\n      localStorage.removeItem(\"access_token\");\n      localStorage.removeItem(\"user\");\n    }\n  } else {\n    console.log(\"API: No token found in localStorage\");\n  }\n\n  console.log(\"API: Final headers:\", headers);\n  return headers;\n};\n\nexport const sendMessageToChatbot = async (query, sessionId = null) => {\n  try {\n    const response = await fetch(`${API_BASE_URL}/chat`, {\n      method: \"POST\",\n      headers: getAuthHeaders(),\n      body: JSON.stringify({\n        query,\n        session_id: sessionId,\n      }),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.detail || \"Terjadi kesalahan pada server.\");\n    }\n\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error(\"Error sending message to chatbot:\", error);\n    throw error;\n  }\n};\n\n// Session management functions\nexport const createSession = async (title = null) => {\n  try {\n    const response = await fetch(`${API_BASE_URL}/sessions`, {\n      method: \"POST\",\n      headers: getAuthHeaders(),\n      body: JSON.stringify({ title }),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error(\"Error creating session:\", error);\n    throw error;\n  }\n};\n\nexport const getSessions = async () => {\n  try {\n    console.log(\"API: Getting sessions from\", `${API_BASE_URL}/sessions`);\n    const headers = getAuthHeaders();\n    console.log(\"API: Using headers:\", headers);\n\n    const response = await fetch(`${API_BASE_URL}/sessions`, {\n      headers: headers,\n    });\n\n    console.log(\"API: Sessions response status:\", response.status);\n\n    if (!response.ok) {\n      const errorText = await response.text();\n      console.error(\"API: Sessions error response:\", errorText);\n      throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);\n    }\n\n    const data = await response.json();\n    console.log(\"API: Sessions data received:\", data);\n    return data;\n  } catch (error) {\n    console.error(\"Error getting sessions:\", error);\n    throw error;\n  }\n};\n\nexport const getSessionMessages = async (sessionId) => {\n  try {\n    const response = await fetch(\n      `${API_BASE_URL}/sessions/${sessionId}/messages`,\n      {\n        headers: getAuthHeaders(),\n      }\n    );\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error(\"Error getting session messages:\", error);\n    throw error;\n  }\n};\n\nexport const updateSession = async (sessionId, title) => {\n  try {\n    const response = await fetch(`${API_BASE_URL}/sessions/${sessionId}`, {\n      method: \"PUT\",\n      headers: getAuthHeaders(),\n      body: JSON.stringify({ title }),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error(\"Error updating session:\", error);\n    throw error;\n  }\n};\n\nexport const deleteSession = async (sessionId) => {\n  try {\n    const response = await fetch(`${API_BASE_URL}/sessions/${sessionId}`, {\n      method: \"DELETE\",\n      headers: getAuthHeaders(),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error(\"Error deleting session:\", error);\n    throw error;\n  }\n};\n"], "mappings": "AAAA;AACA,MAAMA,YAAY,GAChBC,OAAO,CAACC,GAAG,CAACC,sBAAsB,IAAI,uBAAuB;AAE/DC,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEL,YAAY,CAAC;AACrDI,OAAO,CAACC,GAAG,CACT,0CAA0C,EAC1CJ,OAAO,CAACC,GAAG,CAACC,sBACd,CAAC;;AAED;AACA,MAAMG,cAAc,GAAGA,CAAA,KAAM;EAC3B,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;EAClD,MAAMC,OAAO,GAAG;IACd,cAAc,EAAE;EAClB,CAAC;EAEDN,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;EAC3CD,OAAO,CAACC,GAAG,CACT,+BAA+B,EAC/BE,KAAK,GAAG,GAAGA,KAAK,CAACI,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,GAAG,MAC3C,CAAC;EAED,IAAIJ,KAAK,EAAE;IACT;IACA,IAAI;MACF,MAAMK,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACC,IAAI,CAACR,KAAK,CAACS,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrD,MAAMC,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;MAEjDjB,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEO,OAAO,CAAC;MAC3CR,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEY,WAAW,CAAC;MAC9Cb,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEO,OAAO,CAACU,GAAG,CAAC;;MAElD;MACA,IAAIV,OAAO,CAACU,GAAG,GAAGL,WAAW,EAAE;QAC7Bb,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;QAC3DG,YAAY,CAACe,UAAU,CAAC,cAAc,CAAC;QACvCf,YAAY,CAACe,UAAU,CAAC,MAAM,CAAC;QAC/B,OAAOb,OAAO;MAChB;MAEAA,OAAO,CAACc,aAAa,GAAG,UAAUjB,KAAK,EAAE;MACzCH,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAChD,CAAC,CAAC,OAAOoB,KAAK,EAAE;MACdrB,OAAO,CAACqB,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD;MACAjB,YAAY,CAACe,UAAU,CAAC,cAAc,CAAC;MACvCf,YAAY,CAACe,UAAU,CAAC,MAAM,CAAC;IACjC;EACF,CAAC,MAAM;IACLnB,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;EACpD;EAEAD,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEK,OAAO,CAAC;EAC3C,OAAOA,OAAO;AAChB,CAAC;AAED,OAAO,MAAMgB,oBAAoB,GAAG,MAAAA,CAAOC,KAAK,EAAEC,SAAS,GAAG,IAAI,KAAK;EACrE,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG9B,YAAY,OAAO,EAAE;MACnD+B,MAAM,EAAE,MAAM;MACdrB,OAAO,EAAEJ,cAAc,CAAC,CAAC;MACzB0B,IAAI,EAAEnB,IAAI,CAACoB,SAAS,CAAC;QACnBN,KAAK;QACLO,UAAU,EAAEN;MACd,CAAC;IACH,CAAC,CAAC;IAEF,IAAI,CAACC,QAAQ,CAACM,EAAE,EAAE;MAChB,MAAMC,SAAS,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;MACvC,MAAM,IAAIC,KAAK,CAACF,SAAS,CAACG,MAAM,IAAI,gCAAgC,CAAC;IACvE;IAEA,MAAMC,IAAI,GAAG,MAAMX,QAAQ,CAACQ,IAAI,CAAC,CAAC;IAClC,OAAOG,IAAI;EACb,CAAC,CAAC,OAAOf,KAAK,EAAE;IACdrB,OAAO,CAACqB,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;IACzD,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMgB,aAAa,GAAG,MAAAA,CAAOC,KAAK,GAAG,IAAI,KAAK;EACnD,IAAI;IACF,MAAMb,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG9B,YAAY,WAAW,EAAE;MACvD+B,MAAM,EAAE,MAAM;MACdrB,OAAO,EAAEJ,cAAc,CAAC,CAAC;MACzB0B,IAAI,EAAEnB,IAAI,CAACoB,SAAS,CAAC;QAAES;MAAM,CAAC;IAChC,CAAC,CAAC;IAEF,IAAI,CAACb,QAAQ,CAACM,EAAE,EAAE;MAChB,MAAM,IAAIG,KAAK,CAAC,uBAAuBT,QAAQ,CAACc,MAAM,EAAE,CAAC;IAC3D;IAEA,OAAO,MAAMd,QAAQ,CAACQ,IAAI,CAAC,CAAC;EAC9B,CAAC,CAAC,OAAOZ,KAAK,EAAE;IACdrB,OAAO,CAACqB,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IAC/C,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMmB,WAAW,GAAG,MAAAA,CAAA,KAAY;EACrC,IAAI;IACFxC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,GAAGL,YAAY,WAAW,CAAC;IACrE,MAAMU,OAAO,GAAGJ,cAAc,CAAC,CAAC;IAChCF,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEK,OAAO,CAAC;IAE3C,MAAMmB,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG9B,YAAY,WAAW,EAAE;MACvDU,OAAO,EAAEA;IACX,CAAC,CAAC;IAEFN,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEwB,QAAQ,CAACc,MAAM,CAAC;IAE9D,IAAI,CAACd,QAAQ,CAACM,EAAE,EAAE;MAChB,MAAMU,SAAS,GAAG,MAAMhB,QAAQ,CAACiB,IAAI,CAAC,CAAC;MACvC1C,OAAO,CAACqB,KAAK,CAAC,+BAA+B,EAAEoB,SAAS,CAAC;MACzD,MAAM,IAAIP,KAAK,CAAC,uBAAuBT,QAAQ,CAACc,MAAM,MAAME,SAAS,EAAE,CAAC;IAC1E;IAEA,MAAML,IAAI,GAAG,MAAMX,QAAQ,CAACQ,IAAI,CAAC,CAAC;IAClCjC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEmC,IAAI,CAAC;IACjD,OAAOA,IAAI;EACb,CAAC,CAAC,OAAOf,KAAK,EAAE;IACdrB,OAAO,CAACqB,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IAC/C,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMsB,kBAAkB,GAAG,MAAOnB,SAAS,IAAK;EACrD,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAC1B,GAAG9B,YAAY,aAAa4B,SAAS,WAAW,EAChD;MACElB,OAAO,EAAEJ,cAAc,CAAC;IAC1B,CACF,CAAC;IAED,IAAI,CAACuB,QAAQ,CAACM,EAAE,EAAE;MAChB,MAAM,IAAIG,KAAK,CAAC,uBAAuBT,QAAQ,CAACc,MAAM,EAAE,CAAC;IAC3D;IAEA,OAAO,MAAMd,QAAQ,CAACQ,IAAI,CAAC,CAAC;EAC9B,CAAC,CAAC,OAAOZ,KAAK,EAAE;IACdrB,OAAO,CAACqB,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACvD,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMuB,aAAa,GAAG,MAAAA,CAAOpB,SAAS,EAAEc,KAAK,KAAK;EACvD,IAAI;IACF,MAAMb,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG9B,YAAY,aAAa4B,SAAS,EAAE,EAAE;MACpEG,MAAM,EAAE,KAAK;MACbrB,OAAO,EAAEJ,cAAc,CAAC,CAAC;MACzB0B,IAAI,EAAEnB,IAAI,CAACoB,SAAS,CAAC;QAAES;MAAM,CAAC;IAChC,CAAC,CAAC;IAEF,IAAI,CAACb,QAAQ,CAACM,EAAE,EAAE;MAChB,MAAM,IAAIG,KAAK,CAAC,uBAAuBT,QAAQ,CAACc,MAAM,EAAE,CAAC;IAC3D;IAEA,OAAO,MAAMd,QAAQ,CAACQ,IAAI,CAAC,CAAC;EAC9B,CAAC,CAAC,OAAOZ,KAAK,EAAE;IACdrB,OAAO,CAACqB,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IAC/C,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMwB,aAAa,GAAG,MAAOrB,SAAS,IAAK;EAChD,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG9B,YAAY,aAAa4B,SAAS,EAAE,EAAE;MACpEG,MAAM,EAAE,QAAQ;MAChBrB,OAAO,EAAEJ,cAAc,CAAC;IAC1B,CAAC,CAAC;IAEF,IAAI,CAACuB,QAAQ,CAACM,EAAE,EAAE;MAChB,MAAM,IAAIG,KAAK,CAAC,uBAAuBT,QAAQ,CAACc,MAAM,EAAE,CAAC;IAC3D;IAEA,OAAO,MAAMd,QAAQ,CAACQ,IAAI,CAAC,CAAC;EAC9B,CAAC,CAAC,OAAOZ,KAAK,EAAE;IACdrB,OAAO,CAACqB,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IAC/C,MAAMA,KAAK;EACb;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}