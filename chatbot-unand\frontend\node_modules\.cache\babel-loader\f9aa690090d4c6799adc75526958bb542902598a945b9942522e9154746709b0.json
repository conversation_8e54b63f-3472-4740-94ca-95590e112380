{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website kp\\\\chatbot-unand\\\\frontend\\\\src\\\\contexts\\\\AuthContext.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from \"react\";\nimport { jwtDecode } from \"jwt-decode\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error(\"useAuth must be used within an AuthProvider\");\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [token, setToken] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Admin state\n  const [adminUser, setAdminUser] = useState(null);\n  const [adminToken, setAdminToken] = useState(null);\n\n  // Session type tracking\n  const [sessionType, setSessionType] = useState(null); // 'user' | 'admin' | null\n\n  // Check for existing token on mount\n  useEffect(() => {\n    const savedToken = localStorage.getItem(\"access_token\");\n    const savedUser = localStorage.getItem(\"user\");\n    const savedAdminToken = localStorage.getItem(\"admin_token\");\n    const savedAdminUser = localStorage.getItem(\"admin_user\");\n\n    // Allow dual sessions - both user and admin can be logged in simultaneously\n    console.log(\"AuthContext: Loading existing sessions\", {\n      hasUserSession: !!savedToken,\n      hasAdminSession: !!savedAdminToken\n    });\n    if (savedToken && savedUser) {\n      try {\n        // Check if token is still valid\n        const decoded = jwtDecode(savedToken);\n        const currentTime = Date.now() / 1000;\n        if (decoded.exp > currentTime) {\n          setToken(savedToken);\n          setUser(JSON.parse(savedUser));\n        } else {\n          // Token expired, clear storage\n          localStorage.removeItem(\"access_token\");\n          localStorage.removeItem(\"user\");\n        }\n      } catch (error) {\n        console.error(\"Error decoding token:\", error);\n        localStorage.removeItem(\"access_token\");\n        localStorage.removeItem(\"user\");\n      }\n    }\n    if (savedAdminToken && savedAdminUser) {\n      try {\n        // Check if admin token is still valid\n        const decoded = jwtDecode(savedAdminToken);\n        const currentTime = Date.now() / 1000;\n        if (decoded.exp > currentTime) {\n          setAdminToken(savedAdminToken);\n          setAdminUser(JSON.parse(savedAdminUser));\n        } else {\n          // Admin token expired, clear storage\n          localStorage.removeItem(\"admin_token\");\n          localStorage.removeItem(\"admin_user\");\n        }\n      } catch (error) {\n        console.error(\"Error decoding admin token:\", error);\n        localStorage.removeItem(\"admin_token\");\n        localStorage.removeItem(\"admin_user\");\n      }\n    }\n    setLoading(false);\n  }, []);\n  const login = async googleToken => {\n    try {\n      console.log(\"AuthContext: Starting user login process...\");\n\n      // Clear only user session data, keep admin session intact\n      localStorage.removeItem(\"access_token\");\n      localStorage.removeItem(\"user\");\n      setToken(null);\n      setUser(null);\n      const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || \"http://localhost:8000\";\n      console.log(\"AuthContext: Using API URL:\", API_BASE_URL);\n      const response = await fetch(`${API_BASE_URL}/auth/google`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          token: googleToken\n        })\n      });\n      console.log(\"AuthContext: Response status:\", response.status);\n      console.log(\"AuthContext: Response headers:\", response.headers);\n      if (!response.ok) {\n        const errorText = await response.text();\n        console.error(\"AuthContext: Response error:\", errorText);\n        console.error(\"AuthContext: Full response:\", response);\n        console.error(\"AuthContext: Response status:\", response.status);\n        console.error(\"AuthContext: Response statusText:\", response.statusText);\n\n        // Try to parse error as JSON if possible\n        let errorJson = null;\n        try {\n          errorJson = JSON.parse(errorText);\n          console.error(\"AuthContext: Parsed error JSON:\", errorJson);\n        } catch (e) {\n          console.error(\"AuthContext: Error text is not JSON:\", errorText);\n        }\n\n        // Handle specific token timing errors\n        if (errorJson && errorJson.detail && errorJson.detail.includes(\"Token used too early\")) {\n          console.log(\"AuthContext: Token timing issue detected, retrying in 2 seconds...\");\n          await new Promise(resolve => setTimeout(resolve, 2000));\n\n          // Retry the request\n          const retryResponse = await fetch(`${API_BASE_URL}/auth/google`, {\n            method: \"POST\",\n            headers: {\n              \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n              token: googleToken\n            })\n          });\n          if (retryResponse.ok) {\n            const retryData = await retryResponse.json();\n            console.log(\"AuthContext: Retry authentication successful\");\n\n            // Save to localStorage\n            localStorage.setItem(\"access_token\", retryData.access_token);\n            localStorage.setItem(\"user\", JSON.stringify(retryData.user));\n\n            // Update state\n            setToken(retryData.access_token);\n            setUser(retryData.user);\n            console.log(\"AuthContext: Login completed successfully after retry\");\n            return retryData;\n          }\n        }\n        throw new Error(`Authentication failed: ${response.status} - ${errorText}`);\n      }\n      const data = await response.json();\n      console.log(\"AuthContext: Response data:\", data);\n\n      // Save to localStorage\n      localStorage.setItem(\"access_token\", data.access_token);\n      localStorage.setItem(\"user\", JSON.stringify(data.user));\n\n      // Update state\n      setToken(data.access_token);\n      setUser(data.user);\n      console.log(\"AuthContext: Login completed successfully\");\n      return data;\n    } catch (error) {\n      console.error(\"AuthContext: Login error:\", error);\n      // Clear any partial authentication data on error\n      forceLogout();\n      throw error;\n    }\n  };\n  const logout = async () => {\n    console.log(\"AuthContext: Starting logout process...\");\n    try {\n      // Call backend logout endpoint if user is authenticated\n      if (token) {\n        const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || \"http://localhost:8000\";\n        console.log(\"AuthContext: Calling backend logout endpoint...\");\n        const response = await fetch(`${API_BASE_URL}/auth/logout`, {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${token}`\n          }\n        });\n        if (response.ok) {\n          const data = await response.json();\n          console.log(\"AuthContext: Backend logout successful:\", data);\n          console.log(`AuthContext: Deactivated ${data.sessions_deactivated} sessions`);\n        } else {\n          console.warn(\"AuthContext: Backend logout failed, but continuing with frontend logout\");\n        }\n      }\n    } catch (error) {\n      console.error(\"AuthContext: Error calling backend logout:\", error);\n      console.log(\"AuthContext: Continuing with frontend logout despite backend error\");\n    }\n\n    // Clear localStorage\n    localStorage.removeItem(\"access_token\");\n    localStorage.removeItem(\"user\");\n\n    // Clear state\n    setToken(null);\n    setUser(null);\n\n    // Sign out from Google more thoroughly\n    if (window.google && window.google.accounts) {\n      console.log(\"AuthContext: Signing out from Google...\");\n      try {\n        // Disable auto-select\n        window.google.accounts.id.disableAutoSelect();\n\n        // Revoke the current session\n        window.google.accounts.id.revoke(token, done => {\n          console.log(\"AuthContext: Google token revoked:\", done);\n        });\n\n        // Clear any Google session cookies\n        document.cookie = \"g_state=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.google.com;\";\n        document.cookie = \"g_csrf_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.google.com;\";\n      } catch (error) {\n        console.warn(\"AuthContext: Error during Google sign-out:\", error);\n      }\n    }\n    console.log(\"AuthContext: Logout completed\");\n  };\n  const forceLogout = () => {\n    console.log(\"AuthContext: Force logout - clearing all authentication data\");\n\n    // Clear localStorage immediately\n    localStorage.removeItem(\"access_token\");\n    localStorage.removeItem(\"user\");\n\n    // Clear state\n    setToken(null);\n    setUser(null);\n\n    // Clear Google session\n    if (window.google && window.google.accounts) {\n      try {\n        window.google.accounts.id.disableAutoSelect();\n      } catch (error) {\n        console.warn(\"AuthContext: Error during force Google sign-out:\", error);\n      }\n    }\n    console.log(\"AuthContext: Force logout completed\");\n  };\n  const isTokenExpired = token => {\n    if (!token) return true;\n    try {\n      // Decode JWT token to check expiry\n      const payload = JSON.parse(atob(token.split(\".\")[1]));\n      const currentTime = Math.floor(Date.now() / 1000);\n\n      // Check if token is expired (with 30 second buffer)\n      return payload.exp < currentTime + 30;\n    } catch (error) {\n      console.error(\"AuthContext: Error checking token expiry:\", error);\n      return true; // Assume expired if can't decode\n    }\n  };\n  const getAuthHeaders = () => {\n    if (token && !isTokenExpired(token)) {\n      return {\n        Authorization: `Bearer ${token}`,\n        \"Content-Type\": \"application/json\"\n      };\n    } else if (token && isTokenExpired(token)) {\n      // Token is expired, clear it\n      console.log(\"AuthContext: Token expired, clearing authentication\");\n      logout();\n      return {\n        \"Content-Type\": \"application/json\"\n      };\n    }\n    return {\n      \"Content-Type\": \"application/json\"\n    };\n  };\n\n  // Admin functions\n  const loginAdmin = async (email, password) => {\n    try {\n      console.log(\"AuthContext: Starting admin login process...\");\n\n      // Clear only admin session data, keep user session intact\n      localStorage.removeItem(\"admin_token\");\n      localStorage.removeItem(\"admin_user\");\n      setAdminToken(null);\n      setAdminUser(null);\n      const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || \"http://localhost:8000\";\n      const response = await fetch(`${API_BASE_URL}/admin/login`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          email,\n          password\n        })\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || \"Admin login failed\");\n      }\n      const data = await response.json();\n      console.log(\"AuthContext: Admin login successful\");\n\n      // Save to localStorage\n      localStorage.setItem(\"admin_token\", data.access_token);\n      localStorage.setItem(\"admin_user\", JSON.stringify({\n        email: data.admin_email\n      }));\n\n      // Update state\n      setAdminToken(data.access_token);\n      setAdminUser({\n        email: data.admin_email\n      });\n      return data;\n    } catch (error) {\n      console.error(\"AuthContext: Admin login error:\", error);\n      throw error;\n    }\n  };\n  const logoutAdmin = () => {\n    console.log(\"AuthContext: Admin logout\");\n\n    // Clear localStorage\n    localStorage.removeItem(\"admin_token\");\n    localStorage.removeItem(\"admin_user\");\n\n    // Clear state\n    setAdminToken(null);\n    setAdminUser(null);\n  };\n  const getAdminAuthHeaders = () => {\n    if (adminToken && !isTokenExpired(adminToken)) {\n      return {\n        Authorization: `Bearer ${adminToken}`,\n        \"Content-Type\": \"application/json\"\n      };\n    } else if (adminToken && isTokenExpired(adminToken)) {\n      // Admin token is expired, clear it\n      console.log(\"AuthContext: Admin token expired, clearing authentication\");\n      logoutAdmin();\n      return {\n        \"Content-Type\": \"application/json\"\n      };\n    }\n    return {\n      \"Content-Type\": \"application/json\"\n    };\n  };\n\n  // Session separation functions\n  const clearAllSessions = () => {\n    console.log(\"AuthContext: Clearing all sessions\");\n    localStorage.clear();\n    setToken(null);\n    setUser(null);\n    setAdminToken(null);\n    setAdminUser(null);\n    setSessionType(null);\n  };\n\n  // Check if user has required session type for the route\n  const hasRequiredSession = requiredType => {\n    if (requiredType === \"user\") {\n      return !!user && !!token;\n    } else if (requiredType === \"admin\") {\n      return !!adminUser && !!adminToken;\n    }\n    return false;\n  };\n\n  // Check if user is trying to access wrong interface\n  const isWrongInterface = (currentPath, userType) => {\n    const isAdminPath = currentPath.startsWith(\"/admin\");\n    const isUserPath = !isAdminPath;\n    if (userType === \"user\" && isAdminPath) {\n      return true; // User trying to access admin interface\n    }\n    if (userType === \"admin\" && isUserPath) {\n      return true; // Admin trying to access user interface\n    }\n    return false;\n  };\n  const value = {\n    user,\n    token,\n    loading,\n    login,\n    logout,\n    forceLogout,\n    getAuthHeaders,\n    isAuthenticated: !!user,\n    // Admin functions\n    adminUser,\n    adminToken,\n    loginAdmin,\n    logoutAdmin,\n    getAdminAuthHeaders,\n    isAdminAuthenticated: !!adminUser,\n    // Session separation\n    sessionType,\n    clearAllSessions,\n    hasRequiredSession,\n    isWrongInterface\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 453,\n    columnNumber: 10\n  }, this);\n};\n_s2(AuthProvider, \"JYAnmyL7JJZ9EhcolWjsFFfqCt4=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "jwtDecode", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "token", "setToken", "loading", "setLoading", "adminUser", "setAdminUser", "adminToken", "setAdminToken", "sessionType", "setSessionType", "savedToken", "localStorage", "getItem", "savedUser", "savedAdminToken", "savedAdminUser", "console", "log", "hasUserSession", "hasAdminSession", "decoded", "currentTime", "Date", "now", "exp", "JSON", "parse", "removeItem", "error", "login", "googleToken", "API_BASE_URL", "process", "env", "REACT_APP_API_BASE_URL", "response", "fetch", "method", "headers", "body", "stringify", "status", "ok", "errorText", "text", "statusText", "<PERSON><PERSON><PERSON>", "e", "detail", "includes", "Promise", "resolve", "setTimeout", "retryResponse", "retryData", "json", "setItem", "access_token", "data", "forceLogout", "logout", "Authorization", "sessions_deactivated", "warn", "window", "google", "accounts", "id", "disableAutoSelect", "revoke", "done", "document", "cookie", "isTokenExpired", "payload", "atob", "split", "Math", "floor", "getAuthHeaders", "loginAdmin", "email", "password", "errorData", "admin_email", "logoutAdmin", "getAdminAuthHeaders", "clearAllSessions", "clear", "hasRequiredSession", "requiredType", "isWrongInterface", "currentPath", "userType", "isAdminPath", "startsWith", "isUserPath", "value", "isAuthenticated", "isAdminAuthenticated", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/website kp/chatbot-unand/frontend/src/contexts/AuthContext.jsx"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from \"react\";\nimport { jwtDecode } from \"jwt-decode\";\n\nconst AuthContext = createContext();\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error(\"useAuth must be used within an AuthProvider\");\n  }\n  return context;\n};\n\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [token, setToken] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Admin state\n  const [adminUser, setAdminUser] = useState(null);\n  const [adminToken, setAdminToken] = useState(null);\n\n  // Session type tracking\n  const [sessionType, setSessionType] = useState(null); // 'user' | 'admin' | null\n\n  // Check for existing token on mount\n  useEffect(() => {\n    const savedToken = localStorage.getItem(\"access_token\");\n    const savedUser = localStorage.getItem(\"user\");\n    const savedAdminToken = localStorage.getItem(\"admin_token\");\n    const savedAdminUser = localStorage.getItem(\"admin_user\");\n\n    // Allow dual sessions - both user and admin can be logged in simultaneously\n    console.log(\"AuthContext: Loading existing sessions\", {\n      hasUserSession: !!savedToken,\n      hasAdminSession: !!savedAdminToken,\n    });\n\n    if (savedToken && savedUser) {\n      try {\n        // Check if token is still valid\n        const decoded = jwtDecode(savedToken);\n        const currentTime = Date.now() / 1000;\n\n        if (decoded.exp > currentTime) {\n          setToken(savedToken);\n          setUser(JSON.parse(savedUser));\n        } else {\n          // Token expired, clear storage\n          localStorage.removeItem(\"access_token\");\n          localStorage.removeItem(\"user\");\n        }\n      } catch (error) {\n        console.error(\"Error decoding token:\", error);\n        localStorage.removeItem(\"access_token\");\n        localStorage.removeItem(\"user\");\n      }\n    }\n\n    if (savedAdminToken && savedAdminUser) {\n      try {\n        // Check if admin token is still valid\n        const decoded = jwtDecode(savedAdminToken);\n        const currentTime = Date.now() / 1000;\n\n        if (decoded.exp > currentTime) {\n          setAdminToken(savedAdminToken);\n          setAdminUser(JSON.parse(savedAdminUser));\n        } else {\n          // Admin token expired, clear storage\n          localStorage.removeItem(\"admin_token\");\n          localStorage.removeItem(\"admin_user\");\n        }\n      } catch (error) {\n        console.error(\"Error decoding admin token:\", error);\n        localStorage.removeItem(\"admin_token\");\n        localStorage.removeItem(\"admin_user\");\n      }\n    }\n\n    setLoading(false);\n  }, []);\n\n  const login = async (googleToken) => {\n    try {\n      console.log(\"AuthContext: Starting user login process...\");\n\n      // Clear only user session data, keep admin session intact\n      localStorage.removeItem(\"access_token\");\n      localStorage.removeItem(\"user\");\n      setToken(null);\n      setUser(null);\n\n      const API_BASE_URL =\n        process.env.REACT_APP_API_BASE_URL || \"http://localhost:8000\";\n      console.log(\"AuthContext: Using API URL:\", API_BASE_URL);\n      const response = await fetch(`${API_BASE_URL}/auth/google`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify({ token: googleToken }),\n      });\n\n      console.log(\"AuthContext: Response status:\", response.status);\n      console.log(\"AuthContext: Response headers:\", response.headers);\n\n      if (!response.ok) {\n        const errorText = await response.text();\n        console.error(\"AuthContext: Response error:\", errorText);\n        console.error(\"AuthContext: Full response:\", response);\n        console.error(\"AuthContext: Response status:\", response.status);\n        console.error(\"AuthContext: Response statusText:\", response.statusText);\n\n        // Try to parse error as JSON if possible\n        let errorJson = null;\n        try {\n          errorJson = JSON.parse(errorText);\n          console.error(\"AuthContext: Parsed error JSON:\", errorJson);\n        } catch (e) {\n          console.error(\"AuthContext: Error text is not JSON:\", errorText);\n        }\n\n        // Handle specific token timing errors\n        if (\n          errorJson &&\n          errorJson.detail &&\n          errorJson.detail.includes(\"Token used too early\")\n        ) {\n          console.log(\n            \"AuthContext: Token timing issue detected, retrying in 2 seconds...\"\n          );\n          await new Promise((resolve) => setTimeout(resolve, 2000));\n\n          // Retry the request\n          const retryResponse = await fetch(`${API_BASE_URL}/auth/google`, {\n            method: \"POST\",\n            headers: {\n              \"Content-Type\": \"application/json\",\n            },\n            body: JSON.stringify({ token: googleToken }),\n          });\n\n          if (retryResponse.ok) {\n            const retryData = await retryResponse.json();\n            console.log(\"AuthContext: Retry authentication successful\");\n\n            // Save to localStorage\n            localStorage.setItem(\"access_token\", retryData.access_token);\n            localStorage.setItem(\"user\", JSON.stringify(retryData.user));\n\n            // Update state\n            setToken(retryData.access_token);\n            setUser(retryData.user);\n\n            console.log(\n              \"AuthContext: Login completed successfully after retry\"\n            );\n            return retryData;\n          }\n        }\n\n        throw new Error(\n          `Authentication failed: ${response.status} - ${errorText}`\n        );\n      }\n\n      const data = await response.json();\n      console.log(\"AuthContext: Response data:\", data);\n\n      // Save to localStorage\n      localStorage.setItem(\"access_token\", data.access_token);\n      localStorage.setItem(\"user\", JSON.stringify(data.user));\n\n      // Update state\n      setToken(data.access_token);\n      setUser(data.user);\n\n      console.log(\"AuthContext: Login completed successfully\");\n      return data;\n    } catch (error) {\n      console.error(\"AuthContext: Login error:\", error);\n      // Clear any partial authentication data on error\n      forceLogout();\n      throw error;\n    }\n  };\n\n  const logout = async () => {\n    console.log(\"AuthContext: Starting logout process...\");\n\n    try {\n      // Call backend logout endpoint if user is authenticated\n      if (token) {\n        const API_BASE_URL =\n          process.env.REACT_APP_API_BASE_URL || \"http://localhost:8000\";\n        console.log(\"AuthContext: Calling backend logout endpoint...\");\n\n        const response = await fetch(`${API_BASE_URL}/auth/logout`, {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${token}`,\n          },\n        });\n\n        if (response.ok) {\n          const data = await response.json();\n          console.log(\"AuthContext: Backend logout successful:\", data);\n          console.log(\n            `AuthContext: Deactivated ${data.sessions_deactivated} sessions`\n          );\n        } else {\n          console.warn(\n            \"AuthContext: Backend logout failed, but continuing with frontend logout\"\n          );\n        }\n      }\n    } catch (error) {\n      console.error(\"AuthContext: Error calling backend logout:\", error);\n      console.log(\n        \"AuthContext: Continuing with frontend logout despite backend error\"\n      );\n    }\n\n    // Clear localStorage\n    localStorage.removeItem(\"access_token\");\n    localStorage.removeItem(\"user\");\n\n    // Clear state\n    setToken(null);\n    setUser(null);\n\n    // Sign out from Google more thoroughly\n    if (window.google && window.google.accounts) {\n      console.log(\"AuthContext: Signing out from Google...\");\n      try {\n        // Disable auto-select\n        window.google.accounts.id.disableAutoSelect();\n\n        // Revoke the current session\n        window.google.accounts.id.revoke(token, (done) => {\n          console.log(\"AuthContext: Google token revoked:\", done);\n        });\n\n        // Clear any Google session cookies\n        document.cookie =\n          \"g_state=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.google.com;\";\n        document.cookie =\n          \"g_csrf_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.google.com;\";\n      } catch (error) {\n        console.warn(\"AuthContext: Error during Google sign-out:\", error);\n      }\n    }\n\n    console.log(\"AuthContext: Logout completed\");\n  };\n\n  const forceLogout = () => {\n    console.log(\"AuthContext: Force logout - clearing all authentication data\");\n\n    // Clear localStorage immediately\n    localStorage.removeItem(\"access_token\");\n    localStorage.removeItem(\"user\");\n\n    // Clear state\n    setToken(null);\n    setUser(null);\n\n    // Clear Google session\n    if (window.google && window.google.accounts) {\n      try {\n        window.google.accounts.id.disableAutoSelect();\n      } catch (error) {\n        console.warn(\"AuthContext: Error during force Google sign-out:\", error);\n      }\n    }\n\n    console.log(\"AuthContext: Force logout completed\");\n  };\n\n  const isTokenExpired = (token) => {\n    if (!token) return true;\n\n    try {\n      // Decode JWT token to check expiry\n      const payload = JSON.parse(atob(token.split(\".\")[1]));\n      const currentTime = Math.floor(Date.now() / 1000);\n\n      // Check if token is expired (with 30 second buffer)\n      return payload.exp < currentTime + 30;\n    } catch (error) {\n      console.error(\"AuthContext: Error checking token expiry:\", error);\n      return true; // Assume expired if can't decode\n    }\n  };\n\n  const getAuthHeaders = () => {\n    if (token && !isTokenExpired(token)) {\n      return {\n        Authorization: `Bearer ${token}`,\n        \"Content-Type\": \"application/json\",\n      };\n    } else if (token && isTokenExpired(token)) {\n      // Token is expired, clear it\n      console.log(\"AuthContext: Token expired, clearing authentication\");\n      logout();\n      return {\n        \"Content-Type\": \"application/json\",\n      };\n    }\n    return {\n      \"Content-Type\": \"application/json\",\n    };\n  };\n\n  // Admin functions\n  const loginAdmin = async (email, password) => {\n    try {\n      console.log(\"AuthContext: Starting admin login process...\");\n\n      // Clear only admin session data, keep user session intact\n      localStorage.removeItem(\"admin_token\");\n      localStorage.removeItem(\"admin_user\");\n      setAdminToken(null);\n      setAdminUser(null);\n\n      const API_BASE_URL =\n        process.env.REACT_APP_API_BASE_URL || \"http://localhost:8000\";\n      const response = await fetch(`${API_BASE_URL}/admin/login`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify({ email, password }),\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || \"Admin login failed\");\n      }\n\n      const data = await response.json();\n      console.log(\"AuthContext: Admin login successful\");\n\n      // Save to localStorage\n      localStorage.setItem(\"admin_token\", data.access_token);\n      localStorage.setItem(\n        \"admin_user\",\n        JSON.stringify({ email: data.admin_email })\n      );\n\n      // Update state\n      setAdminToken(data.access_token);\n      setAdminUser({ email: data.admin_email });\n\n      return data;\n    } catch (error) {\n      console.error(\"AuthContext: Admin login error:\", error);\n      throw error;\n    }\n  };\n\n  const logoutAdmin = () => {\n    console.log(\"AuthContext: Admin logout\");\n\n    // Clear localStorage\n    localStorage.removeItem(\"admin_token\");\n    localStorage.removeItem(\"admin_user\");\n\n    // Clear state\n    setAdminToken(null);\n    setAdminUser(null);\n  };\n\n  const getAdminAuthHeaders = () => {\n    if (adminToken && !isTokenExpired(adminToken)) {\n      return {\n        Authorization: `Bearer ${adminToken}`,\n        \"Content-Type\": \"application/json\",\n      };\n    } else if (adminToken && isTokenExpired(adminToken)) {\n      // Admin token is expired, clear it\n      console.log(\"AuthContext: Admin token expired, clearing authentication\");\n      logoutAdmin();\n      return {\n        \"Content-Type\": \"application/json\",\n      };\n    }\n    return {\n      \"Content-Type\": \"application/json\",\n    };\n  };\n\n  // Session separation functions\n  const clearAllSessions = () => {\n    console.log(\"AuthContext: Clearing all sessions\");\n    localStorage.clear();\n    setToken(null);\n    setUser(null);\n    setAdminToken(null);\n    setAdminUser(null);\n    setSessionType(null);\n  };\n\n  // Check if user has required session type for the route\n  const hasRequiredSession = (requiredType) => {\n    if (requiredType === \"user\") {\n      return !!user && !!token;\n    } else if (requiredType === \"admin\") {\n      return !!adminUser && !!adminToken;\n    }\n    return false;\n  };\n\n  // Check if user is trying to access wrong interface\n  const isWrongInterface = (currentPath, userType) => {\n    const isAdminPath = currentPath.startsWith(\"/admin\");\n    const isUserPath = !isAdminPath;\n\n    if (userType === \"user\" && isAdminPath) {\n      return true; // User trying to access admin interface\n    }\n    if (userType === \"admin\" && isUserPath) {\n      return true; // Admin trying to access user interface\n    }\n    return false;\n  };\n\n  const value = {\n    user,\n    token,\n    loading,\n    login,\n    logout,\n    forceLogout,\n    getAuthHeaders,\n    isAuthenticated: !!user,\n    // Admin functions\n    adminUser,\n    adminToken,\n    loginAdmin,\n    logoutAdmin,\n    getAdminAuthHeaders,\n    isAdminAuthenticated: !!adminUser,\n    // Session separation\n    sessionType,\n    clearAllSessions,\n    hasRequiredSession,\n    isWrongInterface,\n  };\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,SAASC,SAAS,QAAQ,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,WAAW,gBAAGP,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMQ,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGT,UAAU,CAACM,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACe,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACA,MAAM,CAACmB,SAAS,EAAEC,YAAY,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACqB,UAAU,EAAEC,aAAa,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACA,MAAM,CAACuB,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;;EAEtD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMwB,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;IACvD,MAAMC,SAAS,GAAGF,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IAC9C,MAAME,eAAe,GAAGH,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IAC3D,MAAMG,cAAc,GAAGJ,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;;IAEzD;IACAI,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE;MACpDC,cAAc,EAAE,CAAC,CAACR,UAAU;MAC5BS,eAAe,EAAE,CAAC,CAACL;IACrB,CAAC,CAAC;IAEF,IAAIJ,UAAU,IAAIG,SAAS,EAAE;MAC3B,IAAI;QACF;QACA,MAAMO,OAAO,GAAGjC,SAAS,CAACuB,UAAU,CAAC;QACrC,MAAMW,WAAW,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI;QAErC,IAAIH,OAAO,CAACI,GAAG,GAAGH,WAAW,EAAE;UAC7BpB,QAAQ,CAACS,UAAU,CAAC;UACpBX,OAAO,CAAC0B,IAAI,CAACC,KAAK,CAACb,SAAS,CAAC,CAAC;QAChC,CAAC,MAAM;UACL;UACAF,YAAY,CAACgB,UAAU,CAAC,cAAc,CAAC;UACvChB,YAAY,CAACgB,UAAU,CAAC,MAAM,CAAC;QACjC;MACF,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdZ,OAAO,CAACY,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7CjB,YAAY,CAACgB,UAAU,CAAC,cAAc,CAAC;QACvChB,YAAY,CAACgB,UAAU,CAAC,MAAM,CAAC;MACjC;IACF;IAEA,IAAIb,eAAe,IAAIC,cAAc,EAAE;MACrC,IAAI;QACF;QACA,MAAMK,OAAO,GAAGjC,SAAS,CAAC2B,eAAe,CAAC;QAC1C,MAAMO,WAAW,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI;QAErC,IAAIH,OAAO,CAACI,GAAG,GAAGH,WAAW,EAAE;UAC7Bd,aAAa,CAACO,eAAe,CAAC;UAC9BT,YAAY,CAACoB,IAAI,CAACC,KAAK,CAACX,cAAc,CAAC,CAAC;QAC1C,CAAC,MAAM;UACL;UACAJ,YAAY,CAACgB,UAAU,CAAC,aAAa,CAAC;UACtChB,YAAY,CAACgB,UAAU,CAAC,YAAY,CAAC;QACvC;MACF,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdZ,OAAO,CAACY,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnDjB,YAAY,CAACgB,UAAU,CAAC,aAAa,CAAC;QACtChB,YAAY,CAACgB,UAAU,CAAC,YAAY,CAAC;MACvC;IACF;IAEAxB,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM0B,KAAK,GAAG,MAAOC,WAAW,IAAK;IACnC,IAAI;MACFd,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;;MAE1D;MACAN,YAAY,CAACgB,UAAU,CAAC,cAAc,CAAC;MACvChB,YAAY,CAACgB,UAAU,CAAC,MAAM,CAAC;MAC/B1B,QAAQ,CAAC,IAAI,CAAC;MACdF,OAAO,CAAC,IAAI,CAAC;MAEb,MAAMgC,YAAY,GAChBC,OAAO,CAACC,GAAG,CAACC,sBAAsB,IAAI,uBAAuB;MAC/DlB,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEc,YAAY,CAAC;MACxD,MAAMI,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGL,YAAY,cAAc,EAAE;QAC1DM,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEd,IAAI,CAACe,SAAS,CAAC;UAAExC,KAAK,EAAE8B;QAAY,CAAC;MAC7C,CAAC,CAAC;MAEFd,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEkB,QAAQ,CAACM,MAAM,CAAC;MAC7DzB,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEkB,QAAQ,CAACG,OAAO,CAAC;MAE/D,IAAI,CAACH,QAAQ,CAACO,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAMR,QAAQ,CAACS,IAAI,CAAC,CAAC;QACvC5B,OAAO,CAACY,KAAK,CAAC,8BAA8B,EAAEe,SAAS,CAAC;QACxD3B,OAAO,CAACY,KAAK,CAAC,6BAA6B,EAAEO,QAAQ,CAAC;QACtDnB,OAAO,CAACY,KAAK,CAAC,+BAA+B,EAAEO,QAAQ,CAACM,MAAM,CAAC;QAC/DzB,OAAO,CAACY,KAAK,CAAC,mCAAmC,EAAEO,QAAQ,CAACU,UAAU,CAAC;;QAEvE;QACA,IAAIC,SAAS,GAAG,IAAI;QACpB,IAAI;UACFA,SAAS,GAAGrB,IAAI,CAACC,KAAK,CAACiB,SAAS,CAAC;UACjC3B,OAAO,CAACY,KAAK,CAAC,iCAAiC,EAAEkB,SAAS,CAAC;QAC7D,CAAC,CAAC,OAAOC,CAAC,EAAE;UACV/B,OAAO,CAACY,KAAK,CAAC,sCAAsC,EAAEe,SAAS,CAAC;QAClE;;QAEA;QACA,IACEG,SAAS,IACTA,SAAS,CAACE,MAAM,IAChBF,SAAS,CAACE,MAAM,CAACC,QAAQ,CAAC,sBAAsB,CAAC,EACjD;UACAjC,OAAO,CAACC,GAAG,CACT,oEACF,CAAC;UACD,MAAM,IAAIiC,OAAO,CAAEC,OAAO,IAAKC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;UAEzD;UACA,MAAME,aAAa,GAAG,MAAMjB,KAAK,CAAC,GAAGL,YAAY,cAAc,EAAE;YAC/DM,MAAM,EAAE,MAAM;YACdC,OAAO,EAAE;cACP,cAAc,EAAE;YAClB,CAAC;YACDC,IAAI,EAAEd,IAAI,CAACe,SAAS,CAAC;cAAExC,KAAK,EAAE8B;YAAY,CAAC;UAC7C,CAAC,CAAC;UAEF,IAAIuB,aAAa,CAACX,EAAE,EAAE;YACpB,MAAMY,SAAS,GAAG,MAAMD,aAAa,CAACE,IAAI,CAAC,CAAC;YAC5CvC,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;;YAE3D;YACAN,YAAY,CAAC6C,OAAO,CAAC,cAAc,EAAEF,SAAS,CAACG,YAAY,CAAC;YAC5D9C,YAAY,CAAC6C,OAAO,CAAC,MAAM,EAAE/B,IAAI,CAACe,SAAS,CAACc,SAAS,CAACxD,IAAI,CAAC,CAAC;;YAE5D;YACAG,QAAQ,CAACqD,SAAS,CAACG,YAAY,CAAC;YAChC1D,OAAO,CAACuD,SAAS,CAACxD,IAAI,CAAC;YAEvBkB,OAAO,CAACC,GAAG,CACT,uDACF,CAAC;YACD,OAAOqC,SAAS;UAClB;QACF;QAEA,MAAM,IAAI5D,KAAK,CACb,0BAA0ByC,QAAQ,CAACM,MAAM,MAAME,SAAS,EAC1D,CAAC;MACH;MAEA,MAAMe,IAAI,GAAG,MAAMvB,QAAQ,CAACoB,IAAI,CAAC,CAAC;MAClCvC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEyC,IAAI,CAAC;;MAEhD;MACA/C,YAAY,CAAC6C,OAAO,CAAC,cAAc,EAAEE,IAAI,CAACD,YAAY,CAAC;MACvD9C,YAAY,CAAC6C,OAAO,CAAC,MAAM,EAAE/B,IAAI,CAACe,SAAS,CAACkB,IAAI,CAAC5D,IAAI,CAAC,CAAC;;MAEvD;MACAG,QAAQ,CAACyD,IAAI,CAACD,YAAY,CAAC;MAC3B1D,OAAO,CAAC2D,IAAI,CAAC5D,IAAI,CAAC;MAElBkB,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;MACxD,OAAOyC,IAAI;IACb,CAAC,CAAC,OAAO9B,KAAK,EAAE;MACdZ,OAAO,CAACY,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD;MACA+B,WAAW,CAAC,CAAC;MACb,MAAM/B,KAAK;IACb;EACF,CAAC;EAED,MAAMgC,MAAM,GAAG,MAAAA,CAAA,KAAY;IACzB5C,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;IAEtD,IAAI;MACF;MACA,IAAIjB,KAAK,EAAE;QACT,MAAM+B,YAAY,GAChBC,OAAO,CAACC,GAAG,CAACC,sBAAsB,IAAI,uBAAuB;QAC/DlB,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;QAE9D,MAAMkB,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGL,YAAY,cAAc,EAAE;UAC1DM,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClCuB,aAAa,EAAE,UAAU7D,KAAK;UAChC;QACF,CAAC,CAAC;QAEF,IAAImC,QAAQ,CAACO,EAAE,EAAE;UACf,MAAMgB,IAAI,GAAG,MAAMvB,QAAQ,CAACoB,IAAI,CAAC,CAAC;UAClCvC,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEyC,IAAI,CAAC;UAC5D1C,OAAO,CAACC,GAAG,CACT,4BAA4ByC,IAAI,CAACI,oBAAoB,WACvD,CAAC;QACH,CAAC,MAAM;UACL9C,OAAO,CAAC+C,IAAI,CACV,yEACF,CAAC;QACH;MACF;IACF,CAAC,CAAC,OAAOnC,KAAK,EAAE;MACdZ,OAAO,CAACY,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;MAClEZ,OAAO,CAACC,GAAG,CACT,oEACF,CAAC;IACH;;IAEA;IACAN,YAAY,CAACgB,UAAU,CAAC,cAAc,CAAC;IACvChB,YAAY,CAACgB,UAAU,CAAC,MAAM,CAAC;;IAE/B;IACA1B,QAAQ,CAAC,IAAI,CAAC;IACdF,OAAO,CAAC,IAAI,CAAC;;IAEb;IACA,IAAIiE,MAAM,CAACC,MAAM,IAAID,MAAM,CAACC,MAAM,CAACC,QAAQ,EAAE;MAC3ClD,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;MACtD,IAAI;QACF;QACA+C,MAAM,CAACC,MAAM,CAACC,QAAQ,CAACC,EAAE,CAACC,iBAAiB,CAAC,CAAC;;QAE7C;QACAJ,MAAM,CAACC,MAAM,CAACC,QAAQ,CAACC,EAAE,CAACE,MAAM,CAACrE,KAAK,EAAGsE,IAAI,IAAK;UAChDtD,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEqD,IAAI,CAAC;QACzD,CAAC,CAAC;;QAEF;QACAC,QAAQ,CAACC,MAAM,GACb,8EAA8E;QAChFD,QAAQ,CAACC,MAAM,GACb,mFAAmF;MACvF,CAAC,CAAC,OAAO5C,KAAK,EAAE;QACdZ,OAAO,CAAC+C,IAAI,CAAC,4CAA4C,EAAEnC,KAAK,CAAC;MACnE;IACF;IAEAZ,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;EAC9C,CAAC;EAED,MAAM0C,WAAW,GAAGA,CAAA,KAAM;IACxB3C,OAAO,CAACC,GAAG,CAAC,8DAA8D,CAAC;;IAE3E;IACAN,YAAY,CAACgB,UAAU,CAAC,cAAc,CAAC;IACvChB,YAAY,CAACgB,UAAU,CAAC,MAAM,CAAC;;IAE/B;IACA1B,QAAQ,CAAC,IAAI,CAAC;IACdF,OAAO,CAAC,IAAI,CAAC;;IAEb;IACA,IAAIiE,MAAM,CAACC,MAAM,IAAID,MAAM,CAACC,MAAM,CAACC,QAAQ,EAAE;MAC3C,IAAI;QACFF,MAAM,CAACC,MAAM,CAACC,QAAQ,CAACC,EAAE,CAACC,iBAAiB,CAAC,CAAC;MAC/C,CAAC,CAAC,OAAOxC,KAAK,EAAE;QACdZ,OAAO,CAAC+C,IAAI,CAAC,kDAAkD,EAAEnC,KAAK,CAAC;MACzE;IACF;IAEAZ,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;EACpD,CAAC;EAED,MAAMwD,cAAc,GAAIzE,KAAK,IAAK;IAChC,IAAI,CAACA,KAAK,EAAE,OAAO,IAAI;IAEvB,IAAI;MACF;MACA,MAAM0E,OAAO,GAAGjD,IAAI,CAACC,KAAK,CAACiD,IAAI,CAAC3E,KAAK,CAAC4E,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrD,MAAMvD,WAAW,GAAGwD,IAAI,CAACC,KAAK,CAACxD,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;;MAEjD;MACA,OAAOmD,OAAO,CAAClD,GAAG,GAAGH,WAAW,GAAG,EAAE;IACvC,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdZ,OAAO,CAACY,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACjE,OAAO,IAAI,CAAC,CAAC;IACf;EACF,CAAC;EAED,MAAMmD,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI/E,KAAK,IAAI,CAACyE,cAAc,CAACzE,KAAK,CAAC,EAAE;MACnC,OAAO;QACL6D,aAAa,EAAE,UAAU7D,KAAK,EAAE;QAChC,cAAc,EAAE;MAClB,CAAC;IACH,CAAC,MAAM,IAAIA,KAAK,IAAIyE,cAAc,CAACzE,KAAK,CAAC,EAAE;MACzC;MACAgB,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;MAClE2C,MAAM,CAAC,CAAC;MACR,OAAO;QACL,cAAc,EAAE;MAClB,CAAC;IACH;IACA,OAAO;MACL,cAAc,EAAE;IAClB,CAAC;EACH,CAAC;;EAED;EACA,MAAMoB,UAAU,GAAG,MAAAA,CAAOC,KAAK,EAAEC,QAAQ,KAAK;IAC5C,IAAI;MACFlE,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;;MAE3D;MACAN,YAAY,CAACgB,UAAU,CAAC,aAAa,CAAC;MACtChB,YAAY,CAACgB,UAAU,CAAC,YAAY,CAAC;MACrCpB,aAAa,CAAC,IAAI,CAAC;MACnBF,YAAY,CAAC,IAAI,CAAC;MAElB,MAAM0B,YAAY,GAChBC,OAAO,CAACC,GAAG,CAACC,sBAAsB,IAAI,uBAAuB;MAC/D,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGL,YAAY,cAAc,EAAE;QAC1DM,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEd,IAAI,CAACe,SAAS,CAAC;UAAEyC,KAAK;UAAEC;QAAS,CAAC;MAC1C,CAAC,CAAC;MAEF,IAAI,CAAC/C,QAAQ,CAACO,EAAE,EAAE;QAChB,MAAMyC,SAAS,GAAG,MAAMhD,QAAQ,CAACoB,IAAI,CAAC,CAAC;QACvC,MAAM,IAAI7D,KAAK,CAACyF,SAAS,CAACnC,MAAM,IAAI,oBAAoB,CAAC;MAC3D;MAEA,MAAMU,IAAI,GAAG,MAAMvB,QAAQ,CAACoB,IAAI,CAAC,CAAC;MAClCvC,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;;MAElD;MACAN,YAAY,CAAC6C,OAAO,CAAC,aAAa,EAAEE,IAAI,CAACD,YAAY,CAAC;MACtD9C,YAAY,CAAC6C,OAAO,CAClB,YAAY,EACZ/B,IAAI,CAACe,SAAS,CAAC;QAAEyC,KAAK,EAAEvB,IAAI,CAAC0B;MAAY,CAAC,CAC5C,CAAC;;MAED;MACA7E,aAAa,CAACmD,IAAI,CAACD,YAAY,CAAC;MAChCpD,YAAY,CAAC;QAAE4E,KAAK,EAAEvB,IAAI,CAAC0B;MAAY,CAAC,CAAC;MAEzC,OAAO1B,IAAI;IACb,CAAC,CAAC,OAAO9B,KAAK,EAAE;MACdZ,OAAO,CAACY,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAMyD,WAAW,GAAGA,CAAA,KAAM;IACxBrE,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;;IAExC;IACAN,YAAY,CAACgB,UAAU,CAAC,aAAa,CAAC;IACtChB,YAAY,CAACgB,UAAU,CAAC,YAAY,CAAC;;IAErC;IACApB,aAAa,CAAC,IAAI,CAAC;IACnBF,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMiF,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAIhF,UAAU,IAAI,CAACmE,cAAc,CAACnE,UAAU,CAAC,EAAE;MAC7C,OAAO;QACLuD,aAAa,EAAE,UAAUvD,UAAU,EAAE;QACrC,cAAc,EAAE;MAClB,CAAC;IACH,CAAC,MAAM,IAAIA,UAAU,IAAImE,cAAc,CAACnE,UAAU,CAAC,EAAE;MACnD;MACAU,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;MACxEoE,WAAW,CAAC,CAAC;MACb,OAAO;QACL,cAAc,EAAE;MAClB,CAAC;IACH;IACA,OAAO;MACL,cAAc,EAAE;IAClB,CAAC;EACH,CAAC;;EAED;EACA,MAAME,gBAAgB,GAAGA,CAAA,KAAM;IAC7BvE,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;IACjDN,YAAY,CAAC6E,KAAK,CAAC,CAAC;IACpBvF,QAAQ,CAAC,IAAI,CAAC;IACdF,OAAO,CAAC,IAAI,CAAC;IACbQ,aAAa,CAAC,IAAI,CAAC;IACnBF,YAAY,CAAC,IAAI,CAAC;IAClBI,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;;EAED;EACA,MAAMgF,kBAAkB,GAAIC,YAAY,IAAK;IAC3C,IAAIA,YAAY,KAAK,MAAM,EAAE;MAC3B,OAAO,CAAC,CAAC5F,IAAI,IAAI,CAAC,CAACE,KAAK;IAC1B,CAAC,MAAM,IAAI0F,YAAY,KAAK,OAAO,EAAE;MACnC,OAAO,CAAC,CAACtF,SAAS,IAAI,CAAC,CAACE,UAAU;IACpC;IACA,OAAO,KAAK;EACd,CAAC;;EAED;EACA,MAAMqF,gBAAgB,GAAGA,CAACC,WAAW,EAAEC,QAAQ,KAAK;IAClD,MAAMC,WAAW,GAAGF,WAAW,CAACG,UAAU,CAAC,QAAQ,CAAC;IACpD,MAAMC,UAAU,GAAG,CAACF,WAAW;IAE/B,IAAID,QAAQ,KAAK,MAAM,IAAIC,WAAW,EAAE;MACtC,OAAO,IAAI,CAAC,CAAC;IACf;IACA,IAAID,QAAQ,KAAK,OAAO,IAAIG,UAAU,EAAE;MACtC,OAAO,IAAI,CAAC,CAAC;IACf;IACA,OAAO,KAAK;EACd,CAAC;EAED,MAAMC,KAAK,GAAG;IACZnG,IAAI;IACJE,KAAK;IACLE,OAAO;IACP2B,KAAK;IACL+B,MAAM;IACND,WAAW;IACXoB,cAAc;IACdmB,eAAe,EAAE,CAAC,CAACpG,IAAI;IACvB;IACAM,SAAS;IACTE,UAAU;IACV0E,UAAU;IACVK,WAAW;IACXC,mBAAmB;IACnBa,oBAAoB,EAAE,CAAC,CAAC/F,SAAS;IACjC;IACAI,WAAW;IACX+E,gBAAgB;IAChBE,kBAAkB;IAClBE;EACF,CAAC;EAED,oBAAOtG,OAAA,CAACC,WAAW,CAAC8G,QAAQ;IAACH,KAAK,EAAEA,KAAM;IAAArG,QAAA,EAAEA;EAAQ;IAAAyG,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAuB,CAAC;AAC9E,CAAC;AAAC3G,GAAA,CAxbWF,YAAY;AAAA8G,EAAA,GAAZ9G,YAAY;AAAA,IAAA8G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}