{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"6\",\n  height: \"14\",\n  x: \"2\",\n  y: \"5\",\n  rx: \"2\",\n  key: \"dy24zr\"\n}], [\"rect\", {\n  width: \"6\",\n  height: \"10\",\n  x: \"12\",\n  y: \"7\",\n  rx: \"2\",\n  key: \"1ht384\"\n}], [\"path\", {\n  d: \"M22 2v20\",\n  key: \"40qfg1\"\n}]];\nconst AlignHorizontalJustifyEnd = createLucideIcon(\"align-horizontal-justify-end\", __iconNode);\nexport { __iconNode, AlignHorizontalJustifyEnd as default };", "map": {"version": 3, "names": ["__iconNode", "width", "height", "x", "y", "rx", "key", "d", "AlignHorizontalJustifyEnd", "createLucideIcon"], "sources": ["D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\node_modules\\lucide-react\\src\\icons\\align-horizontal-justify-end.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['rect', { width: '6', height: '14', x: '2', y: '5', rx: '2', key: 'dy24zr' }],\n  ['rect', { width: '6', height: '10', x: '12', y: '7', rx: '2', key: '1ht384' }],\n  ['path', { d: 'M22 2v20', key: '40qfg1' }],\n];\n\n/**\n * @component @name AlignHorizontalJustifyEnd\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iNiIgaGVpZ2h0PSIxNCIgeD0iMiIgeT0iNSIgcng9IjIiIC8+CiAgPHJlY3Qgd2lkdGg9IjYiIGhlaWdodD0iMTAiIHg9IjEyIiB5PSI3IiByeD0iMiIgLz4KICA8cGF0aCBkPSJNMjIgMnYyMCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/align-horizontal-justify-end\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst AlignHorizontalJustifyEnd = createLucideIcon('align-horizontal-justify-end', __iconNode);\n\nexport default AlignHorizontalJustifyEnd;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,QAAQ;EAAEC,KAAA,EAAO;EAAKC,MAAQ;EAAMC,CAAG;EAAKC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7E,CAAC,QAAQ;EAAEL,KAAA,EAAO;EAAKC,MAAQ;EAAMC,CAAG;EAAMC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9E,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAD,GAAA,EAAK;AAAU,GAC3C;AAaM,MAAAE,yBAAA,GAA4BC,gBAAiB,iCAAgCT,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}