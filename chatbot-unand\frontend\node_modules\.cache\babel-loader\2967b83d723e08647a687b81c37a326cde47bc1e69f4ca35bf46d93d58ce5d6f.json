{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M8 7v7\",\n  key: \"1x2jlm\"\n}], [\"path\", {\n  d: \"M12 7v4\",\n  key: \"xawao1\"\n}], [\"path\", {\n  d: \"M16 7v9\",\n  key: \"1hp2iy\"\n}], [\"path\", {\n  d: \"M5 3a2 2 0 0 0-2 2\",\n  key: \"y57alp\"\n}], [\"path\", {\n  d: \"M9 3h1\",\n  key: \"1yesri\"\n}], [\"path\", {\n  d: \"M14 3h1\",\n  key: \"1ec4yj\"\n}], [\"path\", {\n  d: \"M19 3a2 2 0 0 1 2 2\",\n  key: \"18rm91\"\n}], [\"path\", {\n  d: \"M21 9v1\",\n  key: \"mxsmne\"\n}], [\"path\", {\n  d: \"M21 14v1\",\n  key: \"169vum\"\n}], [\"path\", {\n  d: \"M21 19a2 2 0 0 1-2 2\",\n  key: \"1j7049\"\n}], [\"path\", {\n  d: \"M14 21h1\",\n  key: \"v9vybs\"\n}], [\"path\", {\n  d: \"M9 21h1\",\n  key: \"15o7lz\"\n}], [\"path\", {\n  d: \"M5 21a2 2 0 0 1-2-2\",\n  key: \"sbafld\"\n}], [\"path\", {\n  d: \"M3 14v1\",\n  key: \"vnatye\"\n}], [\"path\", {\n  d: \"M3 9v1\",\n  key: \"1r0deq\"\n}]];\nconst SquareDashedKanban = createLucideIcon(\"square-dashed-kanban\", __iconNode);\nexport { __iconNode, SquareDashedKanban as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "SquareDashedKanban", "createLucideIcon"], "sources": ["D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\node_modules\\lucide-react\\src\\icons\\square-dashed-kanban.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M8 7v7', key: '1x2jlm' }],\n  ['path', { d: 'M12 7v4', key: 'xawao1' }],\n  ['path', { d: 'M16 7v9', key: '1hp2iy' }],\n  ['path', { d: 'M5 3a2 2 0 0 0-2 2', key: 'y57alp' }],\n  ['path', { d: 'M9 3h1', key: '1yesri' }],\n  ['path', { d: 'M14 3h1', key: '1ec4yj' }],\n  ['path', { d: 'M19 3a2 2 0 0 1 2 2', key: '18rm91' }],\n  ['path', { d: 'M21 9v1', key: 'mxsmne' }],\n  ['path', { d: 'M21 14v1', key: '169vum' }],\n  ['path', { d: 'M21 19a2 2 0 0 1-2 2', key: '1j7049' }],\n  ['path', { d: 'M14 21h1', key: 'v9vybs' }],\n  ['path', { d: 'M9 21h1', key: '15o7lz' }],\n  ['path', { d: 'M5 21a2 2 0 0 1-2-2', key: 'sbafld' }],\n  ['path', { d: 'M3 14v1', key: 'vnatye' }],\n  ['path', { d: 'M3 9v1', key: '1r0deq' }],\n];\n\n/**\n * @component @name SquareDashedKanban\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNOCA3djciIC8+CiAgPHBhdGggZD0iTTEyIDd2NCIgLz4KICA8cGF0aCBkPSJNMTYgN3Y5IiAvPgogIDxwYXRoIGQ9Ik01IDNhMiAyIDAgMCAwLTIgMiIgLz4KICA8cGF0aCBkPSJNOSAzaDEiIC8+CiAgPHBhdGggZD0iTTE0IDNoMSIgLz4KICA8cGF0aCBkPSJNMTkgM2EyIDIgMCAwIDEgMiAyIiAvPgogIDxwYXRoIGQ9Ik0yMSA5djEiIC8+CiAgPHBhdGggZD0iTTIxIDE0djEiIC8+CiAgPHBhdGggZD0iTTIxIDE5YTIgMiAwIDAgMS0yIDIiIC8+CiAgPHBhdGggZD0iTTE0IDIxaDEiIC8+CiAgPHBhdGggZD0iTTkgMjFoMSIgLz4KICA8cGF0aCBkPSJNNSAyMWEyIDIgMCAwIDEtMi0yIiAvPgogIDxwYXRoIGQ9Ik0zIDE0djEiIC8+CiAgPHBhdGggZD0iTTMgOXYxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/square-dashed-kanban\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst SquareDashedKanban = createLucideIcon('square-dashed-kanban', __iconNode);\n\nexport default SquareDashedKanban;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,QAAU;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,oBAAsB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACnD,CAAC,MAAQ;EAAED,CAAA,EAAG,QAAU;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,qBAAuB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACpD,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,sBAAwB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACrD,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,qBAAuB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACpD,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,QAAU;EAAAC,GAAA,EAAK;AAAU,GACzC;AAaM,MAAAC,kBAAA,GAAqBC,gBAAiB,yBAAwBJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}