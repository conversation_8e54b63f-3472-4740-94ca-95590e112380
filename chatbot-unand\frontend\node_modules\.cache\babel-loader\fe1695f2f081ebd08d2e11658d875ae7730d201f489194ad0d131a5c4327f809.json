{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M2 17a5 5 0 0 0 10 0c0-2.76-2.5-5-5-3-2.5-2-5 .24-5 3Z\",\n  key: \"cvxqlc\"\n}], [\"path\", {\n  d: \"M12 17a5 5 0 0 0 10 0c0-2.76-2.5-5-5-3-2.5-2-5 .24-5 3Z\",\n  key: \"1ostrc\"\n}], [\"path\", {\n  d: \"M7 14c3.22-2.91 4.29-8.75 5-12 1.66 2.38 4.94 9 5 12\",\n  key: \"hqx58h\"\n}], [\"path\", {\n  d: \"M22 9c-4.29 0-7.14-2.33-10-7 5.71 0 10 4.67 10 7Z\",\n  key: \"eykp1o\"\n}]];\nconst Cherry = createLucideIcon(\"cherry\", __iconNode);\nexport { __iconNode, Cherry as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "Cherry", "createLucideIcon"], "sources": ["D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\node_modules\\lucide-react\\src\\icons\\cherry.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M2 17a5 5 0 0 0 10 0c0-2.76-2.5-5-5-3-2.5-2-5 .24-5 3Z', key: 'cvxqlc' }],\n  ['path', { d: 'M12 17a5 5 0 0 0 10 0c0-2.76-2.5-5-5-3-2.5-2-5 .24-5 3Z', key: '1ostrc' }],\n  ['path', { d: 'M7 14c3.22-2.91 4.29-8.75 5-12 1.66 2.38 4.94 9 5 12', key: 'hqx58h' }],\n  ['path', { d: 'M22 9c-4.29 0-7.14-2.33-10-7 5.71 0 10 4.67 10 7Z', key: 'eykp1o' }],\n];\n\n/**\n * @component @name Cherry\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMiAxN2E1IDUgMCAwIDAgMTAgMGMwLTIuNzYtMi41LTUtNS0zLTIuNS0yLTUgLjI0LTUgM1oiIC8+CiAgPHBhdGggZD0iTTEyIDE3YTUgNSAwIDAgMCAxMCAwYzAtMi43Ni0yLjUtNS01LTMtMi41LTItNSAuMjQtNSAzWiIgLz4KICA8cGF0aCBkPSJNNyAxNGMzLjIyLTIuOTEgNC4yOS04Ljc1IDUtMTIgMS42NiAyLjM4IDQuOTQgOSA1IDEyIiAvPgogIDxwYXRoIGQ9Ik0yMiA5Yy00LjI5IDAtNy4xNC0yLjMzLTEwLTcgNS43MSAwIDEwIDQuNjcgMTAgN1oiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/cherry\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Cherry = createLucideIcon('cherry', __iconNode);\n\nexport default Cherry;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,wDAA0D;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvF,CAAC,MAAQ;EAAED,CAAA,EAAG,yDAA2D;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxF,CAAC,MAAQ;EAAED,CAAA,EAAG,sDAAwD;EAAAC,GAAA,EAAK;AAAA,CAAU,GACrF,CAAC,MAAQ;EAAED,CAAA,EAAG,mDAAqD;EAAAC,GAAA,EAAK;AAAU,GACpF;AAaM,MAAAC,MAAA,GAASC,gBAAiB,WAAUJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}