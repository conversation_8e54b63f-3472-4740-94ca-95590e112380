{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website kp\\\\chatbot-unand\\\\frontend\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport ChatWindow from \"./ChatWindow\";\nimport ChatSidebar from \"./ChatSidebar\";\nimport { getSessionMessages } from \"./api\";\nimport \"./index.css\"; // Pastikan Tailwind CSS diimpor\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [currentSessionId, setCurrentSessionId] = useState(null);\n  const [messages, setMessages] = useState([{\n    id: 1,\n    text: \"Halo! Saya adalah Chatbot UNAND. Saya siap membantu Anda dengan pertanyaan seputar peraturan kampus dan pemerintah. Silakan ajukan pertanyaan Anda!\",\n    isBot: true,\n    timestamp: new Date()\n  }]);\n  const handleSessionSelect = async sessionId => {\n    try {\n      setCurrentSessionId(sessionId);\n      const sessionMessages = await getSessionMessages(sessionId);\n\n      // Convert API messages to frontend format\n      const convertedMessages = sessionMessages.map(msg => ({\n        id: msg.id,\n        text: msg.content,\n        isBot: msg.message_type === \"bot\",\n        timestamp: new Date(msg.timestamp),\n        sources: msg.sources\n      }));\n      setMessages(convertedMessages);\n    } catch (error) {\n      console.error(\"Error loading session messages:\", error);\n      alert(\"Gagal memuat riwayat percakapan\");\n    }\n  };\n  const handleNewChat = () => {\n    setCurrentSessionId(null);\n    setMessages([{\n      id: 1,\n      text: \"Halo! Saya adalah Chatbot UNAND. Saya siap membantu Anda dengan pertanyaan seputar peraturan kampus dan pemerintah. Silakan ajukan pertanyaan Anda!\",\n      isBot: true,\n      timestamp: new Date()\n    }]);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"h-screen bg-gray-100 flex overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed left-0 top-0 h-full z-10\",\n      children: /*#__PURE__*/_jsxDEV(ChatSidebar, {\n        currentSessionId: currentSessionId,\n        onSessionSelect: handleSessionSelect,\n        onNewChat: handleNewChat\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col ml-80\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed top-0 right-0 bg-gradient-to-r from-green-50 to-yellow-50 border-b-2 border-green-600 p-4 z-10 shadow-lg\",\n        style: {\n          left: \"320px\"\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-shrink-0\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"/lambang-unand.jpg\",\n              alt: \"Logo Universitas Andalas\",\n              className: \"w-16 h-16 object-contain rounded-lg shadow-md border-2 border-green-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 72,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-2xl font-bold text-green-700 mb-1\",\n              children: \"Chatbot UNAND\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 81,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-green-600 text-sm font-medium mb-1\",\n              children: \"Asisten AI untuk pertanyaan seputar peraturan kampus dan pemerintah\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 84,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-yellow-700 text-xs font-bold tracking-wider uppercase\",\n              children: \"\\\"UNTUK KEDJAJAAN BANGSA\\\"\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-shrink-0\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-gradient-to-br from-green-600 to-yellow-600 rounded-full flex items-center justify-center shadow-md\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-6 h-6 text-white\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 102,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 96,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1\",\n        style: {\n          marginTop: \"120px\",\n          // Height of header with logo (padding + logo + text)\n          height: \"calc(100vh - 120px)\"\n        },\n        children: /*#__PURE__*/_jsxDEV(ChatWindow, {\n          messages: messages,\n          setMessages: setMessages,\n          currentSessionId: currentSessionId,\n          setCurrentSessionId: setCurrentSessionId\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 52,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"R1gwBKiSP0X6FWgCVSgx3kOYYAA=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "ChatWindow", "ChatSidebar", "getSessionMessages", "jsxDEV", "_jsxDEV", "App", "_s", "currentSessionId", "setCurrentSessionId", "messages", "setMessages", "id", "text", "isBot", "timestamp", "Date", "handleSessionSelect", "sessionId", "sessionMessages", "convertedMessages", "map", "msg", "content", "message_type", "sources", "error", "console", "alert", "handleNewChat", "className", "children", "onSessionSelect", "onNewChat", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "left", "src", "alt", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "marginTop", "height", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/website kp/chatbot-unand/frontend/src/App.js"], "sourcesContent": ["import React, { useState } from \"react\";\nimport ChatWindow from \"./ChatWindow\";\nimport ChatS<PERSON>bar from \"./ChatSidebar\";\nimport { getSessionMessages } from \"./api\";\nimport \"./index.css\"; // Pastikan Tailwind CSS diimpor\n\nfunction App() {\n  const [currentSessionId, setCurrentSessionId] = useState(null);\n  const [messages, setMessages] = useState([\n    {\n      id: 1,\n      text: \"Halo! Saya adalah Chatbot UNAND. Saya siap membantu Anda dengan pertanyaan seputar peraturan kampus dan pemerintah. Silakan ajukan pertanyaan Anda!\",\n      isBot: true,\n      timestamp: new Date(),\n    },\n  ]);\n\n  const handleSessionSelect = async (sessionId) => {\n    try {\n      setCurrentSessionId(sessionId);\n      const sessionMessages = await getSessionMessages(sessionId);\n\n      // Convert API messages to frontend format\n      const convertedMessages = sessionMessages.map((msg) => ({\n        id: msg.id,\n        text: msg.content,\n        isBot: msg.message_type === \"bot\",\n        timestamp: new Date(msg.timestamp),\n        sources: msg.sources,\n      }));\n\n      setMessages(convertedMessages);\n    } catch (error) {\n      console.error(\"Error loading session messages:\", error);\n      alert(\"Gagal memuat riwayat percakapan\");\n    }\n  };\n\n  const handleNewChat = () => {\n    setCurrentSessionId(null);\n    setMessages([\n      {\n        id: 1,\n        text: \"Halo! Saya adalah Chatbot UNAND. Saya siap membantu Anda dengan pertanyaan seputar peraturan kampus dan pemerintah. Silakan ajukan pertanyaan Anda!\",\n        isBot: true,\n        timestamp: new Date(),\n      },\n    ]);\n  };\n\n  return (\n    <div className=\"h-screen bg-gray-100 flex overflow-hidden\">\n      {/* Fixed Sidebar */}\n      <div className=\"fixed left-0 top-0 h-full z-10\">\n        <ChatSidebar\n          currentSessionId={currentSessionId}\n          onSessionSelect={handleSessionSelect}\n          onNewChat={handleNewChat}\n        />\n      </div>\n\n      {/* Main Content Area */}\n      <div className=\"flex-1 flex flex-col ml-80\">\n        {/* Fixed Header */}\n        <div\n          className=\"fixed top-0 right-0 bg-gradient-to-r from-green-50 to-yellow-50 border-b-2 border-green-600 p-4 z-10 shadow-lg\"\n          style={{ left: \"320px\" }}\n        >\n          <div className=\"flex items-center gap-4\">\n            {/* Logo UNAND */}\n            <div className=\"flex-shrink-0\">\n              <img\n                src=\"/lambang-unand.jpg\"\n                alt=\"Logo Universitas Andalas\"\n                className=\"w-16 h-16 object-contain rounded-lg shadow-md border-2 border-green-600\"\n              />\n            </div>\n\n            {/* Title and Tagline */}\n            <div className=\"flex-1\">\n              <h1 className=\"text-2xl font-bold text-green-700 mb-1\">\n                Chatbot UNAND\n              </h1>\n              <p className=\"text-green-600 text-sm font-medium mb-1\">\n                Asisten AI untuk pertanyaan seputar peraturan kampus dan\n                pemerintah\n              </p>\n              <p className=\"text-yellow-700 text-xs font-bold tracking-wider uppercase\">\n                \"UNTUK KEDJAJAAN BANGSA\"\n              </p>\n            </div>\n\n            {/* Decorative Element */}\n            <div className=\"flex-shrink-0\">\n              <div className=\"w-12 h-12 bg-gradient-to-br from-green-600 to-yellow-600 rounded-full flex items-center justify-center shadow-md\">\n                <svg\n                  className=\"w-6 h-6 text-white\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  viewBox=\"0 0 24 24\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    strokeWidth={2}\n                    d=\"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                  />\n                </svg>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Chat Window with calculated height to avoid header overlap */}\n        <div\n          className=\"flex-1\"\n          style={{\n            marginTop: \"120px\", // Height of header with logo (padding + logo + text)\n            height: \"calc(100vh - 120px)\",\n          }}\n        >\n          <ChatWindow\n            messages={messages}\n            setMessages={setMessages}\n            currentSessionId={currentSessionId}\n            setCurrentSessionId={setCurrentSessionId}\n          />\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,kBAAkB,QAAQ,OAAO;AAC1C,OAAO,aAAa,CAAC,CAAC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEtB,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGT,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACU,QAAQ,EAAEC,WAAW,CAAC,GAAGX,QAAQ,CAAC,CACvC;IACEY,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,qJAAqJ;IAC3JC,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,IAAIC,IAAI,CAAC;EACtB,CAAC,CACF,CAAC;EAEF,MAAMC,mBAAmB,GAAG,MAAOC,SAAS,IAAK;IAC/C,IAAI;MACFT,mBAAmB,CAACS,SAAS,CAAC;MAC9B,MAAMC,eAAe,GAAG,MAAMhB,kBAAkB,CAACe,SAAS,CAAC;;MAE3D;MACA,MAAME,iBAAiB,GAAGD,eAAe,CAACE,GAAG,CAAEC,GAAG,KAAM;QACtDV,EAAE,EAAEU,GAAG,CAACV,EAAE;QACVC,IAAI,EAAES,GAAG,CAACC,OAAO;QACjBT,KAAK,EAAEQ,GAAG,CAACE,YAAY,KAAK,KAAK;QACjCT,SAAS,EAAE,IAAIC,IAAI,CAACM,GAAG,CAACP,SAAS,CAAC;QAClCU,OAAO,EAAEH,GAAG,CAACG;MACf,CAAC,CAAC,CAAC;MAEHd,WAAW,CAACS,iBAAiB,CAAC;IAChC,CAAC,CAAC,OAAOM,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvDE,KAAK,CAAC,iCAAiC,CAAC;IAC1C;EACF,CAAC;EAED,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1BpB,mBAAmB,CAAC,IAAI,CAAC;IACzBE,WAAW,CAAC,CACV;MACEC,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,qJAAqJ;MAC3JC,KAAK,EAAE,IAAI;MACXC,SAAS,EAAE,IAAIC,IAAI,CAAC;IACtB,CAAC,CACF,CAAC;EACJ,CAAC;EAED,oBACEX,OAAA;IAAKyB,SAAS,EAAC,2CAA2C;IAAAC,QAAA,gBAExD1B,OAAA;MAAKyB,SAAS,EAAC,gCAAgC;MAAAC,QAAA,eAC7C1B,OAAA,CAACH,WAAW;QACVM,gBAAgB,EAAEA,gBAAiB;QACnCwB,eAAe,EAAEf,mBAAoB;QACrCgB,SAAS,EAAEJ;MAAc;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNhC,OAAA;MAAKyB,SAAS,EAAC,4BAA4B;MAAAC,QAAA,gBAEzC1B,OAAA;QACEyB,SAAS,EAAC,gHAAgH;QAC1HQ,KAAK,EAAE;UAAEC,IAAI,EAAE;QAAQ,CAAE;QAAAR,QAAA,eAEzB1B,OAAA;UAAKyB,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBAEtC1B,OAAA;YAAKyB,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5B1B,OAAA;cACEmC,GAAG,EAAC,oBAAoB;cACxBC,GAAG,EAAC,0BAA0B;cAC9BX,SAAS,EAAC;YAAyE;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNhC,OAAA;YAAKyB,SAAS,EAAC,QAAQ;YAAAC,QAAA,gBACrB1B,OAAA;cAAIyB,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAEvD;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLhC,OAAA;cAAGyB,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAGvD;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJhC,OAAA;cAAGyB,SAAS,EAAC,4DAA4D;cAAAC,QAAA,EAAC;YAE1E;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAGNhC,OAAA;YAAKyB,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5B1B,OAAA;cAAKyB,SAAS,EAAC,kHAAkH;cAAAC,QAAA,eAC/H1B,OAAA;gBACEyB,SAAS,EAAC,oBAAoB;gBAC9BY,IAAI,EAAC,MAAM;gBACXC,MAAM,EAAC,cAAc;gBACrBC,OAAO,EAAC,WAAW;gBAAAb,QAAA,eAEnB1B,OAAA;kBACEwC,aAAa,EAAC,OAAO;kBACrBC,cAAc,EAAC,OAAO;kBACtBC,WAAW,EAAE,CAAE;kBACfC,CAAC,EAAC;gBAA+J;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNhC,OAAA;QACEyB,SAAS,EAAC,QAAQ;QAClBQ,KAAK,EAAE;UACLW,SAAS,EAAE,OAAO;UAAE;UACpBC,MAAM,EAAE;QACV,CAAE;QAAAnB,QAAA,eAEF1B,OAAA,CAACJ,UAAU;UACTS,QAAQ,EAAEA,QAAS;UACnBC,WAAW,EAAEA,WAAY;UACzBH,gBAAgB,EAAEA,gBAAiB;UACnCC,mBAAmB,EAAEA;QAAoB;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC9B,EAAA,CA7HQD,GAAG;AAAA6C,EAAA,GAAH7C,GAAG;AA+HZ,eAAeA,GAAG;AAAC,IAAA6C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}