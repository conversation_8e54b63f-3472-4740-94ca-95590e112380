# =============================================================================
# CHATBOT UNAND - BACKEND DEPENDENCIES
# =============================================================================

# -----------------------------------------------------------------------------
# Core FastAPI Framework
# -----------------------------------------------------------------------------
fastapi==0.111.0
uvicorn==0.30.1
python-dotenv==1.0.1
python-multipart>=0.0.7

# -----------------------------------------------------------------------------
# Database & ORM
# -----------------------------------------------------------------------------
sqlalchemy==2.0.23
sqlmodel==0.0.20
psycopg2-binary==2.9.9

# -----------------------------------------------------------------------------
# AI & Vector Database
# -----------------------------------------------------------------------------
google-generativeai==0.8.5
qdrant-client>=1.7.0
numpy==1.26.4

# -----------------------------------------------------------------------------
# Document Processing
# -----------------------------------------------------------------------------
python-docx==1.1.2

# -----------------------------------------------------------------------------
# Authentication & Security
# -----------------------------------------------------------------------------
google-auth>=2.20.0
google-auth-oauthlib>=1.0.0
google-auth-httplib2>=0.1.0
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.0

# -----------------------------------------------------------------------------
# Telegram Bot
# -----------------------------------------------------------------------------
python-telegram-bot==20.7
aiohttp==3.9.1
requests==2.31.0
nest-asyncio==1.5.8