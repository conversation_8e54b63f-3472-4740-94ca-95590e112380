{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m10 15 5 5 5-5\",\n  key: \"1hpjnr\"\n}], [\"path\", {\n  d: \"M4 4h7a4 4 0 0 1 4 4v12\",\n  key: \"wcbgct\"\n}]];\nconst CornerRightDown = createLucideIcon(\"corner-right-down\", __iconNode);\nexport { __iconNode, CornerRightDown as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "CornerRightDown", "createLucideIcon"], "sources": ["D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\node_modules\\lucide-react\\src\\icons\\corner-right-down.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm10 15 5 5 5-5', key: '1hpjnr' }],\n  ['path', { d: 'M4 4h7a4 4 0 0 1 4 4v12', key: 'wcbgct' }],\n];\n\n/**\n * @component @name CornerRightDown\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTAgMTUgNSA1IDUtNSIgLz4KICA8cGF0aCBkPSJNNCA0aDdhNCA0IDAgMCAxIDQgNHYxMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/corner-right-down\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CornerRightDown = createLucideIcon('corner-right-down', __iconNode);\n\nexport default CornerRightDown;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,gBAAkB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/C,CAAC,MAAQ;EAAED,CAAA,EAAG,yBAA2B;EAAAC,GAAA,EAAK;AAAU,GAC1D;AAaM,MAAAC,eAAA,GAAkBC,gBAAiB,sBAAqBJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}