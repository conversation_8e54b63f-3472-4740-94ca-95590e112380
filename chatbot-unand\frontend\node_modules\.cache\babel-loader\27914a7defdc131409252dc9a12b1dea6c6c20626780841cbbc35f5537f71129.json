{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website kp\\\\chatbot-unand\\\\frontend\\\\src\\\\contexts\\\\AuthContext.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from \"react\";\nimport { jwtDecode } from \"jwt-decode\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error(\"useAuth must be used within an AuthProvider\");\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [token, setToken] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Admin state\n  const [adminUser, setAdminUser] = useState(null);\n  const [adminToken, setAdminToken] = useState(null);\n\n  // Session type tracking\n  const [sessionType, setSessionType] = useState(null); // 'user' | 'admin' | null\n\n  // Tab isolation - generate unique tab ID\n  const [tabId] = useState(() => `tab_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`);\n\n  // Simple tab isolation using sessionStorage priority\n  useEffect(() => {\n    // Tab initialization without logging to prevent render issues\n  }, [tabId]);\n\n  // Check for existing token on mount\n  useEffect(() => {\n    // Use sessionStorage for tab-specific data to prevent cross-tab interference\n    const savedToken = sessionStorage.getItem(\"access_token\") || localStorage.getItem(\"access_token\");\n    const savedUser = sessionStorage.getItem(\"user\") || localStorage.getItem(\"user\");\n    const savedAdminToken = sessionStorage.getItem(\"admin_token\") || localStorage.getItem(\"admin_token\");\n    const savedAdminUser = sessionStorage.getItem(\"admin_user\") || localStorage.getItem(\"admin_user\");\n\n    // Allow dual sessions - both user and admin can be logged in simultaneously\n    console.log(\"AuthContext: Loading existing sessions\", {\n      hasUserSession: !!savedToken,\n      hasAdminSession: !!savedAdminToken\n    });\n    if (savedToken && savedUser) {\n      try {\n        // Check if token is still valid\n        const decoded = jwtDecode(savedToken);\n        const currentTime = Date.now() / 1000;\n        if (decoded.exp > currentTime) {\n          setToken(savedToken);\n          setUser(JSON.parse(savedUser));\n        } else {\n          // Token expired, clear storage\n          localStorage.removeItem(\"access_token\");\n          localStorage.removeItem(\"user\");\n        }\n      } catch (error) {\n        console.error(\"Error decoding token:\", error);\n        localStorage.removeItem(\"access_token\");\n        localStorage.removeItem(\"user\");\n      }\n    }\n    if (savedAdminToken && savedAdminUser) {\n      try {\n        // Check if admin token is still valid\n        const decoded = jwtDecode(savedAdminToken);\n        const currentTime = Date.now() / 1000;\n        if (decoded.exp > currentTime) {\n          setAdminToken(savedAdminToken);\n          setAdminUser(JSON.parse(savedAdminUser));\n        } else {\n          // Admin token expired, clear storage\n          localStorage.removeItem(\"admin_token\");\n          localStorage.removeItem(\"admin_user\");\n        }\n      } catch (error) {\n        console.error(\"Error decoding admin token:\", error);\n        localStorage.removeItem(\"admin_token\");\n        localStorage.removeItem(\"admin_user\");\n      }\n    }\n    setLoading(false);\n  }, []);\n  const login = async googleToken => {\n    try {\n      console.log(\"AuthContext: Starting user login process...\");\n\n      // Clear only user session data, keep admin session intact\n      localStorage.removeItem(\"access_token\");\n      localStorage.removeItem(\"user\");\n      setToken(null);\n      setUser(null);\n      const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || \"http://localhost:8000\";\n      console.log(\"AuthContext: Using API URL:\", API_BASE_URL);\n      const response = await fetch(`${API_BASE_URL}/auth/google`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          token: googleToken\n        })\n      });\n      console.log(\"AuthContext: Response status:\", response.status);\n      console.log(\"AuthContext: Response headers:\", response.headers);\n      if (!response.ok) {\n        const errorText = await response.text();\n        console.error(\"AuthContext: Response error:\", errorText);\n        console.error(\"AuthContext: Full response:\", response);\n        console.error(\"AuthContext: Response status:\", response.status);\n        console.error(\"AuthContext: Response statusText:\", response.statusText);\n\n        // Try to parse error as JSON if possible\n        let errorJson = null;\n        try {\n          errorJson = JSON.parse(errorText);\n          console.error(\"AuthContext: Parsed error JSON:\", errorJson);\n        } catch (e) {\n          console.error(\"AuthContext: Error text is not JSON:\", errorText);\n        }\n\n        // Handle specific token timing errors\n        if (errorJson && errorJson.detail && errorJson.detail.includes(\"Token used too early\")) {\n          console.log(\"AuthContext: Token timing issue detected, retrying in 2 seconds...\");\n          await new Promise(resolve => setTimeout(resolve, 2000));\n\n          // Retry the request\n          const retryResponse = await fetch(`${API_BASE_URL}/auth/google`, {\n            method: \"POST\",\n            headers: {\n              \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n              token: googleToken\n            })\n          });\n          if (retryResponse.ok) {\n            const retryData = await retryResponse.json();\n            console.log(\"AuthContext: Retry authentication successful\");\n\n            // Save to both sessionStorage (priority) and localStorage (backup)\n            sessionStorage.setItem(\"access_token\", retryData.access_token);\n            sessionStorage.setItem(\"user\", JSON.stringify(retryData.user));\n            localStorage.setItem(\"access_token\", retryData.access_token);\n            localStorage.setItem(\"user\", JSON.stringify(retryData.user));\n\n            // Update state\n            setToken(retryData.access_token);\n            setUser(retryData.user);\n            console.log(\"AuthContext: Login completed successfully after retry\");\n            return retryData;\n          }\n        }\n        throw new Error(`Authentication failed: ${response.status} - ${errorText}`);\n      }\n      const data = await response.json();\n      console.log(\"AuthContext: Response data:\", data);\n\n      // Save to both sessionStorage (priority) and localStorage (backup)\n      sessionStorage.setItem(\"access_token\", data.access_token);\n      sessionStorage.setItem(\"user\", JSON.stringify(data.user));\n      localStorage.setItem(\"access_token\", data.access_token);\n      localStorage.setItem(\"user\", JSON.stringify(data.user));\n\n      // Update state\n      setToken(data.access_token);\n      setUser(data.user);\n      console.log(\"AuthContext: Login completed successfully\");\n      return data;\n    } catch (error) {\n      console.error(\"AuthContext: Login error:\", error);\n      // Clear any partial authentication data on error\n      forceLogout();\n      throw error;\n    }\n  };\n  const logout = async () => {\n    console.log(\"AuthContext: Starting logout process...\");\n    try {\n      // Call backend logout endpoint if user is authenticated\n      if (token) {\n        const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || \"http://localhost:8000\";\n        console.log(\"AuthContext: Calling backend logout endpoint...\");\n        const response = await fetch(`${API_BASE_URL}/auth/logout`, {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${token}`\n          }\n        });\n        if (response.ok) {\n          const data = await response.json();\n          console.log(\"AuthContext: Backend logout successful:\", data);\n          console.log(`AuthContext: Deactivated ${data.sessions_deactivated} sessions`);\n        } else {\n          console.warn(\"AuthContext: Backend logout failed, but continuing with frontend logout\");\n        }\n      }\n    } catch (error) {\n      console.error(\"AuthContext: Error calling backend logout:\", error);\n      console.log(\"AuthContext: Continuing with frontend logout despite backend error\");\n    }\n\n    // Clear both sessionStorage and localStorage\n    sessionStorage.removeItem(\"access_token\");\n    sessionStorage.removeItem(\"user\");\n    localStorage.removeItem(\"access_token\");\n    localStorage.removeItem(\"user\");\n\n    // Clear state\n    setToken(null);\n    setUser(null);\n\n    // Sign out from Google more thoroughly\n    if (window.google && window.google.accounts) {\n      console.log(\"AuthContext: Signing out from Google...\");\n      try {\n        // Disable auto-select\n        window.google.accounts.id.disableAutoSelect();\n\n        // Revoke the current session\n        window.google.accounts.id.revoke(token, done => {\n          console.log(\"AuthContext: Google token revoked:\", done);\n        });\n\n        // Clear any Google session cookies\n        document.cookie = \"g_state=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.google.com;\";\n        document.cookie = \"g_csrf_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.google.com;\";\n      } catch (error) {\n        console.warn(\"AuthContext: Error during Google sign-out:\", error);\n      }\n    }\n    console.log(\"AuthContext: Logout completed\");\n  };\n  const forceLogout = () => {\n    console.log(\"AuthContext: Force logout - clearing all authentication data\");\n\n    // Clear localStorage immediately\n    localStorage.removeItem(\"access_token\");\n    localStorage.removeItem(\"user\");\n\n    // Clear state\n    setToken(null);\n    setUser(null);\n\n    // Clear Google session\n    if (window.google && window.google.accounts) {\n      try {\n        window.google.accounts.id.disableAutoSelect();\n      } catch (error) {\n        console.warn(\"AuthContext: Error during force Google sign-out:\", error);\n      }\n    }\n    console.log(\"AuthContext: Force logout completed\");\n  };\n  const isTokenExpired = token => {\n    if (!token) return true;\n    try {\n      // Decode JWT token to check expiry\n      const payload = JSON.parse(atob(token.split(\".\")[1]));\n      const currentTime = Math.floor(Date.now() / 1000);\n\n      // Check if token is expired (with 30 second buffer)\n      return payload.exp < currentTime + 30;\n    } catch (error) {\n      console.error(\"AuthContext: Error checking token expiry:\", error);\n      return true; // Assume expired if can't decode\n    }\n  };\n  const getAuthHeaders = () => {\n    if (token && !isTokenExpired(token)) {\n      return {\n        Authorization: `Bearer ${token}`,\n        \"Content-Type\": \"application/json\"\n      };\n    } else if (token && isTokenExpired(token)) {\n      // Token is expired, clear it\n      console.log(\"AuthContext: Token expired, clearing authentication\");\n      logout();\n      return {\n        \"Content-Type\": \"application/json\"\n      };\n    }\n    return {\n      \"Content-Type\": \"application/json\"\n    };\n  };\n\n  // Admin functions\n  const loginAdmin = async (email, password) => {\n    try {\n      console.log(\"AuthContext: Starting admin login process...\");\n\n      // Clear only admin session data, keep user session intact\n      localStorage.removeItem(\"admin_token\");\n      localStorage.removeItem(\"admin_user\");\n      setAdminToken(null);\n      setAdminUser(null);\n      const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || \"http://localhost:8000\";\n      const response = await fetch(`${API_BASE_URL}/admin/login`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          email,\n          password\n        })\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || \"Admin login failed\");\n      }\n      const data = await response.json();\n      console.log(\"AuthContext: Admin login successful\");\n\n      // Save to both sessionStorage (priority) and localStorage (backup)\n      sessionStorage.setItem(\"admin_token\", data.access_token);\n      sessionStorage.setItem(\"admin_user\", JSON.stringify({\n        email: data.admin_email\n      }));\n      localStorage.setItem(\"admin_token\", data.access_token);\n      localStorage.setItem(\"admin_user\", JSON.stringify({\n        email: data.admin_email\n      }));\n\n      // Update state\n      setAdminToken(data.access_token);\n      setAdminUser({\n        email: data.admin_email\n      });\n      return data;\n    } catch (error) {\n      console.error(\"AuthContext: Admin login error:\", error);\n      throw error;\n    }\n  };\n  const logoutAdmin = () => {\n    console.log(\"AuthContext: Admin logout\");\n\n    // Clear both sessionStorage and localStorage\n    sessionStorage.removeItem(\"admin_token\");\n    sessionStorage.removeItem(\"admin_user\");\n    localStorage.removeItem(\"admin_token\");\n    localStorage.removeItem(\"admin_user\");\n\n    // Clear state\n    setAdminToken(null);\n    setAdminUser(null);\n  };\n  const getAdminAuthHeaders = () => {\n    if (adminToken && !isTokenExpired(adminToken)) {\n      return {\n        Authorization: `Bearer ${adminToken}`,\n        \"Content-Type\": \"application/json\"\n      };\n    } else if (adminToken && isTokenExpired(adminToken)) {\n      // Admin token is expired, clear it\n      console.log(\"AuthContext: Admin token expired, clearing authentication\");\n      logoutAdmin();\n      return {\n        \"Content-Type\": \"application/json\"\n      };\n    }\n    return {\n      \"Content-Type\": \"application/json\"\n    };\n  };\n  const getAdminAuthHeadersForUpload = () => {\n    if (adminToken && !isTokenExpired(adminToken)) {\n      return {\n        Authorization: `Bearer ${adminToken}`\n        // Don't set Content-Type for FormData uploads\n      };\n    } else if (adminToken && isTokenExpired(adminToken)) {\n      // Admin token is expired, clear it\n      console.log(\"AuthContext: Admin token expired, clearing authentication\");\n      logoutAdmin();\n      return {};\n    }\n    return {};\n  };\n\n  // Session separation functions\n  const clearAllSessions = () => {\n    console.log(\"AuthContext: Clearing all sessions\");\n    sessionStorage.clear();\n    localStorage.clear();\n    setToken(null);\n    setUser(null);\n    setAdminToken(null);\n    setAdminUser(null);\n    setSessionType(null);\n  };\n\n  // Check if user has required session type for the route\n  const hasRequiredSession = requiredType => {\n    if (requiredType === \"user\") {\n      return !!user && !!token;\n    } else if (requiredType === \"admin\") {\n      return !!adminUser && !!adminToken;\n    }\n    return false;\n  };\n\n  // Check if user is trying to access wrong interface\n  const isWrongInterface = (currentPath, userType) => {\n    const isAdminPath = currentPath.startsWith(\"/admin\");\n    const isUserPath = !isAdminPath;\n    if (userType === \"user\" && isAdminPath) {\n      return true; // User trying to access admin interface\n    }\n    if (userType === \"admin\" && isUserPath) {\n      return true; // Admin trying to access user interface\n    }\n    return false;\n  };\n  const value = {\n    user,\n    token,\n    loading,\n    login,\n    logout,\n    forceLogout,\n    getAuthHeaders,\n    isAuthenticated: !!user,\n    // Admin functions\n    adminUser,\n    adminToken,\n    loginAdmin,\n    logoutAdmin,\n    getAdminAuthHeaders,\n    getAdminAuthHeadersForUpload,\n    isAdminAuthenticated: !!adminUser,\n    // Session separation\n    sessionType,\n    clearAllSessions,\n    hasRequiredSession,\n    isWrongInterface\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 501,\n    columnNumber: 10\n  }, this);\n};\n_s2(AuthProvider, \"urfw2Y2bHXU7OYIji7iHsmN7oKg=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "jwtDecode", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "token", "setToken", "loading", "setLoading", "adminUser", "setAdminUser", "adminToken", "setAdminToken", "sessionType", "setSessionType", "tabId", "Date", "now", "Math", "random", "toString", "substr", "savedToken", "sessionStorage", "getItem", "localStorage", "savedUser", "savedAdminToken", "savedAdminUser", "console", "log", "hasUserSession", "hasAdminSession", "decoded", "currentTime", "exp", "JSON", "parse", "removeItem", "error", "login", "googleToken", "API_BASE_URL", "process", "env", "REACT_APP_API_BASE_URL", "response", "fetch", "method", "headers", "body", "stringify", "status", "ok", "errorText", "text", "statusText", "<PERSON><PERSON><PERSON>", "e", "detail", "includes", "Promise", "resolve", "setTimeout", "retryResponse", "retryData", "json", "setItem", "access_token", "data", "forceLogout", "logout", "Authorization", "sessions_deactivated", "warn", "window", "google", "accounts", "id", "disableAutoSelect", "revoke", "done", "document", "cookie", "isTokenExpired", "payload", "atob", "split", "floor", "getAuthHeaders", "loginAdmin", "email", "password", "errorData", "admin_email", "logoutAdmin", "getAdminAuthHeaders", "getAdminAuthHeadersForUpload", "clearAllSessions", "clear", "hasRequiredSession", "requiredType", "isWrongInterface", "currentPath", "userType", "isAdminPath", "startsWith", "isUserPath", "value", "isAuthenticated", "isAdminAuthenticated", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/website kp/chatbot-unand/frontend/src/contexts/AuthContext.jsx"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from \"react\";\nimport { jwtDecode } from \"jwt-decode\";\n\nconst AuthContext = createContext();\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error(\"useAuth must be used within an AuthProvider\");\n  }\n  return context;\n};\n\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [token, setToken] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Admin state\n  const [adminUser, setAdminUser] = useState(null);\n  const [adminToken, setAdminToken] = useState(null);\n\n  // Session type tracking\n  const [sessionType, setSessionType] = useState(null); // 'user' | 'admin' | null\n\n  // Tab isolation - generate unique tab ID\n  const [tabId] = useState(\n    () => `tab_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`\n  );\n\n  // Simple tab isolation using sessionStorage priority\n  useEffect(() => {\n    // Tab initialization without logging to prevent render issues\n  }, [tabId]);\n\n  // Check for existing token on mount\n  useEffect(() => {\n    // Use sessionStorage for tab-specific data to prevent cross-tab interference\n    const savedToken =\n      sessionStorage.getItem(\"access_token\") ||\n      localStorage.getItem(\"access_token\");\n    const savedUser =\n      sessionStorage.getItem(\"user\") || localStorage.getItem(\"user\");\n    const savedAdminToken =\n      sessionStorage.getItem(\"admin_token\") ||\n      localStorage.getItem(\"admin_token\");\n    const savedAdminUser =\n      sessionStorage.getItem(\"admin_user\") ||\n      localStorage.getItem(\"admin_user\");\n\n    // Allow dual sessions - both user and admin can be logged in simultaneously\n    console.log(\"AuthContext: Loading existing sessions\", {\n      hasUserSession: !!savedToken,\n      hasAdminSession: !!savedAdminToken,\n    });\n\n    if (savedToken && savedUser) {\n      try {\n        // Check if token is still valid\n        const decoded = jwtDecode(savedToken);\n        const currentTime = Date.now() / 1000;\n\n        if (decoded.exp > currentTime) {\n          setToken(savedToken);\n          setUser(JSON.parse(savedUser));\n        } else {\n          // Token expired, clear storage\n          localStorage.removeItem(\"access_token\");\n          localStorage.removeItem(\"user\");\n        }\n      } catch (error) {\n        console.error(\"Error decoding token:\", error);\n        localStorage.removeItem(\"access_token\");\n        localStorage.removeItem(\"user\");\n      }\n    }\n\n    if (savedAdminToken && savedAdminUser) {\n      try {\n        // Check if admin token is still valid\n        const decoded = jwtDecode(savedAdminToken);\n        const currentTime = Date.now() / 1000;\n\n        if (decoded.exp > currentTime) {\n          setAdminToken(savedAdminToken);\n          setAdminUser(JSON.parse(savedAdminUser));\n        } else {\n          // Admin token expired, clear storage\n          localStorage.removeItem(\"admin_token\");\n          localStorage.removeItem(\"admin_user\");\n        }\n      } catch (error) {\n        console.error(\"Error decoding admin token:\", error);\n        localStorage.removeItem(\"admin_token\");\n        localStorage.removeItem(\"admin_user\");\n      }\n    }\n\n    setLoading(false);\n  }, []);\n\n  const login = async (googleToken) => {\n    try {\n      console.log(\"AuthContext: Starting user login process...\");\n\n      // Clear only user session data, keep admin session intact\n      localStorage.removeItem(\"access_token\");\n      localStorage.removeItem(\"user\");\n      setToken(null);\n      setUser(null);\n\n      const API_BASE_URL =\n        process.env.REACT_APP_API_BASE_URL || \"http://localhost:8000\";\n      console.log(\"AuthContext: Using API URL:\", API_BASE_URL);\n      const response = await fetch(`${API_BASE_URL}/auth/google`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify({ token: googleToken }),\n      });\n\n      console.log(\"AuthContext: Response status:\", response.status);\n      console.log(\"AuthContext: Response headers:\", response.headers);\n\n      if (!response.ok) {\n        const errorText = await response.text();\n        console.error(\"AuthContext: Response error:\", errorText);\n        console.error(\"AuthContext: Full response:\", response);\n        console.error(\"AuthContext: Response status:\", response.status);\n        console.error(\"AuthContext: Response statusText:\", response.statusText);\n\n        // Try to parse error as JSON if possible\n        let errorJson = null;\n        try {\n          errorJson = JSON.parse(errorText);\n          console.error(\"AuthContext: Parsed error JSON:\", errorJson);\n        } catch (e) {\n          console.error(\"AuthContext: Error text is not JSON:\", errorText);\n        }\n\n        // Handle specific token timing errors\n        if (\n          errorJson &&\n          errorJson.detail &&\n          errorJson.detail.includes(\"Token used too early\")\n        ) {\n          console.log(\n            \"AuthContext: Token timing issue detected, retrying in 2 seconds...\"\n          );\n          await new Promise((resolve) => setTimeout(resolve, 2000));\n\n          // Retry the request\n          const retryResponse = await fetch(`${API_BASE_URL}/auth/google`, {\n            method: \"POST\",\n            headers: {\n              \"Content-Type\": \"application/json\",\n            },\n            body: JSON.stringify({ token: googleToken }),\n          });\n\n          if (retryResponse.ok) {\n            const retryData = await retryResponse.json();\n            console.log(\"AuthContext: Retry authentication successful\");\n\n            // Save to both sessionStorage (priority) and localStorage (backup)\n            sessionStorage.setItem(\"access_token\", retryData.access_token);\n            sessionStorage.setItem(\"user\", JSON.stringify(retryData.user));\n            localStorage.setItem(\"access_token\", retryData.access_token);\n            localStorage.setItem(\"user\", JSON.stringify(retryData.user));\n\n            // Update state\n            setToken(retryData.access_token);\n            setUser(retryData.user);\n\n            console.log(\n              \"AuthContext: Login completed successfully after retry\"\n            );\n            return retryData;\n          }\n        }\n\n        throw new Error(\n          `Authentication failed: ${response.status} - ${errorText}`\n        );\n      }\n\n      const data = await response.json();\n      console.log(\"AuthContext: Response data:\", data);\n\n      // Save to both sessionStorage (priority) and localStorage (backup)\n      sessionStorage.setItem(\"access_token\", data.access_token);\n      sessionStorage.setItem(\"user\", JSON.stringify(data.user));\n      localStorage.setItem(\"access_token\", data.access_token);\n      localStorage.setItem(\"user\", JSON.stringify(data.user));\n\n      // Update state\n      setToken(data.access_token);\n      setUser(data.user);\n\n      console.log(\"AuthContext: Login completed successfully\");\n      return data;\n    } catch (error) {\n      console.error(\"AuthContext: Login error:\", error);\n      // Clear any partial authentication data on error\n      forceLogout();\n      throw error;\n    }\n  };\n\n  const logout = async () => {\n    console.log(\"AuthContext: Starting logout process...\");\n\n    try {\n      // Call backend logout endpoint if user is authenticated\n      if (token) {\n        const API_BASE_URL =\n          process.env.REACT_APP_API_BASE_URL || \"http://localhost:8000\";\n        console.log(\"AuthContext: Calling backend logout endpoint...\");\n\n        const response = await fetch(`${API_BASE_URL}/auth/logout`, {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${token}`,\n          },\n        });\n\n        if (response.ok) {\n          const data = await response.json();\n          console.log(\"AuthContext: Backend logout successful:\", data);\n          console.log(\n            `AuthContext: Deactivated ${data.sessions_deactivated} sessions`\n          );\n        } else {\n          console.warn(\n            \"AuthContext: Backend logout failed, but continuing with frontend logout\"\n          );\n        }\n      }\n    } catch (error) {\n      console.error(\"AuthContext: Error calling backend logout:\", error);\n      console.log(\n        \"AuthContext: Continuing with frontend logout despite backend error\"\n      );\n    }\n\n    // Clear both sessionStorage and localStorage\n    sessionStorage.removeItem(\"access_token\");\n    sessionStorage.removeItem(\"user\");\n    localStorage.removeItem(\"access_token\");\n    localStorage.removeItem(\"user\");\n\n    // Clear state\n    setToken(null);\n    setUser(null);\n\n    // Sign out from Google more thoroughly\n    if (window.google && window.google.accounts) {\n      console.log(\"AuthContext: Signing out from Google...\");\n      try {\n        // Disable auto-select\n        window.google.accounts.id.disableAutoSelect();\n\n        // Revoke the current session\n        window.google.accounts.id.revoke(token, (done) => {\n          console.log(\"AuthContext: Google token revoked:\", done);\n        });\n\n        // Clear any Google session cookies\n        document.cookie =\n          \"g_state=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.google.com;\";\n        document.cookie =\n          \"g_csrf_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.google.com;\";\n      } catch (error) {\n        console.warn(\"AuthContext: Error during Google sign-out:\", error);\n      }\n    }\n\n    console.log(\"AuthContext: Logout completed\");\n  };\n\n  const forceLogout = () => {\n    console.log(\"AuthContext: Force logout - clearing all authentication data\");\n\n    // Clear localStorage immediately\n    localStorage.removeItem(\"access_token\");\n    localStorage.removeItem(\"user\");\n\n    // Clear state\n    setToken(null);\n    setUser(null);\n\n    // Clear Google session\n    if (window.google && window.google.accounts) {\n      try {\n        window.google.accounts.id.disableAutoSelect();\n      } catch (error) {\n        console.warn(\"AuthContext: Error during force Google sign-out:\", error);\n      }\n    }\n\n    console.log(\"AuthContext: Force logout completed\");\n  };\n\n  const isTokenExpired = (token) => {\n    if (!token) return true;\n\n    try {\n      // Decode JWT token to check expiry\n      const payload = JSON.parse(atob(token.split(\".\")[1]));\n      const currentTime = Math.floor(Date.now() / 1000);\n\n      // Check if token is expired (with 30 second buffer)\n      return payload.exp < currentTime + 30;\n    } catch (error) {\n      console.error(\"AuthContext: Error checking token expiry:\", error);\n      return true; // Assume expired if can't decode\n    }\n  };\n\n  const getAuthHeaders = () => {\n    if (token && !isTokenExpired(token)) {\n      return {\n        Authorization: `Bearer ${token}`,\n        \"Content-Type\": \"application/json\",\n      };\n    } else if (token && isTokenExpired(token)) {\n      // Token is expired, clear it\n      console.log(\"AuthContext: Token expired, clearing authentication\");\n      logout();\n      return {\n        \"Content-Type\": \"application/json\",\n      };\n    }\n    return {\n      \"Content-Type\": \"application/json\",\n    };\n  };\n\n  // Admin functions\n  const loginAdmin = async (email, password) => {\n    try {\n      console.log(\"AuthContext: Starting admin login process...\");\n\n      // Clear only admin session data, keep user session intact\n      localStorage.removeItem(\"admin_token\");\n      localStorage.removeItem(\"admin_user\");\n      setAdminToken(null);\n      setAdminUser(null);\n\n      const API_BASE_URL =\n        process.env.REACT_APP_API_BASE_URL || \"http://localhost:8000\";\n      const response = await fetch(`${API_BASE_URL}/admin/login`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify({ email, password }),\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || \"Admin login failed\");\n      }\n\n      const data = await response.json();\n      console.log(\"AuthContext: Admin login successful\");\n\n      // Save to both sessionStorage (priority) and localStorage (backup)\n      sessionStorage.setItem(\"admin_token\", data.access_token);\n      sessionStorage.setItem(\n        \"admin_user\",\n        JSON.stringify({ email: data.admin_email })\n      );\n      localStorage.setItem(\"admin_token\", data.access_token);\n      localStorage.setItem(\n        \"admin_user\",\n        JSON.stringify({ email: data.admin_email })\n      );\n\n      // Update state\n      setAdminToken(data.access_token);\n      setAdminUser({ email: data.admin_email });\n\n      return data;\n    } catch (error) {\n      console.error(\"AuthContext: Admin login error:\", error);\n      throw error;\n    }\n  };\n\n  const logoutAdmin = () => {\n    console.log(\"AuthContext: Admin logout\");\n\n    // Clear both sessionStorage and localStorage\n    sessionStorage.removeItem(\"admin_token\");\n    sessionStorage.removeItem(\"admin_user\");\n    localStorage.removeItem(\"admin_token\");\n    localStorage.removeItem(\"admin_user\");\n\n    // Clear state\n    setAdminToken(null);\n    setAdminUser(null);\n  };\n\n  const getAdminAuthHeaders = () => {\n    if (adminToken && !isTokenExpired(adminToken)) {\n      return {\n        Authorization: `Bearer ${adminToken}`,\n        \"Content-Type\": \"application/json\",\n      };\n    } else if (adminToken && isTokenExpired(adminToken)) {\n      // Admin token is expired, clear it\n      console.log(\"AuthContext: Admin token expired, clearing authentication\");\n      logoutAdmin();\n      return {\n        \"Content-Type\": \"application/json\",\n      };\n    }\n    return {\n      \"Content-Type\": \"application/json\",\n    };\n  };\n\n  const getAdminAuthHeadersForUpload = () => {\n    if (adminToken && !isTokenExpired(adminToken)) {\n      return {\n        Authorization: `Bearer ${adminToken}`,\n        // Don't set Content-Type for FormData uploads\n      };\n    } else if (adminToken && isTokenExpired(adminToken)) {\n      // Admin token is expired, clear it\n      console.log(\"AuthContext: Admin token expired, clearing authentication\");\n      logoutAdmin();\n      return {};\n    }\n    return {};\n  };\n\n  // Session separation functions\n  const clearAllSessions = () => {\n    console.log(\"AuthContext: Clearing all sessions\");\n    sessionStorage.clear();\n    localStorage.clear();\n    setToken(null);\n    setUser(null);\n    setAdminToken(null);\n    setAdminUser(null);\n    setSessionType(null);\n  };\n\n  // Check if user has required session type for the route\n  const hasRequiredSession = (requiredType) => {\n    if (requiredType === \"user\") {\n      return !!user && !!token;\n    } else if (requiredType === \"admin\") {\n      return !!adminUser && !!adminToken;\n    }\n    return false;\n  };\n\n  // Check if user is trying to access wrong interface\n  const isWrongInterface = (currentPath, userType) => {\n    const isAdminPath = currentPath.startsWith(\"/admin\");\n    const isUserPath = !isAdminPath;\n\n    if (userType === \"user\" && isAdminPath) {\n      return true; // User trying to access admin interface\n    }\n    if (userType === \"admin\" && isUserPath) {\n      return true; // Admin trying to access user interface\n    }\n    return false;\n  };\n\n  const value = {\n    user,\n    token,\n    loading,\n    login,\n    logout,\n    forceLogout,\n    getAuthHeaders,\n    isAuthenticated: !!user,\n    // Admin functions\n    adminUser,\n    adminToken,\n    loginAdmin,\n    logoutAdmin,\n    getAdminAuthHeaders,\n    getAdminAuthHeadersForUpload,\n    isAdminAuthenticated: !!adminUser,\n    // Session separation\n    sessionType,\n    clearAllSessions,\n    hasRequiredSession,\n    isWrongInterface,\n  };\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,SAASC,SAAS,QAAQ,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,WAAW,gBAAGP,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMQ,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGT,UAAU,CAACM,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACe,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACA,MAAM,CAACmB,SAAS,EAAEC,YAAY,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACqB,UAAU,EAAEC,aAAa,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACA,MAAM,CAACuB,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;;EAEtD;EACA,MAAM,CAACyB,KAAK,CAAC,GAAGzB,QAAQ,CACtB,MAAM,OAAO0B,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EACpE,CAAC;;EAED;EACA9B,SAAS,CAAC,MAAM;IACd;EAAA,CACD,EAAE,CAACwB,KAAK,CAAC,CAAC;;EAEX;EACAxB,SAAS,CAAC,MAAM;IACd;IACA,MAAM+B,UAAU,GACdC,cAAc,CAACC,OAAO,CAAC,cAAc,CAAC,IACtCC,YAAY,CAACD,OAAO,CAAC,cAAc,CAAC;IACtC,MAAME,SAAS,GACbH,cAAc,CAACC,OAAO,CAAC,MAAM,CAAC,IAAIC,YAAY,CAACD,OAAO,CAAC,MAAM,CAAC;IAChE,MAAMG,eAAe,GACnBJ,cAAc,CAACC,OAAO,CAAC,aAAa,CAAC,IACrCC,YAAY,CAACD,OAAO,CAAC,aAAa,CAAC;IACrC,MAAMI,cAAc,GAClBL,cAAc,CAACC,OAAO,CAAC,YAAY,CAAC,IACpCC,YAAY,CAACD,OAAO,CAAC,YAAY,CAAC;;IAEpC;IACAK,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE;MACpDC,cAAc,EAAE,CAAC,CAACT,UAAU;MAC5BU,eAAe,EAAE,CAAC,CAACL;IACrB,CAAC,CAAC;IAEF,IAAIL,UAAU,IAAII,SAAS,EAAE;MAC3B,IAAI;QACF;QACA,MAAMO,OAAO,GAAGzC,SAAS,CAAC8B,UAAU,CAAC;QACrC,MAAMY,WAAW,GAAGlB,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI;QAErC,IAAIgB,OAAO,CAACE,GAAG,GAAGD,WAAW,EAAE;UAC7B5B,QAAQ,CAACgB,UAAU,CAAC;UACpBlB,OAAO,CAACgC,IAAI,CAACC,KAAK,CAACX,SAAS,CAAC,CAAC;QAChC,CAAC,MAAM;UACL;UACAD,YAAY,CAACa,UAAU,CAAC,cAAc,CAAC;UACvCb,YAAY,CAACa,UAAU,CAAC,MAAM,CAAC;QACjC;MACF,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdV,OAAO,CAACU,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7Cd,YAAY,CAACa,UAAU,CAAC,cAAc,CAAC;QACvCb,YAAY,CAACa,UAAU,CAAC,MAAM,CAAC;MACjC;IACF;IAEA,IAAIX,eAAe,IAAIC,cAAc,EAAE;MACrC,IAAI;QACF;QACA,MAAMK,OAAO,GAAGzC,SAAS,CAACmC,eAAe,CAAC;QAC1C,MAAMO,WAAW,GAAGlB,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI;QAErC,IAAIgB,OAAO,CAACE,GAAG,GAAGD,WAAW,EAAE;UAC7BtB,aAAa,CAACe,eAAe,CAAC;UAC9BjB,YAAY,CAAC0B,IAAI,CAACC,KAAK,CAACT,cAAc,CAAC,CAAC;QAC1C,CAAC,MAAM;UACL;UACAH,YAAY,CAACa,UAAU,CAAC,aAAa,CAAC;UACtCb,YAAY,CAACa,UAAU,CAAC,YAAY,CAAC;QACvC;MACF,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdV,OAAO,CAACU,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnDd,YAAY,CAACa,UAAU,CAAC,aAAa,CAAC;QACtCb,YAAY,CAACa,UAAU,CAAC,YAAY,CAAC;MACvC;IACF;IAEA9B,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMgC,KAAK,GAAG,MAAOC,WAAW,IAAK;IACnC,IAAI;MACFZ,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;;MAE1D;MACAL,YAAY,CAACa,UAAU,CAAC,cAAc,CAAC;MACvCb,YAAY,CAACa,UAAU,CAAC,MAAM,CAAC;MAC/BhC,QAAQ,CAAC,IAAI,CAAC;MACdF,OAAO,CAAC,IAAI,CAAC;MAEb,MAAMsC,YAAY,GAChBC,OAAO,CAACC,GAAG,CAACC,sBAAsB,IAAI,uBAAuB;MAC/DhB,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEY,YAAY,CAAC;MACxD,MAAMI,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGL,YAAY,cAAc,EAAE;QAC1DM,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEd,IAAI,CAACe,SAAS,CAAC;UAAE9C,KAAK,EAAEoC;QAAY,CAAC;MAC7C,CAAC,CAAC;MAEFZ,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEgB,QAAQ,CAACM,MAAM,CAAC;MAC7DvB,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEgB,QAAQ,CAACG,OAAO,CAAC;MAE/D,IAAI,CAACH,QAAQ,CAACO,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAMR,QAAQ,CAACS,IAAI,CAAC,CAAC;QACvC1B,OAAO,CAACU,KAAK,CAAC,8BAA8B,EAAEe,SAAS,CAAC;QACxDzB,OAAO,CAACU,KAAK,CAAC,6BAA6B,EAAEO,QAAQ,CAAC;QACtDjB,OAAO,CAACU,KAAK,CAAC,+BAA+B,EAAEO,QAAQ,CAACM,MAAM,CAAC;QAC/DvB,OAAO,CAACU,KAAK,CAAC,mCAAmC,EAAEO,QAAQ,CAACU,UAAU,CAAC;;QAEvE;QACA,IAAIC,SAAS,GAAG,IAAI;QACpB,IAAI;UACFA,SAAS,GAAGrB,IAAI,CAACC,KAAK,CAACiB,SAAS,CAAC;UACjCzB,OAAO,CAACU,KAAK,CAAC,iCAAiC,EAAEkB,SAAS,CAAC;QAC7D,CAAC,CAAC,OAAOC,CAAC,EAAE;UACV7B,OAAO,CAACU,KAAK,CAAC,sCAAsC,EAAEe,SAAS,CAAC;QAClE;;QAEA;QACA,IACEG,SAAS,IACTA,SAAS,CAACE,MAAM,IAChBF,SAAS,CAACE,MAAM,CAACC,QAAQ,CAAC,sBAAsB,CAAC,EACjD;UACA/B,OAAO,CAACC,GAAG,CACT,oEACF,CAAC;UACD,MAAM,IAAI+B,OAAO,CAAEC,OAAO,IAAKC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;UAEzD;UACA,MAAME,aAAa,GAAG,MAAMjB,KAAK,CAAC,GAAGL,YAAY,cAAc,EAAE;YAC/DM,MAAM,EAAE,MAAM;YACdC,OAAO,EAAE;cACP,cAAc,EAAE;YAClB,CAAC;YACDC,IAAI,EAAEd,IAAI,CAACe,SAAS,CAAC;cAAE9C,KAAK,EAAEoC;YAAY,CAAC;UAC7C,CAAC,CAAC;UAEF,IAAIuB,aAAa,CAACX,EAAE,EAAE;YACpB,MAAMY,SAAS,GAAG,MAAMD,aAAa,CAACE,IAAI,CAAC,CAAC;YAC5CrC,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;;YAE3D;YACAP,cAAc,CAAC4C,OAAO,CAAC,cAAc,EAAEF,SAAS,CAACG,YAAY,CAAC;YAC9D7C,cAAc,CAAC4C,OAAO,CAAC,MAAM,EAAE/B,IAAI,CAACe,SAAS,CAACc,SAAS,CAAC9D,IAAI,CAAC,CAAC;YAC9DsB,YAAY,CAAC0C,OAAO,CAAC,cAAc,EAAEF,SAAS,CAACG,YAAY,CAAC;YAC5D3C,YAAY,CAAC0C,OAAO,CAAC,MAAM,EAAE/B,IAAI,CAACe,SAAS,CAACc,SAAS,CAAC9D,IAAI,CAAC,CAAC;;YAE5D;YACAG,QAAQ,CAAC2D,SAAS,CAACG,YAAY,CAAC;YAChChE,OAAO,CAAC6D,SAAS,CAAC9D,IAAI,CAAC;YAEvB0B,OAAO,CAACC,GAAG,CACT,uDACF,CAAC;YACD,OAAOmC,SAAS;UAClB;QACF;QAEA,MAAM,IAAIlE,KAAK,CACb,0BAA0B+C,QAAQ,CAACM,MAAM,MAAME,SAAS,EAC1D,CAAC;MACH;MAEA,MAAMe,IAAI,GAAG,MAAMvB,QAAQ,CAACoB,IAAI,CAAC,CAAC;MAClCrC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEuC,IAAI,CAAC;;MAEhD;MACA9C,cAAc,CAAC4C,OAAO,CAAC,cAAc,EAAEE,IAAI,CAACD,YAAY,CAAC;MACzD7C,cAAc,CAAC4C,OAAO,CAAC,MAAM,EAAE/B,IAAI,CAACe,SAAS,CAACkB,IAAI,CAAClE,IAAI,CAAC,CAAC;MACzDsB,YAAY,CAAC0C,OAAO,CAAC,cAAc,EAAEE,IAAI,CAACD,YAAY,CAAC;MACvD3C,YAAY,CAAC0C,OAAO,CAAC,MAAM,EAAE/B,IAAI,CAACe,SAAS,CAACkB,IAAI,CAAClE,IAAI,CAAC,CAAC;;MAEvD;MACAG,QAAQ,CAAC+D,IAAI,CAACD,YAAY,CAAC;MAC3BhE,OAAO,CAACiE,IAAI,CAAClE,IAAI,CAAC;MAElB0B,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;MACxD,OAAOuC,IAAI;IACb,CAAC,CAAC,OAAO9B,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD;MACA+B,WAAW,CAAC,CAAC;MACb,MAAM/B,KAAK;IACb;EACF,CAAC;EAED,MAAMgC,MAAM,GAAG,MAAAA,CAAA,KAAY;IACzB1C,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;IAEtD,IAAI;MACF;MACA,IAAIzB,KAAK,EAAE;QACT,MAAMqC,YAAY,GAChBC,OAAO,CAACC,GAAG,CAACC,sBAAsB,IAAI,uBAAuB;QAC/DhB,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;QAE9D,MAAMgB,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGL,YAAY,cAAc,EAAE;UAC1DM,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClCuB,aAAa,EAAE,UAAUnE,KAAK;UAChC;QACF,CAAC,CAAC;QAEF,IAAIyC,QAAQ,CAACO,EAAE,EAAE;UACf,MAAMgB,IAAI,GAAG,MAAMvB,QAAQ,CAACoB,IAAI,CAAC,CAAC;UAClCrC,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEuC,IAAI,CAAC;UAC5DxC,OAAO,CAACC,GAAG,CACT,4BAA4BuC,IAAI,CAACI,oBAAoB,WACvD,CAAC;QACH,CAAC,MAAM;UACL5C,OAAO,CAAC6C,IAAI,CACV,yEACF,CAAC;QACH;MACF;IACF,CAAC,CAAC,OAAOnC,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;MAClEV,OAAO,CAACC,GAAG,CACT,oEACF,CAAC;IACH;;IAEA;IACAP,cAAc,CAACe,UAAU,CAAC,cAAc,CAAC;IACzCf,cAAc,CAACe,UAAU,CAAC,MAAM,CAAC;IACjCb,YAAY,CAACa,UAAU,CAAC,cAAc,CAAC;IACvCb,YAAY,CAACa,UAAU,CAAC,MAAM,CAAC;;IAE/B;IACAhC,QAAQ,CAAC,IAAI,CAAC;IACdF,OAAO,CAAC,IAAI,CAAC;;IAEb;IACA,IAAIuE,MAAM,CAACC,MAAM,IAAID,MAAM,CAACC,MAAM,CAACC,QAAQ,EAAE;MAC3ChD,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;MACtD,IAAI;QACF;QACA6C,MAAM,CAACC,MAAM,CAACC,QAAQ,CAACC,EAAE,CAACC,iBAAiB,CAAC,CAAC;;QAE7C;QACAJ,MAAM,CAACC,MAAM,CAACC,QAAQ,CAACC,EAAE,CAACE,MAAM,CAAC3E,KAAK,EAAG4E,IAAI,IAAK;UAChDpD,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEmD,IAAI,CAAC;QACzD,CAAC,CAAC;;QAEF;QACAC,QAAQ,CAACC,MAAM,GACb,8EAA8E;QAChFD,QAAQ,CAACC,MAAM,GACb,mFAAmF;MACvF,CAAC,CAAC,OAAO5C,KAAK,EAAE;QACdV,OAAO,CAAC6C,IAAI,CAAC,4CAA4C,EAAEnC,KAAK,CAAC;MACnE;IACF;IAEAV,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;EAC9C,CAAC;EAED,MAAMwC,WAAW,GAAGA,CAAA,KAAM;IACxBzC,OAAO,CAACC,GAAG,CAAC,8DAA8D,CAAC;;IAE3E;IACAL,YAAY,CAACa,UAAU,CAAC,cAAc,CAAC;IACvCb,YAAY,CAACa,UAAU,CAAC,MAAM,CAAC;;IAE/B;IACAhC,QAAQ,CAAC,IAAI,CAAC;IACdF,OAAO,CAAC,IAAI,CAAC;;IAEb;IACA,IAAIuE,MAAM,CAACC,MAAM,IAAID,MAAM,CAACC,MAAM,CAACC,QAAQ,EAAE;MAC3C,IAAI;QACFF,MAAM,CAACC,MAAM,CAACC,QAAQ,CAACC,EAAE,CAACC,iBAAiB,CAAC,CAAC;MAC/C,CAAC,CAAC,OAAOxC,KAAK,EAAE;QACdV,OAAO,CAAC6C,IAAI,CAAC,kDAAkD,EAAEnC,KAAK,CAAC;MACzE;IACF;IAEAV,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;EACpD,CAAC;EAED,MAAMsD,cAAc,GAAI/E,KAAK,IAAK;IAChC,IAAI,CAACA,KAAK,EAAE,OAAO,IAAI;IAEvB,IAAI;MACF;MACA,MAAMgF,OAAO,GAAGjD,IAAI,CAACC,KAAK,CAACiD,IAAI,CAACjF,KAAK,CAACkF,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrD,MAAMrD,WAAW,GAAGhB,IAAI,CAACsE,KAAK,CAACxE,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;;MAEjD;MACA,OAAOoE,OAAO,CAAClD,GAAG,GAAGD,WAAW,GAAG,EAAE;IACvC,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACjE,OAAO,IAAI,CAAC,CAAC;IACf;EACF,CAAC;EAED,MAAMkD,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIpF,KAAK,IAAI,CAAC+E,cAAc,CAAC/E,KAAK,CAAC,EAAE;MACnC,OAAO;QACLmE,aAAa,EAAE,UAAUnE,KAAK,EAAE;QAChC,cAAc,EAAE;MAClB,CAAC;IACH,CAAC,MAAM,IAAIA,KAAK,IAAI+E,cAAc,CAAC/E,KAAK,CAAC,EAAE;MACzC;MACAwB,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;MAClEyC,MAAM,CAAC,CAAC;MACR,OAAO;QACL,cAAc,EAAE;MAClB,CAAC;IACH;IACA,OAAO;MACL,cAAc,EAAE;IAClB,CAAC;EACH,CAAC;;EAED;EACA,MAAMmB,UAAU,GAAG,MAAAA,CAAOC,KAAK,EAAEC,QAAQ,KAAK;IAC5C,IAAI;MACF/D,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;;MAE3D;MACAL,YAAY,CAACa,UAAU,CAAC,aAAa,CAAC;MACtCb,YAAY,CAACa,UAAU,CAAC,YAAY,CAAC;MACrC1B,aAAa,CAAC,IAAI,CAAC;MACnBF,YAAY,CAAC,IAAI,CAAC;MAElB,MAAMgC,YAAY,GAChBC,OAAO,CAACC,GAAG,CAACC,sBAAsB,IAAI,uBAAuB;MAC/D,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGL,YAAY,cAAc,EAAE;QAC1DM,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEd,IAAI,CAACe,SAAS,CAAC;UAAEwC,KAAK;UAAEC;QAAS,CAAC;MAC1C,CAAC,CAAC;MAEF,IAAI,CAAC9C,QAAQ,CAACO,EAAE,EAAE;QAChB,MAAMwC,SAAS,GAAG,MAAM/C,QAAQ,CAACoB,IAAI,CAAC,CAAC;QACvC,MAAM,IAAInE,KAAK,CAAC8F,SAAS,CAAClC,MAAM,IAAI,oBAAoB,CAAC;MAC3D;MAEA,MAAMU,IAAI,GAAG,MAAMvB,QAAQ,CAACoB,IAAI,CAAC,CAAC;MAClCrC,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;;MAElD;MACAP,cAAc,CAAC4C,OAAO,CAAC,aAAa,EAAEE,IAAI,CAACD,YAAY,CAAC;MACxD7C,cAAc,CAAC4C,OAAO,CACpB,YAAY,EACZ/B,IAAI,CAACe,SAAS,CAAC;QAAEwC,KAAK,EAAEtB,IAAI,CAACyB;MAAY,CAAC,CAC5C,CAAC;MACDrE,YAAY,CAAC0C,OAAO,CAAC,aAAa,EAAEE,IAAI,CAACD,YAAY,CAAC;MACtD3C,YAAY,CAAC0C,OAAO,CAClB,YAAY,EACZ/B,IAAI,CAACe,SAAS,CAAC;QAAEwC,KAAK,EAAEtB,IAAI,CAACyB;MAAY,CAAC,CAC5C,CAAC;;MAED;MACAlF,aAAa,CAACyD,IAAI,CAACD,YAAY,CAAC;MAChC1D,YAAY,CAAC;QAAEiF,KAAK,EAAEtB,IAAI,CAACyB;MAAY,CAAC,CAAC;MAEzC,OAAOzB,IAAI;IACb,CAAC,CAAC,OAAO9B,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAMwD,WAAW,GAAGA,CAAA,KAAM;IACxBlE,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;;IAExC;IACAP,cAAc,CAACe,UAAU,CAAC,aAAa,CAAC;IACxCf,cAAc,CAACe,UAAU,CAAC,YAAY,CAAC;IACvCb,YAAY,CAACa,UAAU,CAAC,aAAa,CAAC;IACtCb,YAAY,CAACa,UAAU,CAAC,YAAY,CAAC;;IAErC;IACA1B,aAAa,CAAC,IAAI,CAAC;IACnBF,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMsF,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAIrF,UAAU,IAAI,CAACyE,cAAc,CAACzE,UAAU,CAAC,EAAE;MAC7C,OAAO;QACL6D,aAAa,EAAE,UAAU7D,UAAU,EAAE;QACrC,cAAc,EAAE;MAClB,CAAC;IACH,CAAC,MAAM,IAAIA,UAAU,IAAIyE,cAAc,CAACzE,UAAU,CAAC,EAAE;MACnD;MACAkB,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;MACxEiE,WAAW,CAAC,CAAC;MACb,OAAO;QACL,cAAc,EAAE;MAClB,CAAC;IACH;IACA,OAAO;MACL,cAAc,EAAE;IAClB,CAAC;EACH,CAAC;EAED,MAAME,4BAA4B,GAAGA,CAAA,KAAM;IACzC,IAAItF,UAAU,IAAI,CAACyE,cAAc,CAACzE,UAAU,CAAC,EAAE;MAC7C,OAAO;QACL6D,aAAa,EAAE,UAAU7D,UAAU;QACnC;MACF,CAAC;IACH,CAAC,MAAM,IAAIA,UAAU,IAAIyE,cAAc,CAACzE,UAAU,CAAC,EAAE;MACnD;MACAkB,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;MACxEiE,WAAW,CAAC,CAAC;MACb,OAAO,CAAC,CAAC;IACX;IACA,OAAO,CAAC,CAAC;EACX,CAAC;;EAED;EACA,MAAMG,gBAAgB,GAAGA,CAAA,KAAM;IAC7BrE,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;IACjDP,cAAc,CAAC4E,KAAK,CAAC,CAAC;IACtB1E,YAAY,CAAC0E,KAAK,CAAC,CAAC;IACpB7F,QAAQ,CAAC,IAAI,CAAC;IACdF,OAAO,CAAC,IAAI,CAAC;IACbQ,aAAa,CAAC,IAAI,CAAC;IACnBF,YAAY,CAAC,IAAI,CAAC;IAClBI,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;;EAED;EACA,MAAMsF,kBAAkB,GAAIC,YAAY,IAAK;IAC3C,IAAIA,YAAY,KAAK,MAAM,EAAE;MAC3B,OAAO,CAAC,CAAClG,IAAI,IAAI,CAAC,CAACE,KAAK;IAC1B,CAAC,MAAM,IAAIgG,YAAY,KAAK,OAAO,EAAE;MACnC,OAAO,CAAC,CAAC5F,SAAS,IAAI,CAAC,CAACE,UAAU;IACpC;IACA,OAAO,KAAK;EACd,CAAC;;EAED;EACA,MAAM2F,gBAAgB,GAAGA,CAACC,WAAW,EAAEC,QAAQ,KAAK;IAClD,MAAMC,WAAW,GAAGF,WAAW,CAACG,UAAU,CAAC,QAAQ,CAAC;IACpD,MAAMC,UAAU,GAAG,CAACF,WAAW;IAE/B,IAAID,QAAQ,KAAK,MAAM,IAAIC,WAAW,EAAE;MACtC,OAAO,IAAI,CAAC,CAAC;IACf;IACA,IAAID,QAAQ,KAAK,OAAO,IAAIG,UAAU,EAAE;MACtC,OAAO,IAAI,CAAC,CAAC;IACf;IACA,OAAO,KAAK;EACd,CAAC;EAED,MAAMC,KAAK,GAAG;IACZzG,IAAI;IACJE,KAAK;IACLE,OAAO;IACPiC,KAAK;IACL+B,MAAM;IACND,WAAW;IACXmB,cAAc;IACdoB,eAAe,EAAE,CAAC,CAAC1G,IAAI;IACvB;IACAM,SAAS;IACTE,UAAU;IACV+E,UAAU;IACVK,WAAW;IACXC,mBAAmB;IACnBC,4BAA4B;IAC5Ba,oBAAoB,EAAE,CAAC,CAACrG,SAAS;IACjC;IACAI,WAAW;IACXqF,gBAAgB;IAChBE,kBAAkB;IAClBE;EACF,CAAC;EAED,oBAAO5G,OAAA,CAACC,WAAW,CAACoH,QAAQ;IAACH,KAAK,EAAEA,KAAM;IAAA3G,QAAA,EAAEA;EAAQ;IAAA+G,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAuB,CAAC;AAC9E,CAAC;AAACjH,GAAA,CAxeWF,YAAY;AAAAoH,EAAA,GAAZpH,YAAY;AAAA,IAAAoH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}