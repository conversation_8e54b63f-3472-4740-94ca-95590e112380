{"ast": null, "code": "\"use client\";\n\n// src/switch.tsx\nimport * as React from \"react\";\nimport { composeEventHandlers } from \"@radix-ui/primitive\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport { useControllableState } from \"@radix-ui/react-use-controllable-state\";\nimport { usePrevious } from \"@radix-ui/react-use-previous\";\nimport { useSize } from \"@radix-ui/react-use-size\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar SWITCH_NAME = \"Switch\";\nvar [createSwitchContext, createSwitchScope] = createContextScope(SWITCH_NAME);\nvar [SwitchProvider, useSwitchContext] = createSwitchContext(SWITCH_NAME);\nvar Switch = React.forwardRef((props, forwardedRef) => {\n  const {\n    __scopeSwitch,\n    name,\n    checked: checkedProp,\n    defaultChecked,\n    required,\n    disabled,\n    value = \"on\",\n    onCheckedChange,\n    form,\n    ...switchProps\n  } = props;\n  const [button, setButton] = React.useState(null);\n  const composedRefs = useComposedRefs(forwardedRef, node => setButton(node));\n  const hasConsumerStoppedPropagationRef = React.useRef(false);\n  const isFormControl = button ? form || !!button.closest(\"form\") : true;\n  const [checked, setChecked] = useControllableState({\n    prop: checkedProp,\n    defaultProp: defaultChecked ?? false,\n    onChange: onCheckedChange,\n    caller: SWITCH_NAME\n  });\n  return /* @__PURE__ */jsxs(SwitchProvider, {\n    scope: __scopeSwitch,\n    checked,\n    disabled,\n    children: [/* @__PURE__ */jsx(Primitive.button, {\n      type: \"button\",\n      role: \"switch\",\n      \"aria-checked\": checked,\n      \"aria-required\": required,\n      \"data-state\": getState(checked),\n      \"data-disabled\": disabled ? \"\" : void 0,\n      disabled,\n      value,\n      ...switchProps,\n      ref: composedRefs,\n      onClick: composeEventHandlers(props.onClick, event => {\n        setChecked(prevChecked => !prevChecked);\n        if (isFormControl) {\n          hasConsumerStoppedPropagationRef.current = event.isPropagationStopped();\n          if (!hasConsumerStoppedPropagationRef.current) event.stopPropagation();\n        }\n      })\n    }), isFormControl && /* @__PURE__ */jsx(SwitchBubbleInput, {\n      control: button,\n      bubbles: !hasConsumerStoppedPropagationRef.current,\n      name,\n      value,\n      checked,\n      required,\n      disabled,\n      form,\n      style: {\n        transform: \"translateX(-100%)\"\n      }\n    })]\n  });\n});\nSwitch.displayName = SWITCH_NAME;\nvar THUMB_NAME = \"SwitchThumb\";\nvar SwitchThumb = React.forwardRef((props, forwardedRef) => {\n  const {\n    __scopeSwitch,\n    ...thumbProps\n  } = props;\n  const context = useSwitchContext(THUMB_NAME, __scopeSwitch);\n  return /* @__PURE__ */jsx(Primitive.span, {\n    \"data-state\": getState(context.checked),\n    \"data-disabled\": context.disabled ? \"\" : void 0,\n    ...thumbProps,\n    ref: forwardedRef\n  });\n});\nSwitchThumb.displayName = THUMB_NAME;\nvar BUBBLE_INPUT_NAME = \"SwitchBubbleInput\";\nvar SwitchBubbleInput = React.forwardRef(({\n  __scopeSwitch,\n  control,\n  checked,\n  bubbles = true,\n  ...props\n}, forwardedRef) => {\n  const ref = React.useRef(null);\n  const composedRefs = useComposedRefs(ref, forwardedRef);\n  const prevChecked = usePrevious(checked);\n  const controlSize = useSize(control);\n  React.useEffect(() => {\n    const input = ref.current;\n    if (!input) return;\n    const inputProto = window.HTMLInputElement.prototype;\n    const descriptor = Object.getOwnPropertyDescriptor(inputProto, \"checked\");\n    const setChecked = descriptor.set;\n    if (prevChecked !== checked && setChecked) {\n      const event = new Event(\"click\", {\n        bubbles\n      });\n      setChecked.call(input, checked);\n      input.dispatchEvent(event);\n    }\n  }, [prevChecked, checked, bubbles]);\n  return /* @__PURE__ */jsx(\"input\", {\n    type: \"checkbox\",\n    \"aria-hidden\": true,\n    defaultChecked: checked,\n    ...props,\n    tabIndex: -1,\n    ref: composedRefs,\n    style: {\n      ...props.style,\n      ...controlSize,\n      position: \"absolute\",\n      pointerEvents: \"none\",\n      opacity: 0,\n      margin: 0\n    }\n  });\n});\nSwitchBubbleInput.displayName = BUBBLE_INPUT_NAME;\nfunction getState(checked) {\n  return checked ? \"checked\" : \"unchecked\";\n}\nvar Root = Switch;\nvar Thumb = SwitchThumb;\nexport { Root, Switch, SwitchThumb, Thumb, createSwitchScope };", "map": {"version": 3, "names": ["React", "composeEventHandlers", "useComposedRefs", "createContextScope", "useControllableState", "usePrevious", "useSize", "Primitive", "jsx", "jsxs", "SWITCH_NAME", "createSwitchContext", "createSwitchScope", "SwitchProvider", "useSwitchContext", "Switch", "forwardRef", "props", "forwardedRef", "__scopeSwitch", "name", "checked", "checkedProp", "defaultChecked", "required", "disabled", "value", "onCheckedChange", "form", "switchProps", "button", "setButton", "useState", "composedRefs", "node", "hasConsumerStoppedPropagationRef", "useRef", "isFormControl", "closest", "setChecked", "prop", "defaultProp", "onChange", "caller", "scope", "children", "type", "role", "getState", "ref", "onClick", "event", "prevChecked", "current", "isPropagationStopped", "stopPropagation", "SwitchBubbleInput", "control", "bubbles", "style", "transform", "displayName", "THUMB_NAME", "SwitchThumb", "thumbProps", "context", "span", "BUBBLE_INPUT_NAME", "controlSize", "useEffect", "input", "inputProto", "window", "HTMLInputElement", "prototype", "descriptor", "Object", "getOwnPropertyDescriptor", "set", "Event", "call", "dispatchEvent", "tabIndex", "position", "pointerEvents", "opacity", "margin", "Root", "Thumb"], "sources": ["D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\node_modules\\@radix-ui\\react-switch\\src\\switch.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { usePrevious } from '@radix-ui/react-use-previous';\nimport { useSize } from '@radix-ui/react-use-size';\nimport { Primitive } from '@radix-ui/react-primitive';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Switch\n * -----------------------------------------------------------------------------------------------*/\n\nconst SWITCH_NAME = 'Switch';\n\ntype ScopedProps<P> = P & { __scopeSwitch?: Scope };\nconst [createSwitchContext, createSwitchScope] = createContextScope(SWITCH_NAME);\n\ntype SwitchContextValue = { checked: boolean; disabled?: boolean };\nconst [SwitchProvider, useSwitchContext] = createSwitchContext<SwitchContextValue>(SWITCH_NAME);\n\ntype SwitchElement = React.ComponentRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface SwitchProps extends PrimitiveButtonProps {\n  checked?: boolean;\n  defaultChecked?: boolean;\n  required?: boolean;\n  onCheckedChange?(checked: boolean): void;\n}\n\nconst Switch = React.forwardRef<SwitchElement, SwitchProps>(\n  (props: ScopedProps<SwitchProps>, forwardedRef) => {\n    const {\n      __scopeSwitch,\n      name,\n      checked: checkedProp,\n      defaultChecked,\n      required,\n      disabled,\n      value = 'on',\n      onCheckedChange,\n      form,\n      ...switchProps\n    } = props;\n    const [button, setButton] = React.useState<HTMLButtonElement | null>(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setButton(node));\n    const hasConsumerStoppedPropagationRef = React.useRef(false);\n    // We set this to true by default so that events bubble to forms without JS (SSR)\n    const isFormControl = button ? form || !!button.closest('form') : true;\n    const [checked, setChecked] = useControllableState({\n      prop: checkedProp,\n      defaultProp: defaultChecked ?? false,\n      onChange: onCheckedChange,\n      caller: SWITCH_NAME,\n    });\n\n    return (\n      <SwitchProvider scope={__scopeSwitch} checked={checked} disabled={disabled}>\n        <Primitive.button\n          type=\"button\"\n          role=\"switch\"\n          aria-checked={checked}\n          aria-required={required}\n          data-state={getState(checked)}\n          data-disabled={disabled ? '' : undefined}\n          disabled={disabled}\n          value={value}\n          {...switchProps}\n          ref={composedRefs}\n          onClick={composeEventHandlers(props.onClick, (event) => {\n            setChecked((prevChecked) => !prevChecked);\n            if (isFormControl) {\n              hasConsumerStoppedPropagationRef.current = event.isPropagationStopped();\n              // if switch is in a form, stop propagation from the button so that we only propagate\n              // one click event (from the input). We propagate changes from an input so that native\n              // form validation works and form events reflect switch updates.\n              if (!hasConsumerStoppedPropagationRef.current) event.stopPropagation();\n            }\n          })}\n        />\n        {isFormControl && (\n          <SwitchBubbleInput\n            control={button}\n            bubbles={!hasConsumerStoppedPropagationRef.current}\n            name={name}\n            value={value}\n            checked={checked}\n            required={required}\n            disabled={disabled}\n            form={form}\n            // We transform because the input is absolutely positioned but we have\n            // rendered it **after** the button. This pulls it back to sit on top\n            // of the button.\n            style={{ transform: 'translateX(-100%)' }}\n          />\n        )}\n      </SwitchProvider>\n    );\n  }\n);\n\nSwitch.displayName = SWITCH_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SwitchThumb\n * -----------------------------------------------------------------------------------------------*/\n\nconst THUMB_NAME = 'SwitchThumb';\n\ntype SwitchThumbElement = React.ComponentRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface SwitchThumbProps extends PrimitiveSpanProps {}\n\nconst SwitchThumb = React.forwardRef<SwitchThumbElement, SwitchThumbProps>(\n  (props: ScopedProps<SwitchThumbProps>, forwardedRef) => {\n    const { __scopeSwitch, ...thumbProps } = props;\n    const context = useSwitchContext(THUMB_NAME, __scopeSwitch);\n    return (\n      <Primitive.span\n        data-state={getState(context.checked)}\n        data-disabled={context.disabled ? '' : undefined}\n        {...thumbProps}\n        ref={forwardedRef}\n      />\n    );\n  }\n);\n\nSwitchThumb.displayName = THUMB_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SwitchBubbleInput\n * -----------------------------------------------------------------------------------------------*/\n\nconst BUBBLE_INPUT_NAME = 'SwitchBubbleInput';\n\ntype InputProps = React.ComponentPropsWithoutRef<typeof Primitive.input>;\ninterface SwitchBubbleInputProps extends Omit<InputProps, 'checked'> {\n  checked: boolean;\n  control: HTMLElement | null;\n  bubbles: boolean;\n}\n\nconst SwitchBubbleInput = React.forwardRef<HTMLInputElement, SwitchBubbleInputProps>(\n  (\n    {\n      __scopeSwitch,\n      control,\n      checked,\n      bubbles = true,\n      ...props\n    }: ScopedProps<SwitchBubbleInputProps>,\n    forwardedRef\n  ) => {\n    const ref = React.useRef<HTMLInputElement>(null);\n    const composedRefs = useComposedRefs(ref, forwardedRef);\n    const prevChecked = usePrevious(checked);\n    const controlSize = useSize(control);\n\n    // Bubble checked change to parents (e.g form change event)\n    React.useEffect(() => {\n      const input = ref.current;\n      if (!input) return;\n\n      const inputProto = window.HTMLInputElement.prototype;\n      const descriptor = Object.getOwnPropertyDescriptor(\n        inputProto,\n        'checked'\n      ) as PropertyDescriptor;\n      const setChecked = descriptor.set;\n      if (prevChecked !== checked && setChecked) {\n        const event = new Event('click', { bubbles });\n        setChecked.call(input, checked);\n        input.dispatchEvent(event);\n      }\n    }, [prevChecked, checked, bubbles]);\n\n    return (\n      <input\n        type=\"checkbox\"\n        aria-hidden\n        defaultChecked={checked}\n        {...props}\n        tabIndex={-1}\n        ref={composedRefs}\n        style={{\n          ...props.style,\n          ...controlSize,\n          position: 'absolute',\n          pointerEvents: 'none',\n          opacity: 0,\n          margin: 0,\n        }}\n      />\n    );\n  }\n);\n\nSwitchBubbleInput.displayName = BUBBLE_INPUT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction getState(checked: boolean) {\n  return checked ? 'checked' : 'unchecked';\n}\n\nconst Root = Switch;\nconst Thumb = SwitchThumb;\n\nexport {\n  createSwitchScope,\n  //\n  Switch,\n  SwitchThumb,\n  //\n  Root,\n  Thumb,\n};\nexport type { SwitchProps, SwitchThumbProps };\n"], "mappings": ";;;AAAA,YAAYA,KAAA,MAAW;AACvB,SAASC,oBAAA,QAA4B;AACrC,SAASC,eAAA,QAAuB;AAChC,SAASC,kBAAA,QAA0B;AACnC,SAASC,oBAAA,QAA4B;AACrC,SAASC,WAAA,QAAmB;AAC5B,SAASC,OAAA,QAAe;AACxB,SAASC,SAAA,QAAiB;AAoDpB,SACEC,GAAA,EADFC,IAAA;AA5CN,IAAMC,WAAA,GAAc;AAGpB,IAAM,CAACC,mBAAA,EAAqBC,iBAAiB,IAAIT,kBAAA,CAAmBO,WAAW;AAG/E,IAAM,CAACG,cAAA,EAAgBC,gBAAgB,IAAIH,mBAAA,CAAwCD,WAAW;AAW9F,IAAMK,MAAA,GAAef,KAAA,CAAAgB,UAAA,CACnB,CAACC,KAAA,EAAiCC,YAAA,KAAiB;EACjD,MAAM;IACJC,aAAA;IACAC,IAAA;IACAC,OAAA,EAASC,WAAA;IACTC,cAAA;IACAC,QAAA;IACAC,QAAA;IACAC,KAAA,GAAQ;IACRC,eAAA;IACAC,IAAA;IACA,GAAGC;EACL,IAAIZ,KAAA;EACJ,MAAM,CAACa,MAAA,EAAQC,SAAS,IAAU/B,KAAA,CAAAgC,QAAA,CAAmC,IAAI;EACzE,MAAMC,YAAA,GAAe/B,eAAA,CAAgBgB,YAAA,EAAegB,IAAA,IAASH,SAAA,CAAUG,IAAI,CAAC;EAC5E,MAAMC,gCAAA,GAAyCnC,KAAA,CAAAoC,MAAA,CAAO,KAAK;EAE3D,MAAMC,aAAA,GAAgBP,MAAA,GAASF,IAAA,IAAQ,CAAC,CAACE,MAAA,CAAOQ,OAAA,CAAQ,MAAM,IAAI;EAClE,MAAM,CAACjB,OAAA,EAASkB,UAAU,IAAInC,oBAAA,CAAqB;IACjDoC,IAAA,EAAMlB,WAAA;IACNmB,WAAA,EAAalB,cAAA,IAAkB;IAC/BmB,QAAA,EAAUf,eAAA;IACVgB,MAAA,EAAQjC;EACV,CAAC;EAED,OACE,eAAAD,IAAA,CAACI,cAAA;IAAe+B,KAAA,EAAOzB,aAAA;IAAeE,OAAA;IAAkBI,QAAA;IACtDoB,QAAA,kBAAArC,GAAA,CAACD,SAAA,CAAUuB,MAAA,EAAV;MACCgB,IAAA,EAAK;MACLC,IAAA,EAAK;MACL,gBAAc1B,OAAA;MACd,iBAAeG,QAAA;MACf,cAAYwB,QAAA,CAAS3B,OAAO;MAC5B,iBAAeI,QAAA,GAAW,KAAK;MAC/BA,QAAA;MACAC,KAAA;MACC,GAAGG,WAAA;MACJoB,GAAA,EAAKhB,YAAA;MACLiB,OAAA,EAASjD,oBAAA,CAAqBgB,KAAA,CAAMiC,OAAA,EAAUC,KAAA,IAAU;QACtDZ,UAAA,CAAYa,WAAA,IAAgB,CAACA,WAAW;QACxC,IAAIf,aAAA,EAAe;UACjBF,gCAAA,CAAiCkB,OAAA,GAAUF,KAAA,CAAMG,oBAAA,CAAqB;UAItE,IAAI,CAACnB,gCAAA,CAAiCkB,OAAA,EAASF,KAAA,CAAMI,eAAA,CAAgB;QACvE;MACF,CAAC;IAAA,CACH,GACClB,aAAA,IACC,eAAA7B,GAAA,CAACgD,iBAAA;MACCC,OAAA,EAAS3B,MAAA;MACT4B,OAAA,EAAS,CAACvB,gCAAA,CAAiCkB,OAAA;MAC3CjC,IAAA;MACAM,KAAA;MACAL,OAAA;MACAG,QAAA;MACAC,QAAA;MACAG,IAAA;MAIA+B,KAAA,EAAO;QAAEC,SAAA,EAAW;MAAoB;IAAA,CAC1C;EAAA,CAEJ;AAEJ,CACF;AAEA7C,MAAA,CAAO8C,WAAA,GAAcnD,WAAA;AAMrB,IAAMoD,UAAA,GAAa;AAMnB,IAAMC,WAAA,GAAoB/D,KAAA,CAAAgB,UAAA,CACxB,CAACC,KAAA,EAAsCC,YAAA,KAAiB;EACtD,MAAM;IAAEC,aAAA;IAAe,GAAG6C;EAAW,IAAI/C,KAAA;EACzC,MAAMgD,OAAA,GAAUnD,gBAAA,CAAiBgD,UAAA,EAAY3C,aAAa;EAC1D,OACE,eAAAX,GAAA,CAACD,SAAA,CAAU2D,IAAA,EAAV;IACC,cAAYlB,QAAA,CAASiB,OAAA,CAAQ5C,OAAO;IACpC,iBAAe4C,OAAA,CAAQxC,QAAA,GAAW,KAAK;IACtC,GAAGuC,UAAA;IACJf,GAAA,EAAK/B;EAAA,CACP;AAEJ,CACF;AAEA6C,WAAA,CAAYF,WAAA,GAAcC,UAAA;AAM1B,IAAMK,iBAAA,GAAoB;AAS1B,IAAMX,iBAAA,GAA0BxD,KAAA,CAAAgB,UAAA,CAC9B,CACE;EACEG,aAAA;EACAsC,OAAA;EACApC,OAAA;EACAqC,OAAA,GAAU;EACV,GAAGzC;AACL,GACAC,YAAA,KACG;EACH,MAAM+B,GAAA,GAAYjD,KAAA,CAAAoC,MAAA,CAAyB,IAAI;EAC/C,MAAMH,YAAA,GAAe/B,eAAA,CAAgB+C,GAAA,EAAK/B,YAAY;EACtD,MAAMkC,WAAA,GAAc/C,WAAA,CAAYgB,OAAO;EACvC,MAAM+C,WAAA,GAAc9D,OAAA,CAAQmD,OAAO;EAG7BzD,KAAA,CAAAqE,SAAA,CAAU,MAAM;IACpB,MAAMC,KAAA,GAAQrB,GAAA,CAAII,OAAA;IAClB,IAAI,CAACiB,KAAA,EAAO;IAEZ,MAAMC,UAAA,GAAaC,MAAA,CAAOC,gBAAA,CAAiBC,SAAA;IAC3C,MAAMC,UAAA,GAAaC,MAAA,CAAOC,wBAAA,CACxBN,UAAA,EACA,SACF;IACA,MAAMhC,UAAA,GAAaoC,UAAA,CAAWG,GAAA;IAC9B,IAAI1B,WAAA,KAAgB/B,OAAA,IAAWkB,UAAA,EAAY;MACzC,MAAMY,KAAA,GAAQ,IAAI4B,KAAA,CAAM,SAAS;QAAErB;MAAQ,CAAC;MAC5CnB,UAAA,CAAWyC,IAAA,CAAKV,KAAA,EAAOjD,OAAO;MAC9BiD,KAAA,CAAMW,aAAA,CAAc9B,KAAK;IAC3B;EACF,GAAG,CAACC,WAAA,EAAa/B,OAAA,EAASqC,OAAO,CAAC;EAElC,OACE,eAAAlD,GAAA,CAAC;IACCsC,IAAA,EAAK;IACL,eAAW;IACXvB,cAAA,EAAgBF,OAAA;IACf,GAAGJ,KAAA;IACJiE,QAAA,EAAU;IACVjC,GAAA,EAAKhB,YAAA;IACL0B,KAAA,EAAO;MACL,GAAG1C,KAAA,CAAM0C,KAAA;MACT,GAAGS,WAAA;MACHe,QAAA,EAAU;MACVC,aAAA,EAAe;MACfC,OAAA,EAAS;MACTC,MAAA,EAAQ;IACV;EAAA,CACF;AAEJ,CACF;AAEA9B,iBAAA,CAAkBK,WAAA,GAAcM,iBAAA;AAIhC,SAASnB,SAAS3B,OAAA,EAAkB;EAClC,OAAOA,OAAA,GAAU,YAAY;AAC/B;AAEA,IAAMkE,IAAA,GAAOxE,MAAA;AACb,IAAMyE,KAAA,GAAQzB,WAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}