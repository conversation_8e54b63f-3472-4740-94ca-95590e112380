{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website kp\\\\chatbot-unand\\\\frontend\\\\src\\\\contexts\\\\AuthContext.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from \"react\";\nimport { jwtDecode } from \"jwt-decode\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error(\"useAuth must be used within an AuthProvider\");\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [token, setToken] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Check for existing token on mount\n  useEffect(() => {\n    const savedToken = localStorage.getItem(\"access_token\");\n    const savedUser = localStorage.getItem(\"user\");\n    if (savedToken && savedUser) {\n      try {\n        // Check if token is still valid\n        const decoded = jwtDecode(savedToken);\n        const currentTime = Date.now() / 1000;\n        if (decoded.exp > currentTime) {\n          setToken(savedToken);\n          setUser(JSON.parse(savedUser));\n        } else {\n          // Token expired, clear storage\n          localStorage.removeItem(\"access_token\");\n          localStorage.removeItem(\"user\");\n        }\n      } catch (error) {\n        console.error(\"Error decoding token:\", error);\n        localStorage.removeItem(\"access_token\");\n        localStorage.removeItem(\"user\");\n      }\n    }\n    setLoading(false);\n  }, []);\n  const login = async googleToken => {\n    try {\n      console.log(\"AuthContext: Starting login process...\");\n      const response = await fetch(\"http://localhost:8000/auth/google\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          token: googleToken\n        })\n      });\n      console.log(\"AuthContext: Response status:\", response.status);\n      if (!response.ok) {\n        const errorText = await response.text();\n        console.error(\"AuthContext: Response error:\", errorText);\n        throw new Error(`Authentication failed: ${response.status}`);\n      }\n      const data = await response.json();\n      console.log(\"AuthContext: Response data:\", data);\n\n      // Save to localStorage\n      localStorage.setItem(\"access_token\", data.access_token);\n      localStorage.setItem(\"user\", JSON.stringify(data.user));\n\n      // Update state\n      setToken(data.access_token);\n      setUser(data.user);\n      console.log(\"AuthContext: Login completed successfully\");\n      return data;\n    } catch (error) {\n      console.error(\"AuthContext: Login error:\", error);\n      throw error;\n    }\n  };\n  const logout = () => {\n    // Clear localStorage\n    localStorage.removeItem(\"access_token\");\n    localStorage.removeItem(\"user\");\n\n    // Clear state\n    setToken(null);\n    setUser(null);\n  };\n  const getAuthHeaders = () => {\n    if (token) {\n      return {\n        Authorization: `Bearer ${token}`,\n        \"Content-Type\": \"application/json\"\n      };\n    }\n    return {\n      \"Content-Type\": \"application/json\"\n    };\n  };\n  const value = {\n    user,\n    token,\n    loading,\n    login,\n    logout,\n    getAuthHeaders,\n    isAuthenticated: !!user\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 118,\n    columnNumber: 10\n  }, this);\n};\n_s2(AuthProvider, \"uAkFQMmIUxfxJcQTEb8tCM/KFt4=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "jwtDecode", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "token", "setToken", "loading", "setLoading", "savedToken", "localStorage", "getItem", "savedUser", "decoded", "currentTime", "Date", "now", "exp", "JSON", "parse", "removeItem", "error", "console", "login", "googleToken", "log", "response", "fetch", "method", "headers", "body", "stringify", "status", "ok", "errorText", "text", "data", "json", "setItem", "access_token", "logout", "getAuthHeaders", "Authorization", "value", "isAuthenticated", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/website kp/chatbot-unand/frontend/src/contexts/AuthContext.jsx"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from \"react\";\nimport { jwtDecode } from \"jwt-decode\";\n\nconst AuthContext = createContext();\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error(\"useAuth must be used within an AuthProvider\");\n  }\n  return context;\n};\n\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [token, setToken] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Check for existing token on mount\n  useEffect(() => {\n    const savedToken = localStorage.getItem(\"access_token\");\n    const savedUser = localStorage.getItem(\"user\");\n\n    if (savedToken && savedUser) {\n      try {\n        // Check if token is still valid\n        const decoded = jwtDecode(savedToken);\n        const currentTime = Date.now() / 1000;\n\n        if (decoded.exp > currentTime) {\n          setToken(savedToken);\n          setUser(JSON.parse(savedUser));\n        } else {\n          // Token expired, clear storage\n          localStorage.removeItem(\"access_token\");\n          localStorage.removeItem(\"user\");\n        }\n      } catch (error) {\n        console.error(\"Error decoding token:\", error);\n        localStorage.removeItem(\"access_token\");\n        localStorage.removeItem(\"user\");\n      }\n    }\n\n    setLoading(false);\n  }, []);\n\n  const login = async (googleToken) => {\n    try {\n      console.log(\"AuthContext: Starting login process...\");\n      const response = await fetch(\"http://localhost:8000/auth/google\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify({ token: googleToken }),\n      });\n\n      console.log(\"AuthContext: Response status:\", response.status);\n\n      if (!response.ok) {\n        const errorText = await response.text();\n        console.error(\"AuthContext: Response error:\", errorText);\n        throw new Error(`Authentication failed: ${response.status}`);\n      }\n\n      const data = await response.json();\n      console.log(\"AuthContext: Response data:\", data);\n\n      // Save to localStorage\n      localStorage.setItem(\"access_token\", data.access_token);\n      localStorage.setItem(\"user\", JSON.stringify(data.user));\n\n      // Update state\n      setToken(data.access_token);\n      setUser(data.user);\n\n      console.log(\"AuthContext: Login completed successfully\");\n      return data;\n    } catch (error) {\n      console.error(\"AuthContext: Login error:\", error);\n      throw error;\n    }\n  };\n\n  const logout = () => {\n    // Clear localStorage\n    localStorage.removeItem(\"access_token\");\n    localStorage.removeItem(\"user\");\n\n    // Clear state\n    setToken(null);\n    setUser(null);\n  };\n\n  const getAuthHeaders = () => {\n    if (token) {\n      return {\n        Authorization: `Bearer ${token}`,\n        \"Content-Type\": \"application/json\",\n      };\n    }\n    return {\n      \"Content-Type\": \"application/json\",\n    };\n  };\n\n  const value = {\n    user,\n    token,\n    loading,\n    login,\n    logout,\n    getAuthHeaders,\n    isAuthenticated: !!user,\n  };\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,SAASC,SAAS,QAAQ,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,WAAW,gBAAGP,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMQ,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGT,UAAU,CAACM,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACe,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACAC,SAAS,CAAC,MAAM;IACd,MAAMkB,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;IACvD,MAAMC,SAAS,GAAGF,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IAE9C,IAAIF,UAAU,IAAIG,SAAS,EAAE;MAC3B,IAAI;QACF;QACA,MAAMC,OAAO,GAAGrB,SAAS,CAACiB,UAAU,CAAC;QACrC,MAAMK,WAAW,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI;QAErC,IAAIH,OAAO,CAACI,GAAG,GAAGH,WAAW,EAAE;UAC7BR,QAAQ,CAACG,UAAU,CAAC;UACpBL,OAAO,CAACc,IAAI,CAACC,KAAK,CAACP,SAAS,CAAC,CAAC;QAChC,CAAC,MAAM;UACL;UACAF,YAAY,CAACU,UAAU,CAAC,cAAc,CAAC;UACvCV,YAAY,CAACU,UAAU,CAAC,MAAM,CAAC;QACjC;MACF,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7CX,YAAY,CAACU,UAAU,CAAC,cAAc,CAAC;QACvCV,YAAY,CAACU,UAAU,CAAC,MAAM,CAAC;MACjC;IACF;IAEAZ,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMe,KAAK,GAAG,MAAOC,WAAW,IAAK;IACnC,IAAI;MACFF,OAAO,CAACG,GAAG,CAAC,wCAAwC,CAAC;MACrD,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,mCAAmC,EAAE;QAChEC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEZ,IAAI,CAACa,SAAS,CAAC;UAAE1B,KAAK,EAAEmB;QAAY,CAAC;MAC7C,CAAC,CAAC;MAEFF,OAAO,CAACG,GAAG,CAAC,+BAA+B,EAAEC,QAAQ,CAACM,MAAM,CAAC;MAE7D,IAAI,CAACN,QAAQ,CAACO,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAMR,QAAQ,CAACS,IAAI,CAAC,CAAC;QACvCb,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEa,SAAS,CAAC;QACxD,MAAM,IAAInC,KAAK,CAAC,0BAA0B2B,QAAQ,CAACM,MAAM,EAAE,CAAC;MAC9D;MAEA,MAAMI,IAAI,GAAG,MAAMV,QAAQ,CAACW,IAAI,CAAC,CAAC;MAClCf,OAAO,CAACG,GAAG,CAAC,6BAA6B,EAAEW,IAAI,CAAC;;MAEhD;MACA1B,YAAY,CAAC4B,OAAO,CAAC,cAAc,EAAEF,IAAI,CAACG,YAAY,CAAC;MACvD7B,YAAY,CAAC4B,OAAO,CAAC,MAAM,EAAEpB,IAAI,CAACa,SAAS,CAACK,IAAI,CAACjC,IAAI,CAAC,CAAC;;MAEvD;MACAG,QAAQ,CAAC8B,IAAI,CAACG,YAAY,CAAC;MAC3BnC,OAAO,CAACgC,IAAI,CAACjC,IAAI,CAAC;MAElBmB,OAAO,CAACG,GAAG,CAAC,2CAA2C,CAAC;MACxD,OAAOW,IAAI;IACb,CAAC,CAAC,OAAOf,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAMmB,MAAM,GAAGA,CAAA,KAAM;IACnB;IACA9B,YAAY,CAACU,UAAU,CAAC,cAAc,CAAC;IACvCV,YAAY,CAACU,UAAU,CAAC,MAAM,CAAC;;IAE/B;IACAd,QAAQ,CAAC,IAAI,CAAC;IACdF,OAAO,CAAC,IAAI,CAAC;EACf,CAAC;EAED,MAAMqC,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIpC,KAAK,EAAE;MACT,OAAO;QACLqC,aAAa,EAAE,UAAUrC,KAAK,EAAE;QAChC,cAAc,EAAE;MAClB,CAAC;IACH;IACA,OAAO;MACL,cAAc,EAAE;IAClB,CAAC;EACH,CAAC;EAED,MAAMsC,KAAK,GAAG;IACZxC,IAAI;IACJE,KAAK;IACLE,OAAO;IACPgB,KAAK;IACLiB,MAAM;IACNC,cAAc;IACdG,eAAe,EAAE,CAAC,CAACzC;EACrB,CAAC;EAED,oBAAOT,OAAA,CAACC,WAAW,CAACkD,QAAQ;IAACF,KAAK,EAAEA,KAAM;IAAA1C,QAAA,EAAEA;EAAQ;IAAA6C,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAuB,CAAC;AAC9E,CAAC;AAAC/C,GAAA,CAzGWF,YAAY;AAAAkD,EAAA,GAAZlD,YAAY;AAAA,IAAAkD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}