{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 20a8 8 0 1 0 0-16 8 8 0 0 0 0 16Z\",\n  key: \"sobvz5\"\n}], [\"path\", {\n  d: \"M12 14a2 2 0 1 0 0-4 2 2 0 0 0 0 4Z\",\n  key: \"11i496\"\n}], [\"path\", {\n  d: \"M12 2v2\",\n  key: \"tus03m\"\n}], [\"path\", {\n  d: \"M12 22v-2\",\n  key: \"1osdcq\"\n}], [\"path\", {\n  d: \"m17 20.66-1-1.73\",\n  key: \"eq3orb\"\n}], [\"path\", {\n  d: \"M11 10.27 7 3.34\",\n  key: \"16pf9h\"\n}], [\"path\", {\n  d: \"m20.66 17-1.73-1\",\n  key: \"sg0v6f\"\n}], [\"path\", {\n  d: \"m3.34 7 1.73 1\",\n  key: \"1ulond\"\n}], [\"path\", {\n  d: \"M14 12h8\",\n  key: \"4f43i9\"\n}], [\"path\", {\n  d: \"M2 12h2\",\n  key: \"1t8f8n\"\n}], [\"path\", {\n  d: \"m20.66 7-1.73 1\",\n  key: \"1ow05n\"\n}], [\"path\", {\n  d: \"m3.34 17 1.73-1\",\n  key: \"nuk764\"\n}], [\"path\", {\n  d: \"m17 3.34-1 1.73\",\n  key: \"2wel8s\"\n}], [\"path\", {\n  d: \"m11 13.73-4 6.93\",\n  key: \"794ttg\"\n}]];\nconst Cog = createLucideIcon(\"cog\", __iconNode);\nexport { __iconNode, Cog as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "Cog", "createLucideIcon"], "sources": ["D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\node_modules\\lucide-react\\src\\icons\\cog.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 20a8 8 0 1 0 0-16 8 8 0 0 0 0 16Z', key: 'sobvz5' }],\n  ['path', { d: 'M12 14a2 2 0 1 0 0-4 2 2 0 0 0 0 4Z', key: '11i496' }],\n  ['path', { d: 'M12 2v2', key: 'tus03m' }],\n  ['path', { d: 'M12 22v-2', key: '1osdcq' }],\n  ['path', { d: 'm17 20.66-1-1.73', key: 'eq3orb' }],\n  ['path', { d: 'M11 10.27 7 3.34', key: '16pf9h' }],\n  ['path', { d: 'm20.66 17-1.73-1', key: 'sg0v6f' }],\n  ['path', { d: 'm3.34 7 1.73 1', key: '1ulond' }],\n  ['path', { d: 'M14 12h8', key: '4f43i9' }],\n  ['path', { d: 'M2 12h2', key: '1t8f8n' }],\n  ['path', { d: 'm20.66 7-1.73 1', key: '1ow05n' }],\n  ['path', { d: 'm3.34 17 1.73-1', key: 'nuk764' }],\n  ['path', { d: 'm17 3.34-1 1.73', key: '2wel8s' }],\n  ['path', { d: 'm11 13.73-4 6.93', key: '794ttg' }],\n];\n\n/**\n * @component @name Cog\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMjBhOCA4IDAgMSAwIDAtMTYgOCA4IDAgMCAwIDAgMTZaIiAvPgogIDxwYXRoIGQ9Ik0xMiAxNGEyIDIgMCAxIDAgMC00IDIgMiAwIDAgMCAwIDRaIiAvPgogIDxwYXRoIGQ9Ik0xMiAydjIiIC8+CiAgPHBhdGggZD0iTTEyIDIydi0yIiAvPgogIDxwYXRoIGQ9Im0xNyAyMC42Ni0xLTEuNzMiIC8+CiAgPHBhdGggZD0iTTExIDEwLjI3IDcgMy4zNCIgLz4KICA8cGF0aCBkPSJtMjAuNjYgMTctMS43My0xIiAvPgogIDxwYXRoIGQ9Im0zLjM0IDcgMS43MyAxIiAvPgogIDxwYXRoIGQ9Ik0xNCAxMmg4IiAvPgogIDxwYXRoIGQ9Ik0yIDEyaDIiIC8+CiAgPHBhdGggZD0ibTIwLjY2IDctMS43MyAxIiAvPgogIDxwYXRoIGQ9Im0zLjM0IDE3IDEuNzMtMSIgLz4KICA8cGF0aCBkPSJtMTcgMy4zNC0xIDEuNzMiIC8+CiAgPHBhdGggZD0ibTExIDEzLjczLTQgNi45MyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/cog\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Cog = createLucideIcon('cog', __iconNode);\n\nexport default Cog;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,uCAAyC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACtE,CAAC,MAAQ;EAAED,CAAA,EAAG,qCAAuC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACpE,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,kBAAoB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACjD,CAAC,MAAQ;EAAED,CAAA,EAAG,kBAAoB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACjD,CAAC,MAAQ;EAAED,CAAA,EAAG,kBAAoB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACjD,CAAC,MAAQ;EAAED,CAAA,EAAG,gBAAkB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/C,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,iBAAmB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChD,CAAC,MAAQ;EAAED,CAAA,EAAG,iBAAmB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChD,CAAC,MAAQ;EAAED,CAAA,EAAG,iBAAmB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChD,CAAC,MAAQ;EAAED,CAAA,EAAG,kBAAoB;EAAAC,GAAA,EAAK;AAAU,GACnD;AAaM,MAAAC,GAAA,GAAMC,gBAAiB,QAAOJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}