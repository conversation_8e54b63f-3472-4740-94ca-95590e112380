{"name": "jest-message-util", "version": "27.5.1", "repository": {"type": "git", "url": "https://github.com/facebook/jest.git", "directory": "packages/jest-message-util"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "license": "MIT", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "dependencies": {"@babel/code-frame": "^7.12.13", "@jest/types": "^27.5.1", "@types/stack-utils": "^2.0.0", "chalk": "^4.0.0", "graceful-fs": "^4.2.9", "micromatch": "^4.0.4", "pretty-format": "^27.5.1", "slash": "^3.0.0", "stack-utils": "^2.0.3"}, "devDependencies": {"@types/babel__code-frame": "^7.0.0", "@types/graceful-fs": "^4.1.3", "@types/micromatch": "^4.0.1"}, "publishConfig": {"access": "public"}, "gitHead": "67c1aa20c5fec31366d733e901fee2b981cb1850"}