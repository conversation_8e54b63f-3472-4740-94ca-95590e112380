{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 20v2\",\n  key: \"1n8e1g\"\n}], [\"path\", {\n  d: \"M14 20v2\",\n  key: \"1lq872\"\n}], [\"path\", {\n  d: \"M18 20v2\",\n  key: \"10uadw\"\n}], [\"path\", {\n  d: \"M21 20H3\",\n  key: \"kdqkdp\"\n}], [\"path\", {\n  d: \"M6 20v2\",\n  key: \"a9bc87\"\n}], [\"path\", {\n  d: \"M8 16V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v12\",\n  key: \"17n9tx\"\n}], [\"rect\", {\n  x: \"4\",\n  y: \"6\",\n  width: \"16\",\n  height: \"10\",\n  rx: \"2\",\n  key: \"1097i5\"\n}]];\nconst BriefcaseConveyorBelt = createLucideIcon(\"briefcase-conveyor-belt\", __iconNode);\nexport { __iconNode, BriefcaseConveyorBelt as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "x", "y", "width", "height", "rx", "BriefcaseConveyorBelt", "createLucideIcon"], "sources": ["D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\node_modules\\lucide-react\\src\\icons\\briefcase-conveyor-belt.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M10 20v2', key: '1n8e1g' }],\n  ['path', { d: 'M14 20v2', key: '1lq872' }],\n  ['path', { d: 'M18 20v2', key: '10uadw' }],\n  ['path', { d: 'M21 20H3', key: 'kdqkdp' }],\n  ['path', { d: 'M6 20v2', key: 'a9bc87' }],\n  ['path', { d: 'M8 16V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v12', key: '17n9tx' }],\n  ['rect', { x: '4', y: '6', width: '16', height: '10', rx: '2', key: '1097i5' }],\n];\n\n/**\n * @component @name BriefcaseConveyorBelt\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAgMjB2MiIgLz4KICA8cGF0aCBkPSJNMTQgMjB2MiIgLz4KICA8cGF0aCBkPSJNMTggMjB2MiIgLz4KICA8cGF0aCBkPSJNMjEgMjBIMyIgLz4KICA8cGF0aCBkPSJNNiAyMHYyIiAvPgogIDxwYXRoIGQ9Ik04IDE2VjRhMiAyIDAgMCAxIDItMmg0YTIgMiAwIDAgMSAyIDJ2MTIiIC8+CiAgPHJlY3QgeD0iNCIgeT0iNiIgd2lkdGg9IjE2IiBoZWlnaHQ9IjEwIiByeD0iMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/briefcase-conveyor-belt\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst BriefcaseConveyorBelt = createLucideIcon('briefcase-conveyor-belt', __iconNode);\n\nexport default BriefcaseConveyorBelt;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,0CAA4C;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzE,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAKC,CAAG;EAAKC,KAAO;EAAMC,MAAA,EAAQ,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAL,GAAA,EAAK;AAAU,GAChF;AAaM,MAAAM,qBAAA,GAAwBC,gBAAiB,4BAA2BT,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}