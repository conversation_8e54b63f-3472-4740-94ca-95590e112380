{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m12.99 6.74 1.93 3.44\",\n  key: \"iwagvd\"\n}], [\"path\", {\n  d: \"M19.136 12a10 10 0 0 1-14.271 0\",\n  key: \"ppmlo4\"\n}], [\"path\", {\n  d: \"m21 21-2.16-3.84\",\n  key: \"vylbct\"\n}], [\"path\", {\n  d: \"m3 21 8.02-14.26\",\n  key: \"1ssaw4\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"5\",\n  r: \"2\",\n  key: \"f1ur92\"\n}]];\nconst DraftingCompass = createLucideIcon(\"drafting-compass\", __iconNode);\nexport { __iconNode, DraftingCompass as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "cx", "cy", "r", "DraftingCompass", "createLucideIcon"], "sources": ["D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\node_modules\\lucide-react\\src\\icons\\drafting-compass.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm12.99 6.74 1.93 3.44', key: 'iwagvd' }],\n  ['path', { d: 'M19.136 12a10 10 0 0 1-14.271 0', key: 'ppmlo4' }],\n  ['path', { d: 'm21 21-2.16-3.84', key: 'vylbct' }],\n  ['path', { d: 'm3 21 8.02-14.26', key: '1ssaw4' }],\n  ['circle', { cx: '12', cy: '5', r: '2', key: 'f1ur92' }],\n];\n\n/**\n * @component @name DraftingCompass\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTIuOTkgNi43NCAxLjkzIDMuNDQiIC8+CiAgPHBhdGggZD0iTTE5LjEzNiAxMmExMCAxMCAwIDAgMS0xNC4yNzEgMCIgLz4KICA8cGF0aCBkPSJtMjEgMjEtMi4xNi0zLjg0IiAvPgogIDxwYXRoIGQ9Im0zIDIxIDguMDItMTQuMjYiIC8+CiAgPGNpcmNsZSBjeD0iMTIiIGN5PSI1IiByPSIyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/drafting-compass\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst DraftingCompass = createLucideIcon('drafting-compass', __iconNode);\n\nexport default DraftingCompass;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,uBAAyB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACtD,CAAC,MAAQ;EAAED,CAAA,EAAG,iCAAmC;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChE,CAAC,MAAQ;EAAED,CAAA,EAAG,kBAAoB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACjD,CAAC,MAAQ;EAAED,CAAA,EAAG,kBAAoB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACjD,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAKC,CAAG;EAAKH,GAAK;AAAU,GACzD;AAaM,MAAAI,eAAA,GAAkBC,gBAAiB,qBAAoBP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}