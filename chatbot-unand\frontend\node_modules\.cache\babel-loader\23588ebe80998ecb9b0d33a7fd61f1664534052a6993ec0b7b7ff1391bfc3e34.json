{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"10\",\n  key: \"1mglay\"\n}], [\"path\", {\n  d: \"M16 16s-1.5-2-4-2-4 2-4 2\",\n  key: \"epbg0q\"\n}], [\"path\", {\n  d: \"M7.5 8 10 9\",\n  key: \"olxxln\"\n}], [\"path\", {\n  d: \"m14 9 2.5-1\",\n  key: \"1j6cij\"\n}], [\"path\", {\n  d: \"M9 10h.01\",\n  key: \"qbtxuw\"\n}], [\"path\", {\n  d: \"M15 10h.01\",\n  key: \"1qmjsl\"\n}]];\nconst Angry = createLucideIcon(\"angry\", __iconNode);\nexport { __iconNode, Angry as default };", "map": {"version": 3, "names": ["__iconNode", "cx", "cy", "r", "key", "d", "Angry", "createLucideIcon"], "sources": ["D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\node_modules\\lucide-react\\src\\icons\\angry.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['path', { d: 'M16 16s-1.5-2-4-2-4 2-4 2', key: 'epbg0q' }],\n  ['path', { d: 'M7.5 8 10 9', key: 'olxxln' }],\n  ['path', { d: 'm14 9 2.5-1', key: '1j6cij' }],\n  ['path', { d: 'M9 10h.01', key: 'qbtxuw' }],\n  ['path', { d: 'M15 10h.01', key: '1qmjsl' }],\n];\n\n/**\n * @component @name Angry\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8cGF0aCBkPSJNMTYgMTZzLTEuNS0yLTQtMi00IDItNCAyIiAvPgogIDxwYXRoIGQ9Ik03LjUgOCAxMCA5IiAvPgogIDxwYXRoIGQ9Im0xNCA5IDIuNS0xIiAvPgogIDxwYXRoIGQ9Ik05IDEwaC4wMSIgLz4KICA8cGF0aCBkPSJNMTUgMTBoLjAxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/angry\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Angry = createLucideIcon('angry', __iconNode);\n\nexport default Angry;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAMC,GAAK;AAAA,CAAU,GACzD,CAAC,MAAQ;EAAEC,CAAA,EAAG,2BAA6B;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC1D,CAAC,MAAQ;EAAEC,CAAA,EAAG,aAAe;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC5C,CAAC,MAAQ;EAAEC,CAAA,EAAG,aAAe;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC5C,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAEC,CAAA,EAAG,YAAc;EAAAD,GAAA,EAAK;AAAU,GAC7C;AAaM,MAAAE,KAAA,GAAQC,gBAAiB,UAASP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}