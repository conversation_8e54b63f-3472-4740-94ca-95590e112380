{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website kp\\\\chatbot-unand\\\\frontend\\\\src\\\\Message.js\";\nimport React from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Message = ({\n  message\n}) => {\n  const {\n    text,\n    isBot,\n    timestamp,\n    sources,\n    isError\n  } = message;\n  const formatTime = date => {\n    return date.toLocaleTimeString(\"id-ID\", {\n      hour: \"2-digit\",\n      minute: \"2-digit\"\n    });\n  };\n\n  // Function to format text with basic markdown-like formatting\n  const formatText = text => {\n    if (!text) return text;\n\n    // Escape HTML first to prevent XSS\n    let formatted = text.replace(/&/g, \"&amp;\").replace(/</g, \"&lt;\").replace(/>/g, \"&gt;\");\n\n    // Convert **bold** to <strong>\n    formatted = formatted.replace(/\\*\\*(.*?)\\*\\*/g, \"<strong>$1</strong>\");\n\n    // Convert *italic* to <em>\n    formatted = formatted.replace(/\\*(.*?)\\*/g, \"<em>$1</em>\");\n\n    // Convert line breaks to <br>\n    formatted = formatted.replace(/\\n/g, \"<br>\");\n\n    // Convert numbered lists (1. 2. 3.)\n    formatted = formatted.replace(/^(\\d+\\.\\s)/gm, \"<strong>$1</strong>\");\n\n    // Convert bullet points (- or *)\n    formatted = formatted.replace(/^[-*]\\s/gm, \"• \");\n\n    // Convert ## headers to bold\n    formatted = formatted.replace(/^##\\s(.+)$/gm, \"<strong>$1</strong>\");\n    return formatted;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `flex ${isBot ? \"justify-start\" : \"justify-end\"}`,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `max-w-[85%] sm:max-w-[75%] md:max-w-[65%] lg:max-w-[55%] px-4 py-3 rounded-lg shadow-md ${isBot ? isError ? \"bg-red-50 text-red-800 border-2 border-red-300\" : \"bg-gradient-to-r from-green-50 to-yellow-50 text-green-800 border-2 border-green-300\" : \"bg-gradient-to-r from-green-600 to-green-700 text-white shadow-lg\"}`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-sm sm:text-base leading-relaxed whitespace-pre-wrap\",\n        dangerouslySetInnerHTML: {\n          __html: formatText(text)\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this), sources && sources.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-3 pt-3 border-t border-green-400\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xs font-semibold mb-2 text-green-700\",\n          children: \"\\uD83D\\uDCDA Sumber Referensi:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"text-xs space-y-1 text-green-600\",\n          children: sources.map((source, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"truncate\",\n            children: [\"\\u2022 \", source]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: `text-xs mt-2 ${isBot ? \"text-green-600\" : \"text-green-100\"}`,\n        children: formatTime(timestamp)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 45,\n    columnNumber: 5\n  }, this);\n};\n_c = Message;\nexport default Message;\nvar _c;\n$RefreshReg$(_c, \"Message\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Message", "message", "text", "isBot", "timestamp", "sources", "isError", "formatTime", "date", "toLocaleTimeString", "hour", "minute", "formatText", "formatted", "replace", "className", "children", "dangerouslySetInnerHTML", "__html", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "map", "source", "index", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/website kp/chatbot-unand/frontend/src/Message.js"], "sourcesContent": ["import React from \"react\";\n\nconst Message = ({ message }) => {\n  const { text, isBot, timestamp, sources, isError } = message;\n\n  const formatTime = (date) => {\n    return date.toLocaleTimeString(\"id-ID\", {\n      hour: \"2-digit\",\n      minute: \"2-digit\",\n    });\n  };\n\n  // Function to format text with basic markdown-like formatting\n  const formatText = (text) => {\n    if (!text) return text;\n\n    // Escape HTML first to prevent XSS\n    let formatted = text\n      .replace(/&/g, \"&amp;\")\n      .replace(/</g, \"&lt;\")\n      .replace(/>/g, \"&gt;\");\n\n    // Convert **bold** to <strong>\n    formatted = formatted.replace(/\\*\\*(.*?)\\*\\*/g, \"<strong>$1</strong>\");\n\n    // Convert *italic* to <em>\n    formatted = formatted.replace(/\\*(.*?)\\*/g, \"<em>$1</em>\");\n\n    // Convert line breaks to <br>\n    formatted = formatted.replace(/\\n/g, \"<br>\");\n\n    // Convert numbered lists (1. 2. 3.)\n    formatted = formatted.replace(/^(\\d+\\.\\s)/gm, \"<strong>$1</strong>\");\n\n    // Convert bullet points (- or *)\n    formatted = formatted.replace(/^[-*]\\s/gm, \"• \");\n\n    // Convert ## headers to bold\n    formatted = formatted.replace(/^##\\s(.+)$/gm, \"<strong>$1</strong>\");\n\n    return formatted;\n  };\n\n  return (\n    <div className={`flex ${isBot ? \"justify-start\" : \"justify-end\"}`}>\n      <div\n        className={`max-w-[85%] sm:max-w-[75%] md:max-w-[65%] lg:max-w-[55%] px-4 py-3 rounded-lg shadow-md ${\n          isBot\n            ? isError\n              ? \"bg-red-50 text-red-800 border-2 border-red-300\"\n              : \"bg-gradient-to-r from-green-50 to-yellow-50 text-green-800 border-2 border-green-300\"\n            : \"bg-gradient-to-r from-green-600 to-green-700 text-white shadow-lg\"\n        }`}\n      >\n        <div\n          className=\"text-sm sm:text-base leading-relaxed whitespace-pre-wrap\"\n          dangerouslySetInnerHTML={{ __html: formatText(text) }}\n        />\n\n        {sources && sources.length > 0 && (\n          <div className=\"mt-3 pt-3 border-t border-green-400\">\n            <p className=\"text-xs font-semibold mb-2 text-green-700\">\n              📚 Sumber Referensi:\n            </p>\n            <ul className=\"text-xs space-y-1 text-green-600\">\n              {sources.map((source, index) => (\n                <li key={index} className=\"truncate\">\n                  • {source}\n                </li>\n              ))}\n            </ul>\n          </div>\n        )}\n\n        <p\n          className={`text-xs mt-2 ${\n            isBot ? \"text-green-600\" : \"text-green-100\"\n          }`}\n        >\n          {formatTime(timestamp)}\n        </p>\n      </div>\n    </div>\n  );\n};\n\nexport default Message;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,OAAO,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAC/B,MAAM;IAAEC,IAAI;IAAEC,KAAK;IAAEC,SAAS;IAAEC,OAAO;IAAEC;EAAQ,CAAC,GAAGL,OAAO;EAE5D,MAAMM,UAAU,GAAIC,IAAI,IAAK;IAC3B,OAAOA,IAAI,CAACC,kBAAkB,CAAC,OAAO,EAAE;MACtCC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,UAAU,GAAIV,IAAI,IAAK;IAC3B,IAAI,CAACA,IAAI,EAAE,OAAOA,IAAI;;IAEtB;IACA,IAAIW,SAAS,GAAGX,IAAI,CACjBY,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CACtBA,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CACrBA,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC;;IAExB;IACAD,SAAS,GAAGA,SAAS,CAACC,OAAO,CAAC,gBAAgB,EAAE,qBAAqB,CAAC;;IAEtE;IACAD,SAAS,GAAGA,SAAS,CAACC,OAAO,CAAC,YAAY,EAAE,aAAa,CAAC;;IAE1D;IACAD,SAAS,GAAGA,SAAS,CAACC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC;;IAE5C;IACAD,SAAS,GAAGA,SAAS,CAACC,OAAO,CAAC,cAAc,EAAE,qBAAqB,CAAC;;IAEpE;IACAD,SAAS,GAAGA,SAAS,CAACC,OAAO,CAAC,WAAW,EAAE,IAAI,CAAC;;IAEhD;IACAD,SAAS,GAAGA,SAAS,CAACC,OAAO,CAAC,cAAc,EAAE,qBAAqB,CAAC;IAEpE,OAAOD,SAAS;EAClB,CAAC;EAED,oBACEd,OAAA;IAAKgB,SAAS,EAAE,QAAQZ,KAAK,GAAG,eAAe,GAAG,aAAa,EAAG;IAAAa,QAAA,eAChEjB,OAAA;MACEgB,SAAS,EAAE,2FACTZ,KAAK,GACDG,OAAO,GACL,gDAAgD,GAChD,sFAAsF,GACxF,mEAAmE,EACtE;MAAAU,QAAA,gBAEHjB,OAAA;QACEgB,SAAS,EAAC,0DAA0D;QACpEE,uBAAuB,EAAE;UAAEC,MAAM,EAAEN,UAAU,CAACV,IAAI;QAAE;MAAE;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC,EAEDjB,OAAO,IAAIA,OAAO,CAACkB,MAAM,GAAG,CAAC,iBAC5BxB,OAAA;QAAKgB,SAAS,EAAC,qCAAqC;QAAAC,QAAA,gBAClDjB,OAAA;UAAGgB,SAAS,EAAC,2CAA2C;UAAAC,QAAA,EAAC;QAEzD;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJvB,OAAA;UAAIgB,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAC7CX,OAAO,CAACmB,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBACzB3B,OAAA;YAAgBgB,SAAS,EAAC,UAAU;YAAAC,QAAA,GAAC,SACjC,EAACS,MAAM;UAAA,GADFC,KAAK;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEV,CACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CACN,eAEDvB,OAAA;QACEgB,SAAS,EAAE,gBACTZ,KAAK,GAAG,gBAAgB,GAAG,gBAAgB,EAC1C;QAAAa,QAAA,EAEFT,UAAU,CAACH,SAAS;MAAC;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACK,EAAA,GAlFI3B,OAAO;AAoFb,eAAeA,OAAO;AAAC,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}