{"ast": null, "code": "const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || \"http://localhost:8001\";\n\n// Helper function to get auth headers\nconst getAuthHeaders = () => {\n  const token = localStorage.getItem(\"access_token\");\n  const headers = {\n    \"Content-Type\": \"application/json\"\n  };\n  if (token) {\n    headers.Authorization = `Bearer ${token}`;\n  }\n  return headers;\n};\nexport const sendMessageToChatbot = async (query, sessionId = null) => {\n  try {\n    const response = await fetch(`${API_BASE_URL}/chat`, {\n      method: \"POST\",\n      headers: getAuthHeaders(),\n      body: JSON.stringify({\n        query,\n        session_id: sessionId\n      })\n    });\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.detail || \"Terjadi kesalahan pada server.\");\n    }\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error(\"Error sending message to chatbot:\", error);\n    throw error;\n  }\n};\nexport const uploadDocument = async file => {\n  try {\n    const formData = new FormData();\n    formData.append(\"file\", file); // 'file' harus cocok dengan nama parameter di FastAPI\n\n    const token = localStorage.getItem(\"access_token\");\n    const headers = {};\n    if (token) {\n      headers.Authorization = `Bearer ${token}`;\n    }\n    const response = await fetch(`${API_BASE_URL}/upload-document`, {\n      method: \"POST\",\n      headers: headers,\n      body: formData // FormData akan mengatur header Content-Type secara otomatis\n    });\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.detail || \"Gagal mengunggah dokumen.\");\n    }\n    const data = await response.json();\n    return data.message;\n  } catch (error) {\n    console.error(\"Error uploading document:\", error);\n    throw error; // Re-throw to be handled by the component\n  }\n};\n\n// Session management functions\nexport const createSession = async (title = null) => {\n  try {\n    const response = await fetch(`${API_BASE_URL}/sessions`, {\n      method: \"POST\",\n      headers: getAuthHeaders(),\n      body: JSON.stringify({\n        title\n      })\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error(\"Error creating session:\", error);\n    throw error;\n  }\n};\nexport const getSessions = async () => {\n  try {\n    const response = await fetch(`${API_BASE_URL}/sessions`, {\n      headers: getAuthHeaders()\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error(\"Error getting sessions:\", error);\n    throw error;\n  }\n};\nexport const getSessionMessages = async sessionId => {\n  try {\n    const response = await fetch(`${API_BASE_URL}/sessions/${sessionId}/messages`);\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error(\"Error getting session messages:\", error);\n    throw error;\n  }\n};\nexport const updateSession = async (sessionId, title) => {\n  try {\n    const response = await fetch(`${API_BASE_URL}/sessions/${sessionId}`, {\n      method: \"PUT\",\n      headers: {\n        \"Content-Type\": \"application/json\"\n      },\n      body: JSON.stringify({\n        title\n      })\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error(\"Error updating session:\", error);\n    throw error;\n  }\n};\nexport const deleteSession = async sessionId => {\n  try {\n    const response = await fetch(`${API_BASE_URL}/sessions/${sessionId}`, {\n      method: \"DELETE\"\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error(\"Error deleting session:\", error);\n    throw error;\n  }\n};", "map": {"version": 3, "names": ["API_BASE_URL", "process", "env", "REACT_APP_API_BASE_URL", "getAuthHeaders", "token", "localStorage", "getItem", "headers", "Authorization", "sendMessageToChatbot", "query", "sessionId", "response", "fetch", "method", "body", "JSON", "stringify", "session_id", "ok", "errorData", "json", "Error", "detail", "data", "error", "console", "uploadDocument", "file", "formData", "FormData", "append", "message", "createSession", "title", "status", "getSessions", "getSessionMessages", "updateSession", "deleteSession"], "sources": ["C:/Users/<USER>/Downloads/website kp/chatbot-unand/frontend/src/api.js"], "sourcesContent": ["const API_BASE_URL =\n  process.env.REACT_APP_API_BASE_URL || \"http://localhost:8001\";\n\n// Helper function to get auth headers\nconst getAuthHeaders = () => {\n  const token = localStorage.getItem(\"access_token\");\n  const headers = {\n    \"Content-Type\": \"application/json\",\n  };\n\n  if (token) {\n    headers.Authorization = `Bearer ${token}`;\n  }\n\n  return headers;\n};\n\nexport const sendMessageToChatbot = async (query, sessionId = null) => {\n  try {\n    const response = await fetch(`${API_BASE_URL}/chat`, {\n      method: \"POST\",\n      headers: getAuthHeaders(),\n      body: JSON.stringify({\n        query,\n        session_id: sessionId,\n      }),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.detail || \"Terjadi kesalahan pada server.\");\n    }\n\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error(\"Error sending message to chatbot:\", error);\n    throw error;\n  }\n};\n\nexport const uploadDocument = async (file) => {\n  try {\n    const formData = new FormData();\n    formData.append(\"file\", file); // 'file' harus cocok dengan nama parameter di FastAPI\n\n    const token = localStorage.getItem(\"access_token\");\n    const headers = {};\n    if (token) {\n      headers.Authorization = `Bearer ${token}`;\n    }\n\n    const response = await fetch(`${API_BASE_URL}/upload-document`, {\n      method: \"POST\",\n      headers: headers,\n      body: formData, // FormData akan mengatur header Content-Type secara otomatis\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.detail || \"Gagal mengunggah dokumen.\");\n    }\n\n    const data = await response.json();\n    return data.message;\n  } catch (error) {\n    console.error(\"Error uploading document:\", error);\n    throw error; // Re-throw to be handled by the component\n  }\n};\n\n// Session management functions\nexport const createSession = async (title = null) => {\n  try {\n    const response = await fetch(`${API_BASE_URL}/sessions`, {\n      method: \"POST\",\n      headers: getAuthHeaders(),\n      body: JSON.stringify({ title }),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error(\"Error creating session:\", error);\n    throw error;\n  }\n};\n\nexport const getSessions = async () => {\n  try {\n    const response = await fetch(`${API_BASE_URL}/sessions`, {\n      headers: getAuthHeaders(),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error(\"Error getting sessions:\", error);\n    throw error;\n  }\n};\n\nexport const getSessionMessages = async (sessionId) => {\n  try {\n    const response = await fetch(\n      `${API_BASE_URL}/sessions/${sessionId}/messages`\n    );\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error(\"Error getting session messages:\", error);\n    throw error;\n  }\n};\n\nexport const updateSession = async (sessionId, title) => {\n  try {\n    const response = await fetch(`${API_BASE_URL}/sessions/${sessionId}`, {\n      method: \"PUT\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n      },\n      body: JSON.stringify({ title }),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error(\"Error updating session:\", error);\n    throw error;\n  }\n};\n\nexport const deleteSession = async (sessionId) => {\n  try {\n    const response = await fetch(`${API_BASE_URL}/sessions/${sessionId}`, {\n      method: \"DELETE\",\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error(\"Error deleting session:\", error);\n    throw error;\n  }\n};\n"], "mappings": "AAAA,MAAMA,YAAY,GAChBC,OAAO,CAACC,GAAG,CAACC,sBAAsB,IAAI,uBAAuB;;AAE/D;AACA,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAC3B,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;EAClD,MAAMC,OAAO,GAAG;IACd,cAAc,EAAE;EAClB,CAAC;EAED,IAAIH,KAAK,EAAE;IACTG,OAAO,CAACC,aAAa,GAAG,UAAUJ,KAAK,EAAE;EAC3C;EAEA,OAAOG,OAAO;AAChB,CAAC;AAED,OAAO,MAAME,oBAAoB,GAAG,MAAAA,CAAOC,KAAK,EAAEC,SAAS,GAAG,IAAI,KAAK;EACrE,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGd,YAAY,OAAO,EAAE;MACnDe,MAAM,EAAE,MAAM;MACdP,OAAO,EAAEJ,cAAc,CAAC,CAAC;MACzBY,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QACnBP,KAAK;QACLQ,UAAU,EAAEP;MACd,CAAC;IACH,CAAC,CAAC;IAEF,IAAI,CAACC,QAAQ,CAACO,EAAE,EAAE;MAChB,MAAMC,SAAS,GAAG,MAAMR,QAAQ,CAACS,IAAI,CAAC,CAAC;MACvC,MAAM,IAAIC,KAAK,CAACF,SAAS,CAACG,MAAM,IAAI,gCAAgC,CAAC;IACvE;IAEA,MAAMC,IAAI,GAAG,MAAMZ,QAAQ,CAACS,IAAI,CAAC,CAAC;IAClC,OAAOG,IAAI;EACb,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;IACzD,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAME,cAAc,GAAG,MAAOC,IAAI,IAAK;EAC5C,IAAI;IACF,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEH,IAAI,CAAC,CAAC,CAAC;;IAE/B,MAAMxB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;IAClD,MAAMC,OAAO,GAAG,CAAC,CAAC;IAClB,IAAIH,KAAK,EAAE;MACTG,OAAO,CAACC,aAAa,GAAG,UAAUJ,KAAK,EAAE;IAC3C;IAEA,MAAMQ,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGd,YAAY,kBAAkB,EAAE;MAC9De,MAAM,EAAE,MAAM;MACdP,OAAO,EAAEA,OAAO;MAChBQ,IAAI,EAAEc,QAAQ,CAAE;IAClB,CAAC,CAAC;IAEF,IAAI,CAACjB,QAAQ,CAACO,EAAE,EAAE;MAChB,MAAMC,SAAS,GAAG,MAAMR,QAAQ,CAACS,IAAI,CAAC,CAAC;MACvC,MAAM,IAAIC,KAAK,CAACF,SAAS,CAACG,MAAM,IAAI,2BAA2B,CAAC;IAClE;IAEA,MAAMC,IAAI,GAAG,MAAMZ,QAAQ,CAACS,IAAI,CAAC,CAAC;IAClC,OAAOG,IAAI,CAACQ,OAAO;EACrB,CAAC,CAAC,OAAOP,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACjD,MAAMA,KAAK,CAAC,CAAC;EACf;AACF,CAAC;;AAED;AACA,OAAO,MAAMQ,aAAa,GAAG,MAAAA,CAAOC,KAAK,GAAG,IAAI,KAAK;EACnD,IAAI;IACF,MAAMtB,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGd,YAAY,WAAW,EAAE;MACvDe,MAAM,EAAE,MAAM;MACdP,OAAO,EAAEJ,cAAc,CAAC,CAAC;MACzBY,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QAAEiB;MAAM,CAAC;IAChC,CAAC,CAAC;IAEF,IAAI,CAACtB,QAAQ,CAACO,EAAE,EAAE;MAChB,MAAM,IAAIG,KAAK,CAAC,uBAAuBV,QAAQ,CAACuB,MAAM,EAAE,CAAC;IAC3D;IAEA,OAAO,MAAMvB,QAAQ,CAACS,IAAI,CAAC,CAAC;EAC9B,CAAC,CAAC,OAAOI,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IAC/C,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMW,WAAW,GAAG,MAAAA,CAAA,KAAY;EACrC,IAAI;IACF,MAAMxB,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGd,YAAY,WAAW,EAAE;MACvDQ,OAAO,EAAEJ,cAAc,CAAC;IAC1B,CAAC,CAAC;IAEF,IAAI,CAACS,QAAQ,CAACO,EAAE,EAAE;MAChB,MAAM,IAAIG,KAAK,CAAC,uBAAuBV,QAAQ,CAACuB,MAAM,EAAE,CAAC;IAC3D;IAEA,OAAO,MAAMvB,QAAQ,CAACS,IAAI,CAAC,CAAC;EAC9B,CAAC,CAAC,OAAOI,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IAC/C,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMY,kBAAkB,GAAG,MAAO1B,SAAS,IAAK;EACrD,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAC1B,GAAGd,YAAY,aAAaY,SAAS,WACvC,CAAC;IAED,IAAI,CAACC,QAAQ,CAACO,EAAE,EAAE;MAChB,MAAM,IAAIG,KAAK,CAAC,uBAAuBV,QAAQ,CAACuB,MAAM,EAAE,CAAC;IAC3D;IAEA,OAAO,MAAMvB,QAAQ,CAACS,IAAI,CAAC,CAAC;EAC9B,CAAC,CAAC,OAAOI,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACvD,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMa,aAAa,GAAG,MAAAA,CAAO3B,SAAS,EAAEuB,KAAK,KAAK;EACvD,IAAI;IACF,MAAMtB,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGd,YAAY,aAAaY,SAAS,EAAE,EAAE;MACpEG,MAAM,EAAE,KAAK;MACbP,OAAO,EAAE;QACP,cAAc,EAAE;MAClB,CAAC;MACDQ,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QAAEiB;MAAM,CAAC;IAChC,CAAC,CAAC;IAEF,IAAI,CAACtB,QAAQ,CAACO,EAAE,EAAE;MAChB,MAAM,IAAIG,KAAK,CAAC,uBAAuBV,QAAQ,CAACuB,MAAM,EAAE,CAAC;IAC3D;IAEA,OAAO,MAAMvB,QAAQ,CAACS,IAAI,CAAC,CAAC;EAC9B,CAAC,CAAC,OAAOI,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IAC/C,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMc,aAAa,GAAG,MAAO5B,SAAS,IAAK;EAChD,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGd,YAAY,aAAaY,SAAS,EAAE,EAAE;MACpEG,MAAM,EAAE;IACV,CAAC,CAAC;IAEF,IAAI,CAACF,QAAQ,CAACO,EAAE,EAAE;MAChB,MAAM,IAAIG,KAAK,CAAC,uBAAuBV,QAAQ,CAACuB,MAAM,EAAE,CAAC;IAC3D;IAEA,OAAO,MAAMvB,QAAQ,CAACS,IAAI,CAAC,CAAC;EAC9B,CAAC,CAAC,OAAOI,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IAC/C,MAAMA,KAAK;EACb;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}