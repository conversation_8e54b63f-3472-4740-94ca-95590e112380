{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website kp\\\\chatbot-unand\\\\frontend\\\\src\\\\ChatWindow.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from \"react\";\nimport Message from \"./Message\";\nimport ChatInput from \"./ChatInput\";\nimport { sendMessageToChatbot, uploadDocument } from \"./api\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ChatWindow = () => {\n  _s();\n  const [messages, setMessages] = useState([{\n    id: 1,\n    text: \"Halo! Saya adalah Chatbot UNAND. Saya siap membantu Anda dengan pertanyaan seputar peraturan kampus dan pemerintah. Silakan ajukan pertanyaan Anda!\",\n    isBot: true,\n    timestamp: new Date()\n  }]);\n  const [isLoading, setIsLoading] = useState(false);\n  const messagesEndRef = useRef(null);\n  const fileInputRef = useRef(null); // Ref for file input\n\n  const scrollToBottom = () => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: \"smooth\"\n    });\n  };\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n  const handleSendMessage = async text => {\n    const userMessage = {\n      id: Date.now(),\n      text: text,\n      isBot: false,\n      timestamp: new Date()\n    };\n    setMessages(prevMessages => [...prevMessages, userMessage]);\n    setIsLoading(true);\n    try {\n      const botResponse = await sendMessageToChatbot(text);\n      const botMessage = {\n        id: Date.now() + 1,\n        text: botResponse,\n        isBot: true,\n        timestamp: new Date()\n      };\n      setMessages(prevMessages => [...prevMessages, botMessage]);\n    } catch (error) {\n      const errorMessage = {\n        id: Date.now() + 1,\n        text: `Maaf, terjadi kesalahan saat menghubungi chatbot: ${error.message}`,\n        isBot: true,\n        timestamp: new Date(),\n        isError: true\n      };\n      setMessages(prevMessages => [...prevMessages, errorMessage]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleFileUpload = async event => {\n    const file = event.target.files[0];\n    if (!file) return;\n    if (!file.name.endsWith(\".docx\")) {\n      setMessages(prev => [...prev, {\n        text: \"Hanya file Word (.docx) yang diizinkan.\",\n        sender: \"bot\"\n      }]);\n      return;\n    }\n    setMessages(prevMessages => [...prevMessages, {\n      text: `Mengunggah file \"${file.name}\"...`,\n      sender: \"user\"\n    }]);\n    setIsLoading(true);\n    try {\n      const uploadMessage = await uploadDocument(file);\n      setMessages(prevMessages => [...prevMessages, {\n        text: uploadMessage,\n        sender: \"bot\"\n      }]);\n    } catch (error) {\n      setMessages(prevMessages => [...prevMessages, {\n        text: `Gagal mengunggah file: ${error.message}`,\n        sender: \"bot\"\n      }]);\n    } finally {\n      setIsLoading(false);\n      // Reset file input value so the same file can be uploaded again if needed\n      if (fileInputRef.current) {\n        fileInputRef.current.value = \"\";\n      }\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col h-screen bg-gray-50 font-sans\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"bg-gradient-to-r from-blue-600 to-blue-800 text-white p-4 shadow-md flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-xl font-bold\",\n        children: \"Chatbot Peraturan Unand\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"file-upload\",\n          className: \"bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-4 rounded-full text-sm cursor-pointer disabled:opacity-50\",\n          children: isLoading ? \"Mengunggah...\" : \"Unggah Dokumen Baru\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          id: \"file-upload\",\n          type: \"file\",\n          ref: fileInputRef,\n          className: \"hidden\",\n          onChange: handleFileUpload,\n          disabled: isLoading,\n          accept: \".docx\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 overflow-y-auto p-4 space-y-2\",\n      children: [messages.map(message => /*#__PURE__*/_jsxDEV(Message, {\n        message: message\n      }, message.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 11\n      }, this)), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-start\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gray-200 rounded-lg p-3 max-w-xs\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n              style: {\n                animationDelay: \"0.1s\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n              style: {\n                animationDelay: \"0.2s\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: messagesEndRef\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ChatInput, {\n      onSendMessage: handleSendMessage,\n      isLoading: isLoading\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 148,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 99,\n    columnNumber: 5\n  }, this);\n};\n_s(ChatWindow, \"BuuQJvP87EPEpigyvfYAK9rMS0w=\");\n_c = ChatWindow;\nexport default ChatWindow;\nvar _c;\n$RefreshReg$(_c, \"ChatWindow\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Message", "ChatInput", "sendMessageToChatbot", "uploadDocument", "jsxDEV", "_jsxDEV", "ChatWindow", "_s", "messages", "setMessages", "id", "text", "isBot", "timestamp", "Date", "isLoading", "setIsLoading", "messagesEndRef", "fileInputRef", "scrollToBottom", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "handleSendMessage", "userMessage", "now", "prevMessages", "botResponse", "botMessage", "error", "errorMessage", "message", "isError", "handleFileUpload", "event", "file", "target", "files", "name", "endsWith", "prev", "sender", "uploadMessage", "value", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "htmlFor", "type", "ref", "onChange", "disabled", "accept", "map", "style", "animationDelay", "onSendMessage", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/website kp/chatbot-unand/frontend/src/ChatWindow.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from \"react\";\nimport Message from \"./Message\";\nimport ChatInput from \"./ChatInput\";\nimport { sendMessageToChatbot, uploadDocument } from \"./api\";\n\nconst ChatWindow = () => {\n  const [messages, setMessages] = useState([\n    {\n      id: 1,\n      text: \"Halo! Saya adalah Chatbot UNAND. Saya siap membantu Anda dengan pertanyaan seputar peraturan kampus dan pemerintah. Silakan ajukan pertanyaan Anda!\",\n      isBot: true,\n      timestamp: new Date(),\n    },\n  ]);\n  const [isLoading, setIsLoading] = useState(false);\n  const messagesEndRef = useRef(null);\n  const fileInputRef = useRef(null); // Ref for file input\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: \"smooth\" });\n  };\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  const handleSendMessage = async (text) => {\n    const userMessage = {\n      id: Date.now(),\n      text: text,\n      isBot: false,\n      timestamp: new Date(),\n    };\n    setMessages((prevMessages) => [...prevMessages, userMessage]);\n    setIsLoading(true);\n\n    try {\n      const botResponse = await sendMessageToChatbot(text);\n      const botMessage = {\n        id: Date.now() + 1,\n        text: botResponse,\n        isBot: true,\n        timestamp: new Date(),\n      };\n      setMessages((prevMessages) => [...prevMessages, botMessage]);\n    } catch (error) {\n      const errorMessage = {\n        id: Date.now() + 1,\n        text: `Maaf, terjadi kesalahan saat menghubungi chatbot: ${error.message}`,\n        isBot: true,\n        timestamp: new Date(),\n        isError: true,\n      };\n      setMessages((prevMessages) => [...prevMessages, errorMessage]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleFileUpload = async (event) => {\n    const file = event.target.files[0];\n    if (!file) return;\n\n    if (!file.name.endsWith(\".docx\")) {\n      setMessages((prev) => [\n        ...prev,\n        { text: \"Hanya file Word (.docx) yang diizinkan.\", sender: \"bot\" },\n      ]);\n      return;\n    }\n\n    setMessages((prevMessages) => [\n      ...prevMessages,\n      { text: `Mengunggah file \"${file.name}\"...`, sender: \"user\" },\n    ]);\n    setIsLoading(true);\n\n    try {\n      const uploadMessage = await uploadDocument(file);\n      setMessages((prevMessages) => [\n        ...prevMessages,\n        { text: uploadMessage, sender: \"bot\" },\n      ]);\n    } catch (error) {\n      setMessages((prevMessages) => [\n        ...prevMessages,\n        { text: `Gagal mengunggah file: ${error.message}`, sender: \"bot\" },\n      ]);\n    } finally {\n      setIsLoading(false);\n      // Reset file input value so the same file can be uploaded again if needed\n      if (fileInputRef.current) {\n        fileInputRef.current.value = \"\";\n      }\n    }\n  };\n\n  return (\n    <div className=\"flex flex-col h-screen bg-gray-50 font-sans\">\n      <header className=\"bg-gradient-to-r from-blue-600 to-blue-800 text-white p-4 shadow-md flex justify-between items-center\">\n        <h1 className=\"text-xl font-bold\">Chatbot Peraturan Unand</h1>\n        <div className=\"flex items-center space-x-2\">\n          {/* Tombol untuk mengunggah file */}\n          <label\n            htmlFor=\"file-upload\"\n            className=\"bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-4 rounded-full text-sm cursor-pointer disabled:opacity-50\"\n          >\n            {isLoading ? \"Mengunggah...\" : \"Unggah Dokumen Baru\"}\n          </label>\n          <input\n            id=\"file-upload\"\n            type=\"file\"\n            ref={fileInputRef}\n            className=\"hidden\"\n            onChange={handleFileUpload}\n            disabled={isLoading}\n            accept=\".docx\"\n          />\n        </div>\n      </header>\n\n      <div className=\"flex-1 overflow-y-auto p-4 space-y-2\">\n        {messages.map((message) => (\n          <Message key={message.id} message={message} />\n        ))}\n\n        {isLoading && (\n          <div className=\"flex justify-start\">\n            <div className=\"bg-gray-200 rounded-lg p-3 max-w-xs\">\n              <div className=\"flex space-x-1\">\n                <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"></div>\n                <div\n                  className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"\n                  style={{ animationDelay: \"0.1s\" }}\n                ></div>\n                <div\n                  className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"\n                  style={{ animationDelay: \"0.2s\" }}\n                ></div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        <div ref={messagesEndRef} />\n      </div>\n\n      <ChatInput onSendMessage={handleSendMessage} isLoading={isLoading} />\n    </div>\n  );\n};\n\nexport default ChatWindow;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,SAAS,MAAM,aAAa;AACnC,SAASC,oBAAoB,EAAEC,cAAc,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7D,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGZ,QAAQ,CAAC,CACvC;IACEa,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,qJAAqJ;IAC3JC,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,IAAIC,IAAI,CAAC;EACtB,CAAC,CACF,CAAC;EACF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAMoB,cAAc,GAAGlB,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMmB,YAAY,GAAGnB,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;;EAEnC,MAAMoB,cAAc,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAC3B,CAAAA,qBAAA,GAAAH,cAAc,CAACI,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC;EAEDzB,SAAS,CAAC,MAAM;IACdqB,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACX,QAAQ,CAAC,CAAC;EAEd,MAAMgB,iBAAiB,GAAG,MAAOb,IAAI,IAAK;IACxC,MAAMc,WAAW,GAAG;MAClBf,EAAE,EAAEI,IAAI,CAACY,GAAG,CAAC,CAAC;MACdf,IAAI,EAAEA,IAAI;MACVC,KAAK,EAAE,KAAK;MACZC,SAAS,EAAE,IAAIC,IAAI,CAAC;IACtB,CAAC;IACDL,WAAW,CAAEkB,YAAY,IAAK,CAAC,GAAGA,YAAY,EAAEF,WAAW,CAAC,CAAC;IAC7DT,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACF,MAAMY,WAAW,GAAG,MAAM1B,oBAAoB,CAACS,IAAI,CAAC;MACpD,MAAMkB,UAAU,GAAG;QACjBnB,EAAE,EAAEI,IAAI,CAACY,GAAG,CAAC,CAAC,GAAG,CAAC;QAClBf,IAAI,EAAEiB,WAAW;QACjBhB,KAAK,EAAE,IAAI;QACXC,SAAS,EAAE,IAAIC,IAAI,CAAC;MACtB,CAAC;MACDL,WAAW,CAAEkB,YAAY,IAAK,CAAC,GAAGA,YAAY,EAAEE,UAAU,CAAC,CAAC;IAC9D,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd,MAAMC,YAAY,GAAG;QACnBrB,EAAE,EAAEI,IAAI,CAACY,GAAG,CAAC,CAAC,GAAG,CAAC;QAClBf,IAAI,EAAE,qDAAqDmB,KAAK,CAACE,OAAO,EAAE;QAC1EpB,KAAK,EAAE,IAAI;QACXC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;QACrBmB,OAAO,EAAE;MACX,CAAC;MACDxB,WAAW,CAAEkB,YAAY,IAAK,CAAC,GAAGA,YAAY,EAAEI,YAAY,CAAC,CAAC;IAChE,CAAC,SAAS;MACRf,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMkB,gBAAgB,GAAG,MAAOC,KAAK,IAAK;IACxC,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAI,CAACF,IAAI,EAAE;IAEX,IAAI,CAACA,IAAI,CAACG,IAAI,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;MAChC/B,WAAW,CAAEgC,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;QAAE9B,IAAI,EAAE,yCAAyC;QAAE+B,MAAM,EAAE;MAAM,CAAC,CACnE,CAAC;MACF;IACF;IAEAjC,WAAW,CAAEkB,YAAY,IAAK,CAC5B,GAAGA,YAAY,EACf;MAAEhB,IAAI,EAAE,oBAAoByB,IAAI,CAACG,IAAI,MAAM;MAAEG,MAAM,EAAE;IAAO,CAAC,CAC9D,CAAC;IACF1B,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACF,MAAM2B,aAAa,GAAG,MAAMxC,cAAc,CAACiC,IAAI,CAAC;MAChD3B,WAAW,CAAEkB,YAAY,IAAK,CAC5B,GAAGA,YAAY,EACf;QAAEhB,IAAI,EAAEgC,aAAa;QAAED,MAAM,EAAE;MAAM,CAAC,CACvC,CAAC;IACJ,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACdrB,WAAW,CAAEkB,YAAY,IAAK,CAC5B,GAAGA,YAAY,EACf;QAAEhB,IAAI,EAAE,0BAA0BmB,KAAK,CAACE,OAAO,EAAE;QAAEU,MAAM,EAAE;MAAM,CAAC,CACnE,CAAC;IACJ,CAAC,SAAS;MACR1B,YAAY,CAAC,KAAK,CAAC;MACnB;MACA,IAAIE,YAAY,CAACG,OAAO,EAAE;QACxBH,YAAY,CAACG,OAAO,CAACuB,KAAK,GAAG,EAAE;MACjC;IACF;EACF,CAAC;EAED,oBACEvC,OAAA;IAAKwC,SAAS,EAAC,6CAA6C;IAAAC,QAAA,gBAC1DzC,OAAA;MAAQwC,SAAS,EAAC,uGAAuG;MAAAC,QAAA,gBACvHzC,OAAA;QAAIwC,SAAS,EAAC,mBAAmB;QAAAC,QAAA,EAAC;MAAuB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC9D7C,OAAA;QAAKwC,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAE1CzC,OAAA;UACE8C,OAAO,EAAC,aAAa;UACrBN,SAAS,EAAC,4HAA4H;UAAAC,QAAA,EAErI/B,SAAS,GAAG,eAAe,GAAG;QAAqB;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC,eACR7C,OAAA;UACEK,EAAE,EAAC,aAAa;UAChB0C,IAAI,EAAC,MAAM;UACXC,GAAG,EAAEnC,YAAa;UAClB2B,SAAS,EAAC,QAAQ;UAClBS,QAAQ,EAAEpB,gBAAiB;UAC3BqB,QAAQ,EAAExC,SAAU;UACpByC,MAAM,EAAC;QAAO;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAET7C,OAAA;MAAKwC,SAAS,EAAC,sCAAsC;MAAAC,QAAA,GAClDtC,QAAQ,CAACiD,GAAG,CAAEzB,OAAO,iBACpB3B,OAAA,CAACL,OAAO;QAAkBgC,OAAO,EAAEA;MAAQ,GAA7BA,OAAO,CAACtB,EAAE;QAAAqC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAqB,CAC9C,CAAC,EAEDnC,SAAS,iBACRV,OAAA;QAAKwC,SAAS,EAAC,oBAAoB;QAAAC,QAAA,eACjCzC,OAAA;UAAKwC,SAAS,EAAC,qCAAqC;UAAAC,QAAA,eAClDzC,OAAA;YAAKwC,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BzC,OAAA;cAAKwC,SAAS,EAAC;YAAiD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvE7C,OAAA;cACEwC,SAAS,EAAC,iDAAiD;cAC3Da,KAAK,EAAE;gBAAEC,cAAc,EAAE;cAAO;YAAE;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,eACP7C,OAAA;cACEwC,SAAS,EAAC,iDAAiD;cAC3Da,KAAK,EAAE;gBAAEC,cAAc,EAAE;cAAO;YAAE;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAED7C,OAAA;QAAKgD,GAAG,EAAEpC;MAAe;QAAA8B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,eAEN7C,OAAA,CAACJ,SAAS;MAAC2D,aAAa,EAAEpC,iBAAkB;MAACT,SAAS,EAAEA;IAAU;MAAAgC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAClE,CAAC;AAEV,CAAC;AAAC3C,EAAA,CAjJID,UAAU;AAAAuD,EAAA,GAAVvD,UAAU;AAmJhB,eAAeA,UAAU;AAAC,IAAAuD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}