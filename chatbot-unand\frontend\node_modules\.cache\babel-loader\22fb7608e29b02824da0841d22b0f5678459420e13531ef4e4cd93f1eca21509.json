{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website kp\\\\chatbot-unand\\\\frontend\\\\src\\\\contexts\\\\AuthContext.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from \"react\";\nimport { jwtDecode } from \"jwt-decode\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error(\"useAuth must be used within an AuthProvider\");\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [token, setToken] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Check for existing token on mount\n  useEffect(() => {\n    const savedToken = localStorage.getItem(\"access_token\");\n    const savedUser = localStorage.getItem(\"user\");\n    if (savedToken && savedUser) {\n      try {\n        // Check if token is still valid\n        const decoded = jwtDecode(savedToken);\n        const currentTime = Date.now() / 1000;\n        if (decoded.exp > currentTime) {\n          setToken(savedToken);\n          setUser(JSON.parse(savedUser));\n        } else {\n          // Token expired, clear storage\n          localStorage.removeItem(\"access_token\");\n          localStorage.removeItem(\"user\");\n        }\n      } catch (error) {\n        console.error(\"Error decoding token:\", error);\n        localStorage.removeItem(\"access_token\");\n        localStorage.removeItem(\"user\");\n      }\n    }\n    setLoading(false);\n  }, []);\n  const login = async googleToken => {\n    try {\n      console.log(\"AuthContext: Starting login process...\");\n      const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || \"http://localhost:8001\";\n      console.log(\"AuthContext: Using API URL:\", API_BASE_URL);\n      const response = await fetch(`${API_BASE_URL}/auth/google`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          token: googleToken\n        })\n      });\n      console.log(\"AuthContext: Response status:\", response.status);\n      console.log(\"AuthContext: Response headers:\", response.headers);\n      if (!response.ok) {\n        const errorText = await response.text();\n        console.error(\"AuthContext: Response error:\", errorText);\n        console.error(\"AuthContext: Full response:\", response);\n        console.error(\"AuthContext: Response status:\", response.status);\n        console.error(\"AuthContext: Response statusText:\", response.statusText);\n\n        // Try to parse error as JSON if possible\n        try {\n          const errorJson = JSON.parse(errorText);\n          console.error(\"AuthContext: Parsed error JSON:\", errorJson);\n        } catch (e) {\n          console.error(\"AuthContext: Error text is not JSON:\", errorText);\n        }\n        throw new Error(`Authentication failed: ${response.status} - ${errorText}`);\n      }\n      const data = await response.json();\n      console.log(\"AuthContext: Response data:\", data);\n\n      // Save to localStorage\n      localStorage.setItem(\"access_token\", data.access_token);\n      localStorage.setItem(\"user\", JSON.stringify(data.user));\n\n      // Update state\n      setToken(data.access_token);\n      setUser(data.user);\n      console.log(\"AuthContext: Login completed successfully\");\n      return data;\n    } catch (error) {\n      console.error(\"AuthContext: Login error:\", error);\n      throw error;\n    }\n  };\n  const logout = () => {\n    // Clear localStorage\n    localStorage.removeItem(\"access_token\");\n    localStorage.removeItem(\"user\");\n\n    // Clear state\n    setToken(null);\n    setUser(null);\n  };\n  const getAuthHeaders = () => {\n    if (token) {\n      return {\n        Authorization: `Bearer ${token}`,\n        \"Content-Type\": \"application/json\"\n      };\n    }\n    return {\n      \"Content-Type\": \"application/json\"\n    };\n  };\n  const value = {\n    user,\n    token,\n    loading,\n    login,\n    logout,\n    getAuthHeaders,\n    isAuthenticated: !!user\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 136,\n    columnNumber: 10\n  }, this);\n};\n_s2(AuthProvider, \"uAkFQMmIUxfxJcQTEb8tCM/KFt4=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "jwtDecode", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "token", "setToken", "loading", "setLoading", "savedToken", "localStorage", "getItem", "savedUser", "decoded", "currentTime", "Date", "now", "exp", "JSON", "parse", "removeItem", "error", "console", "login", "googleToken", "log", "API_BASE_URL", "process", "env", "REACT_APP_API_BASE_URL", "response", "fetch", "method", "headers", "body", "stringify", "status", "ok", "errorText", "text", "statusText", "<PERSON><PERSON><PERSON>", "e", "data", "json", "setItem", "access_token", "logout", "getAuthHeaders", "Authorization", "value", "isAuthenticated", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/website kp/chatbot-unand/frontend/src/contexts/AuthContext.jsx"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from \"react\";\nimport { jwtDecode } from \"jwt-decode\";\n\nconst AuthContext = createContext();\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error(\"useAuth must be used within an AuthProvider\");\n  }\n  return context;\n};\n\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [token, setToken] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Check for existing token on mount\n  useEffect(() => {\n    const savedToken = localStorage.getItem(\"access_token\");\n    const savedUser = localStorage.getItem(\"user\");\n\n    if (savedToken && savedUser) {\n      try {\n        // Check if token is still valid\n        const decoded = jwtDecode(savedToken);\n        const currentTime = Date.now() / 1000;\n\n        if (decoded.exp > currentTime) {\n          setToken(savedToken);\n          setUser(JSON.parse(savedUser));\n        } else {\n          // Token expired, clear storage\n          localStorage.removeItem(\"access_token\");\n          localStorage.removeItem(\"user\");\n        }\n      } catch (error) {\n        console.error(\"Error decoding token:\", error);\n        localStorage.removeItem(\"access_token\");\n        localStorage.removeItem(\"user\");\n      }\n    }\n\n    setLoading(false);\n  }, []);\n\n  const login = async (googleToken) => {\n    try {\n      console.log(\"AuthContext: Starting login process...\");\n      const API_BASE_URL =\n        process.env.REACT_APP_API_BASE_URL || \"http://localhost:8001\";\n      console.log(\"AuthContext: Using API URL:\", API_BASE_URL);\n      const response = await fetch(`${API_BASE_URL}/auth/google`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify({ token: googleToken }),\n      });\n\n      console.log(\"AuthContext: Response status:\", response.status);\n      console.log(\"AuthContext: Response headers:\", response.headers);\n\n      if (!response.ok) {\n        const errorText = await response.text();\n        console.error(\"AuthContext: Response error:\", errorText);\n        console.error(\"AuthContext: Full response:\", response);\n        console.error(\"AuthContext: Response status:\", response.status);\n        console.error(\"AuthContext: Response statusText:\", response.statusText);\n\n        // Try to parse error as JSON if possible\n        try {\n          const errorJson = JSON.parse(errorText);\n          console.error(\"AuthContext: Parsed error JSON:\", errorJson);\n        } catch (e) {\n          console.error(\"AuthContext: Error text is not JSON:\", errorText);\n        }\n\n        throw new Error(\n          `Authentication failed: ${response.status} - ${errorText}`\n        );\n      }\n\n      const data = await response.json();\n      console.log(\"AuthContext: Response data:\", data);\n\n      // Save to localStorage\n      localStorage.setItem(\"access_token\", data.access_token);\n      localStorage.setItem(\"user\", JSON.stringify(data.user));\n\n      // Update state\n      setToken(data.access_token);\n      setUser(data.user);\n\n      console.log(\"AuthContext: Login completed successfully\");\n      return data;\n    } catch (error) {\n      console.error(\"AuthContext: Login error:\", error);\n      throw error;\n    }\n  };\n\n  const logout = () => {\n    // Clear localStorage\n    localStorage.removeItem(\"access_token\");\n    localStorage.removeItem(\"user\");\n\n    // Clear state\n    setToken(null);\n    setUser(null);\n  };\n\n  const getAuthHeaders = () => {\n    if (token) {\n      return {\n        Authorization: `Bearer ${token}`,\n        \"Content-Type\": \"application/json\",\n      };\n    }\n    return {\n      \"Content-Type\": \"application/json\",\n    };\n  };\n\n  const value = {\n    user,\n    token,\n    loading,\n    login,\n    logout,\n    getAuthHeaders,\n    isAuthenticated: !!user,\n  };\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,SAASC,SAAS,QAAQ,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,WAAW,gBAAGP,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMQ,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGT,UAAU,CAACM,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACe,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACAC,SAAS,CAAC,MAAM;IACd,MAAMkB,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;IACvD,MAAMC,SAAS,GAAGF,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IAE9C,IAAIF,UAAU,IAAIG,SAAS,EAAE;MAC3B,IAAI;QACF;QACA,MAAMC,OAAO,GAAGrB,SAAS,CAACiB,UAAU,CAAC;QACrC,MAAMK,WAAW,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI;QAErC,IAAIH,OAAO,CAACI,GAAG,GAAGH,WAAW,EAAE;UAC7BR,QAAQ,CAACG,UAAU,CAAC;UACpBL,OAAO,CAACc,IAAI,CAACC,KAAK,CAACP,SAAS,CAAC,CAAC;QAChC,CAAC,MAAM;UACL;UACAF,YAAY,CAACU,UAAU,CAAC,cAAc,CAAC;UACvCV,YAAY,CAACU,UAAU,CAAC,MAAM,CAAC;QACjC;MACF,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7CX,YAAY,CAACU,UAAU,CAAC,cAAc,CAAC;QACvCV,YAAY,CAACU,UAAU,CAAC,MAAM,CAAC;MACjC;IACF;IAEAZ,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMe,KAAK,GAAG,MAAOC,WAAW,IAAK;IACnC,IAAI;MACFF,OAAO,CAACG,GAAG,CAAC,wCAAwC,CAAC;MACrD,MAAMC,YAAY,GAChBC,OAAO,CAACC,GAAG,CAACC,sBAAsB,IAAI,uBAAuB;MAC/DP,OAAO,CAACG,GAAG,CAAC,6BAA6B,EAAEC,YAAY,CAAC;MACxD,MAAMI,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGL,YAAY,cAAc,EAAE;QAC1DM,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEhB,IAAI,CAACiB,SAAS,CAAC;UAAE9B,KAAK,EAAEmB;QAAY,CAAC;MAC7C,CAAC,CAAC;MAEFF,OAAO,CAACG,GAAG,CAAC,+BAA+B,EAAEK,QAAQ,CAACM,MAAM,CAAC;MAC7Dd,OAAO,CAACG,GAAG,CAAC,gCAAgC,EAAEK,QAAQ,CAACG,OAAO,CAAC;MAE/D,IAAI,CAACH,QAAQ,CAACO,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAMR,QAAQ,CAACS,IAAI,CAAC,CAAC;QACvCjB,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEiB,SAAS,CAAC;QACxDhB,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAES,QAAQ,CAAC;QACtDR,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAES,QAAQ,CAACM,MAAM,CAAC;QAC/Dd,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAES,QAAQ,CAACU,UAAU,CAAC;;QAEvE;QACA,IAAI;UACF,MAAMC,SAAS,GAAGvB,IAAI,CAACC,KAAK,CAACmB,SAAS,CAAC;UACvChB,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEoB,SAAS,CAAC;QAC7D,CAAC,CAAC,OAAOC,CAAC,EAAE;UACVpB,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEiB,SAAS,CAAC;QAClE;QAEA,MAAM,IAAIvC,KAAK,CACb,0BAA0B+B,QAAQ,CAACM,MAAM,MAAME,SAAS,EAC1D,CAAC;MACH;MAEA,MAAMK,IAAI,GAAG,MAAMb,QAAQ,CAACc,IAAI,CAAC,CAAC;MAClCtB,OAAO,CAACG,GAAG,CAAC,6BAA6B,EAAEkB,IAAI,CAAC;;MAEhD;MACAjC,YAAY,CAACmC,OAAO,CAAC,cAAc,EAAEF,IAAI,CAACG,YAAY,CAAC;MACvDpC,YAAY,CAACmC,OAAO,CAAC,MAAM,EAAE3B,IAAI,CAACiB,SAAS,CAACQ,IAAI,CAACxC,IAAI,CAAC,CAAC;;MAEvD;MACAG,QAAQ,CAACqC,IAAI,CAACG,YAAY,CAAC;MAC3B1C,OAAO,CAACuC,IAAI,CAACxC,IAAI,CAAC;MAElBmB,OAAO,CAACG,GAAG,CAAC,2CAA2C,CAAC;MACxD,OAAOkB,IAAI;IACb,CAAC,CAAC,OAAOtB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAM0B,MAAM,GAAGA,CAAA,KAAM;IACnB;IACArC,YAAY,CAACU,UAAU,CAAC,cAAc,CAAC;IACvCV,YAAY,CAACU,UAAU,CAAC,MAAM,CAAC;;IAE/B;IACAd,QAAQ,CAAC,IAAI,CAAC;IACdF,OAAO,CAAC,IAAI,CAAC;EACf,CAAC;EAED,MAAM4C,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI3C,KAAK,EAAE;MACT,OAAO;QACL4C,aAAa,EAAE,UAAU5C,KAAK,EAAE;QAChC,cAAc,EAAE;MAClB,CAAC;IACH;IACA,OAAO;MACL,cAAc,EAAE;IAClB,CAAC;EACH,CAAC;EAED,MAAM6C,KAAK,GAAG;IACZ/C,IAAI;IACJE,KAAK;IACLE,OAAO;IACPgB,KAAK;IACLwB,MAAM;IACNC,cAAc;IACdG,eAAe,EAAE,CAAC,CAAChD;EACrB,CAAC;EAED,oBAAOT,OAAA,CAACC,WAAW,CAACyD,QAAQ;IAACF,KAAK,EAAEA,KAAM;IAAAjD,QAAA,EAAEA;EAAQ;IAAAoD,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAuB,CAAC;AAC9E,CAAC;AAACtD,GAAA,CA3HWF,YAAY;AAAAyD,EAAA,GAAZzD,YAAY;AAAA,IAAAyD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}