{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website kp\\\\chatbot-unand\\\\frontend\\\\src\\\\ChatInput.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ChatInput = ({\n  onSendMessage,\n  isLoading\n}) => {\n  _s();\n  const [message, setMessage] = useState(\"\");\n  const handleSubmit = e => {\n    e.preventDefault();\n    if (message.trim() && !isLoading) {\n      onSendMessage(message);\n      setMessage(\"\");\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"border-t-2 border-green-600 dark:border-green-400 bg-gradient-to-r from-green-100 to-yellow-100 dark:from-gray-800 dark:to-gray-700\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center p-3 sm:p-4 gap-2 sm:gap-3\",\n      children: /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"flex flex-1 items-center gap-2 sm:gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          className: \"flex-grow rounded-full py-2 px-3 sm:py-3 sm:px-4 bg-white dark:bg-gray-700 border-2 border-green-300 dark:border-gray-500 focus:outline-none focus:ring-2 focus:ring-green-500 dark:focus:ring-green-400 focus:border-green-500 dark:focus:border-green-400 shadow-md h-10 sm:h-12 text-sm sm:text-base text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-300\",\n          placeholder: \"Tulis pesan Anda...\",\n          value: message,\n          onChange: e => setMessage(e.target.value),\n          disabled: isLoading\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"bg-gradient-to-r from-green-600 to-green-700 dark:from-green-500 dark:to-green-600 text-white rounded-full p-2 sm:p-3 hover:from-green-700 hover:to-green-800 dark:hover:from-green-600 dark:hover:to-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 dark:focus:ring-green-400 disabled:opacity-50 shadow-md hover:shadow-lg transition-all duration-200 h-10 w-10 sm:h-12 sm:w-12 flex items-center justify-center flex-shrink-0\",\n          disabled: isLoading,\n          children: isLoading ? /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"animate-spin h-4 w-4 sm:h-5 sm:w-5 text-white\",\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n              className: \"opacity-25\",\n              cx: \"12\",\n              cy: \"12\",\n              r: \"10\",\n              stroke: \"currentColor\",\n              strokeWidth: \"4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 42,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n              className: \"opacity-75\",\n              fill: \"currentColor\",\n              d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            className: \"h-4 w-4 sm:h-5 sm:w-5\",\n            viewBox: \"0 0 20 20\",\n            fill: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l4.453-1.483a1 1 0 00.67-.099l2.258 2.258a1 1 0 001.414 0l2.258-2.258a1 1 0 00.67.099l4.453 1.483a1 1 0 001.169-1.409l-7-14z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 5\n  }, this);\n};\n_s(ChatInput, \"EiOGSxO4GWQlH0sM782nQ9JwuAs=\");\n_c = ChatInput;\nexport default ChatInput;\nvar _c;\n$RefreshReg$(_c, \"ChatInput\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "ChatInput", "onSendMessage", "isLoading", "_s", "message", "setMessage", "handleSubmit", "e", "preventDefault", "trim", "className", "children", "onSubmit", "type", "placeholder", "value", "onChange", "target", "disabled", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "xmlns", "fill", "viewBox", "cx", "cy", "r", "stroke", "strokeWidth", "d", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/website kp/chatbot-unand/frontend/src/ChatInput.jsx"], "sourcesContent": ["import React, { useState } from \"react\";\n\nconst ChatInput = ({ onSendMessage, isLoading }) => {\n  const [message, setMessage] = useState(\"\");\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    if (message.trim() && !isLoading) {\n      onSendMessage(message);\n      setMessage(\"\");\n    }\n  };\n\n  return (\n    <div className=\"border-t-2 border-green-600 dark:border-green-400 bg-gradient-to-r from-green-100 to-yellow-100 dark:from-gray-800 dark:to-gray-700\">\n      <div className=\"flex items-center p-3 sm:p-4 gap-2 sm:gap-3\">\n        {/* Message Input Form */}\n        <form\n          onSubmit={handleSubmit}\n          className=\"flex flex-1 items-center gap-2 sm:gap-3\"\n        >\n          <input\n            type=\"text\"\n            className=\"flex-grow rounded-full py-2 px-3 sm:py-3 sm:px-4 bg-white dark:bg-gray-700 border-2 border-green-300 dark:border-gray-500 focus:outline-none focus:ring-2 focus:ring-green-500 dark:focus:ring-green-400 focus:border-green-500 dark:focus:border-green-400 shadow-md h-10 sm:h-12 text-sm sm:text-base text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-300\"\n            placeholder=\"Tulis pesan Anda...\"\n            value={message}\n            onChange={(e) => setMessage(e.target.value)}\n            disabled={isLoading}\n          />\n          <button\n            type=\"submit\"\n            className=\"bg-gradient-to-r from-green-600 to-green-700 dark:from-green-500 dark:to-green-600 text-white rounded-full p-2 sm:p-3 hover:from-green-700 hover:to-green-800 dark:hover:from-green-600 dark:hover:to-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 dark:focus:ring-green-400 disabled:opacity-50 shadow-md hover:shadow-lg transition-all duration-200 h-10 w-10 sm:h-12 sm:w-12 flex items-center justify-center flex-shrink-0\"\n            disabled={isLoading}\n          >\n            {isLoading ? (\n              <svg\n                className=\"animate-spin h-4 w-4 sm:h-5 sm:w-5 text-white\"\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n              >\n                <circle\n                  className=\"opacity-25\"\n                  cx=\"12\"\n                  cy=\"12\"\n                  r=\"10\"\n                  stroke=\"currentColor\"\n                  strokeWidth=\"4\"\n                ></circle>\n                <path\n                  className=\"opacity-75\"\n                  fill=\"currentColor\"\n                  d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                ></path>\n              </svg>\n            ) : (\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                className=\"h-4 w-4 sm:h-5 sm:w-5\"\n                viewBox=\"0 0 20 20\"\n                fill=\"currentColor\"\n              >\n                <path d=\"M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l4.453-1.483a1 1 0 00.67-.099l2.258 2.258a1 1 0 001.414 0l2.258-2.258a1 1 0 00.67.099l4.453 1.483a1 1 0 001.169-1.409l-7-14z\" />\n              </svg>\n            )}\n          </button>\n        </form>\n      </div>\n    </div>\n  );\n};\n\nexport default ChatInput;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,SAAS,GAAGA,CAAC;EAAEC,aAAa;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EAClD,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EAE1C,MAAMS,YAAY,GAAIC,CAAC,IAAK;IAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAIJ,OAAO,CAACK,IAAI,CAAC,CAAC,IAAI,CAACP,SAAS,EAAE;MAChCD,aAAa,CAACG,OAAO,CAAC;MACtBC,UAAU,CAAC,EAAE,CAAC;IAChB;EACF,CAAC;EAED,oBACEN,OAAA;IAAKW,SAAS,EAAC,qIAAqI;IAAAC,QAAA,eAClJZ,OAAA;MAAKW,SAAS,EAAC,6CAA6C;MAAAC,QAAA,eAE1DZ,OAAA;QACEa,QAAQ,EAAEN,YAAa;QACvBI,SAAS,EAAC,yCAAyC;QAAAC,QAAA,gBAEnDZ,OAAA;UACEc,IAAI,EAAC,MAAM;UACXH,SAAS,EAAC,sXAAsX;UAChYI,WAAW,EAAC,qBAAqB;UACjCC,KAAK,EAAEX,OAAQ;UACfY,QAAQ,EAAGT,CAAC,IAAKF,UAAU,CAACE,CAAC,CAACU,MAAM,CAACF,KAAK,CAAE;UAC5CG,QAAQ,EAAEhB;QAAU;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC,eACFvB,OAAA;UACEc,IAAI,EAAC,QAAQ;UACbH,SAAS,EAAC,mbAAmb;UAC7bQ,QAAQ,EAAEhB,SAAU;UAAAS,QAAA,EAEnBT,SAAS,gBACRH,OAAA;YACEW,SAAS,EAAC,+CAA+C;YACzDa,KAAK,EAAC,4BAA4B;YAClCC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YAAAd,QAAA,gBAEnBZ,OAAA;cACEW,SAAS,EAAC,YAAY;cACtBgB,EAAE,EAAC,IAAI;cACPC,EAAE,EAAC,IAAI;cACPC,CAAC,EAAC,IAAI;cACNC,MAAM,EAAC,cAAc;cACrBC,WAAW,EAAC;YAAG;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eACVvB,OAAA;cACEW,SAAS,EAAC,YAAY;cACtBc,IAAI,EAAC,cAAc;cACnBO,CAAC,EAAC;YAAiH;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9G,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,gBAENvB,OAAA;YACEwB,KAAK,EAAC,4BAA4B;YAClCb,SAAS,EAAC,uBAAuB;YACjCe,OAAO,EAAC,WAAW;YACnBD,IAAI,EAAC,cAAc;YAAAb,QAAA,eAEnBZ,OAAA;cAAMgC,CAAC,EAAC;YAAsL;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9L;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACnB,EAAA,CApEIH,SAAS;AAAAgC,EAAA,GAAThC,SAAS;AAsEf,eAAeA,SAAS;AAAC,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}