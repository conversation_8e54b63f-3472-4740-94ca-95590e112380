{"ast": null, "code": "// src/use-controllable-state.tsx\nimport * as React from \"react\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nvar useInsertionEffect = React[\" useInsertionEffect \".trim().toString()] || useLayoutEffect;\nfunction useControllableState({\n  prop,\n  defaultProp,\n  onChange = () => {},\n  caller\n}) {\n  const [uncontrolledProp, setUncontrolledProp, onChangeRef] = useUncontrolledState({\n    defaultProp,\n    onChange\n  });\n  const isControlled = prop !== void 0;\n  const value = isControlled ? prop : uncontrolledProp;\n  if (true) {\n    const isControlledRef = React.useRef(prop !== void 0);\n    React.useEffect(() => {\n      const wasControlled = isControlledRef.current;\n      if (wasControlled !== isControlled) {\n        const from = wasControlled ? \"controlled\" : \"uncontrolled\";\n        const to = isControlled ? \"controlled\" : \"uncontrolled\";\n        console.warn(`${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`);\n      }\n      isControlledRef.current = isControlled;\n    }, [isControlled, caller]);\n  }\n  const setValue = React.useCallback(nextValue => {\n    if (isControlled) {\n      const value2 = isFunction(nextValue) ? nextValue(prop) : nextValue;\n      if (value2 !== prop) {\n        onChangeRef.current?.(value2);\n      }\n    } else {\n      setUncontrolledProp(nextValue);\n    }\n  }, [isControlled, prop, setUncontrolledProp, onChangeRef]);\n  return [value, setValue];\n}\nfunction useUncontrolledState({\n  defaultProp,\n  onChange\n}) {\n  const [value, setValue] = React.useState(defaultProp);\n  const prevValueRef = React.useRef(value);\n  const onChangeRef = React.useRef(onChange);\n  useInsertionEffect(() => {\n    onChangeRef.current = onChange;\n  }, [onChange]);\n  React.useEffect(() => {\n    if (prevValueRef.current !== value) {\n      onChangeRef.current?.(value);\n      prevValueRef.current = value;\n    }\n  }, [value, prevValueRef]);\n  return [value, setValue, onChangeRef];\n}\nfunction isFunction(value) {\n  return typeof value === \"function\";\n}\n\n// src/use-controllable-state-reducer.tsx\nimport * as React2 from \"react\";\nimport { useEffectEvent } from \"@radix-ui/react-use-effect-event\";\nvar SYNC_STATE = Symbol(\"RADIX:SYNC_STATE\");\nfunction useControllableStateReducer(reducer, userArgs, initialArg, init) {\n  const {\n    prop: controlledState,\n    defaultProp,\n    onChange: onChangeProp,\n    caller\n  } = userArgs;\n  const isControlled = controlledState !== void 0;\n  const onChange = useEffectEvent(onChangeProp);\n  if (true) {\n    const isControlledRef = React2.useRef(controlledState !== void 0);\n    React2.useEffect(() => {\n      const wasControlled = isControlledRef.current;\n      if (wasControlled !== isControlled) {\n        const from = wasControlled ? \"controlled\" : \"uncontrolled\";\n        const to = isControlled ? \"controlled\" : \"uncontrolled\";\n        console.warn(`${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`);\n      }\n      isControlledRef.current = isControlled;\n    }, [isControlled, caller]);\n  }\n  const args = [{\n    ...initialArg,\n    state: defaultProp\n  }];\n  if (init) {\n    args.push(init);\n  }\n  const [internalState, dispatch] = React2.useReducer((state2, action) => {\n    if (action.type === SYNC_STATE) {\n      return {\n        ...state2,\n        state: action.state\n      };\n    }\n    const next = reducer(state2, action);\n    if (isControlled && !Object.is(next.state, state2.state)) {\n      onChange(next.state);\n    }\n    return next;\n  }, ...args);\n  const uncontrolledState = internalState.state;\n  const prevValueRef = React2.useRef(uncontrolledState);\n  React2.useEffect(() => {\n    if (prevValueRef.current !== uncontrolledState) {\n      prevValueRef.current = uncontrolledState;\n      if (!isControlled) {\n        onChange(uncontrolledState);\n      }\n    }\n  }, [onChange, uncontrolledState, prevValueRef, isControlled]);\n  const state = React2.useMemo(() => {\n    const isControlled2 = controlledState !== void 0;\n    if (isControlled2) {\n      return {\n        ...internalState,\n        state: controlledState\n      };\n    }\n    return internalState;\n  }, [internalState, controlledState]);\n  React2.useEffect(() => {\n    if (isControlled && !Object.is(controlledState, internalState.state)) {\n      dispatch({\n        type: SYNC_STATE,\n        state: controlledState\n      });\n    }\n  }, [controlledState, internalState.state, isControlled]);\n  return [state, dispatch];\n}\nexport { useControllableState, useControllableStateReducer };", "map": {"version": 3, "names": ["React", "useLayoutEffect", "useInsertionEffect", "trim", "toString", "useControllableState", "prop", "defaultProp", "onChange", "caller", "uncontrolledProp", "setUncontrolledProp", "onChangeRef", "useUncontrolledState", "isControlled", "value", "isControlledRef", "useRef", "useEffect", "wasControlled", "current", "from", "to", "console", "warn", "setValue", "useCallback", "nextValue", "value2", "isFunction", "useState", "prevValueRef", "React2", "useEffectEvent", "SYNC_STATE", "Symbol", "useControllableStateReducer", "reducer", "userArgs", "initialArg", "init", "controlledState", "onChangeProp", "args", "state", "push", "internalState", "dispatch", "useReducer", "state2", "action", "type", "next", "Object", "is", "uncontrolledState", "useMemo", "isControlled2"], "sources": ["D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\node_modules\\@radix-ui\\react-use-controllable-state\\src\\use-controllable-state.tsx", "D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\node_modules\\@radix-ui\\react-use-controllable-state\\src\\use-controllable-state-reducer.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\n\n// Prevent bundlers from trying to optimize the import\nconst useInsertionEffect: typeof useLayoutEffect =\n  (React as any)[' useInsertionEffect '.trim().toString()] || useLayoutEffect;\n\ntype ChangeHandler<T> = (state: T) => void;\ntype SetStateFn<T> = React.Dispatch<React.SetStateAction<T>>;\n\ninterface UseControllableStateParams<T> {\n  prop?: T | undefined;\n  defaultProp: T;\n  onChange?: ChangeHandler<T>;\n  caller?: string;\n}\n\nexport function useControllableState<T>({\n  prop,\n  defaultProp,\n  onChange = () => {},\n  caller,\n}: UseControllableStateParams<T>): [T, SetStateFn<T>] {\n  const [uncontrolledProp, setUncontrolledProp, onChangeRef] = useUncontrolledState({\n    defaultProp,\n    onChange,\n  });\n  const isControlled = prop !== undefined;\n  const value = isControlled ? prop : uncontrolledProp;\n\n  // OK to disable conditionally calling hooks here because they will always run\n  // consistently in the same environment. Bundlers should be able to remove the\n  // code block entirely in production.\n  /* eslint-disable react-hooks/rules-of-hooks */\n  if (process.env.NODE_ENV !== 'production') {\n    const isControlledRef = React.useRef(prop !== undefined);\n    React.useEffect(() => {\n      const wasControlled = isControlledRef.current;\n      if (wasControlled !== isControlled) {\n        const from = wasControlled ? 'controlled' : 'uncontrolled';\n        const to = isControlled ? 'controlled' : 'uncontrolled';\n        console.warn(\n          `${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`\n        );\n      }\n      isControlledRef.current = isControlled;\n    }, [isControlled, caller]);\n  }\n  /* eslint-enable react-hooks/rules-of-hooks */\n\n  const setValue = React.useCallback<SetStateFn<T>>(\n    (nextValue) => {\n      if (isControlled) {\n        const value = isFunction(nextValue) ? nextValue(prop) : nextValue;\n        if (value !== prop) {\n          onChangeRef.current?.(value);\n        }\n      } else {\n        setUncontrolledProp(nextValue);\n      }\n    },\n    [isControlled, prop, setUncontrolledProp, onChangeRef]\n  );\n\n  return [value, setValue];\n}\n\nfunction useUncontrolledState<T>({\n  defaultProp,\n  onChange,\n}: Omit<UseControllableStateParams<T>, 'prop'>): [\n  Value: T,\n  setValue: React.Dispatch<React.SetStateAction<T>>,\n  OnChangeRef: React.RefObject<ChangeHandler<T> | undefined>,\n] {\n  const [value, setValue] = React.useState(defaultProp);\n  const prevValueRef = React.useRef(value);\n\n  const onChangeRef = React.useRef(onChange);\n  useInsertionEffect(() => {\n    onChangeRef.current = onChange;\n  }, [onChange]);\n\n  React.useEffect(() => {\n    if (prevValueRef.current !== value) {\n      onChangeRef.current?.(value);\n      prevValueRef.current = value;\n    }\n  }, [value, prevValueRef]);\n\n  return [value, setValue, onChangeRef];\n}\n\nfunction isFunction(value: unknown): value is (...args: any[]) => any {\n  return typeof value === 'function';\n}\n", "import * as React from 'react';\nimport { useEffectEvent } from '@radix-ui/react-use-effect-event';\n\ntype ChangeHandler<T> = (state: T) => void;\n\ninterface UseControllableStateParams<T> {\n  prop: T | undefined;\n  defaultProp: T;\n  onChange: ChangeHandler<T> | undefined;\n  caller: string;\n}\n\ninterface AnyAction {\n  type: string;\n}\n\nconst SYNC_STATE = Symbol('RADIX:SYNC_STATE');\n\ninterface SyncStateAction<T> {\n  type: typeof SYNC_STATE;\n  state: T;\n}\n\nexport function useControllableStateReducer<T, S extends {}, A extends AnyAction>(\n  reducer: (prevState: S & { state: T }, action: A) => S & { state: T },\n  userArgs: UseControllableStateParams<T>,\n  initialState: S\n): [S & { state: T }, React.Dispatch<A>];\n\nexport function useControllableStateReducer<T, S extends {}, I, A extends AnyAction>(\n  reducer: (prevState: S & { state: T }, action: A) => S & { state: T },\n  userArgs: UseControllableStateParams<T>,\n  initialArg: I,\n  init: (i: I & { state: T }) => S\n): [S & { state: T }, React.Dispatch<A>];\n\nexport function useControllableStateReducer<T, S extends {}, A extends AnyAction>(\n  reducer: (prevState: S & { state: T }, action: A) => S & { state: T },\n  userArgs: UseControllableStateParams<T>,\n  initialArg: any,\n  init?: (i: any) => Omit<S, 'state'>\n): [S & { state: T }, React.Dispatch<A>] {\n  const { prop: controlledState, defaultProp, onChange: onChangeProp, caller } = userArgs;\n  const isControlled = controlledState !== undefined;\n\n  const onChange = useEffectEvent(onChangeProp);\n\n  // OK to disable conditionally calling hooks here because they will always run\n  // consistently in the same environment. Bundlers should be able to remove the\n  // code block entirely in production.\n  /* eslint-disable react-hooks/rules-of-hooks */\n  if (process.env.NODE_ENV !== 'production') {\n    const isControlledRef = React.useRef(controlledState !== undefined);\n    React.useEffect(() => {\n      const wasControlled = isControlledRef.current;\n      if (wasControlled !== isControlled) {\n        const from = wasControlled ? 'controlled' : 'uncontrolled';\n        const to = isControlled ? 'controlled' : 'uncontrolled';\n        console.warn(\n          `${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`\n        );\n      }\n      isControlledRef.current = isControlled;\n    }, [isControlled, caller]);\n  }\n  /* eslint-enable react-hooks/rules-of-hooks */\n\n  type InternalState = S & { state: T };\n  const args: [InternalState] = [{ ...initialArg, state: defaultProp }];\n  if (init) {\n    // @ts-expect-error\n    args.push(init);\n  }\n\n  const [internalState, dispatch] = React.useReducer(\n    (state: InternalState, action: A | SyncStateAction<T>): InternalState => {\n      if (action.type === SYNC_STATE) {\n        return { ...state, state: action.state };\n      }\n\n      const next = reducer(state, action);\n      if (isControlled && !Object.is(next.state, state.state)) {\n        onChange(next.state);\n      }\n      return next;\n    },\n    ...args\n  );\n\n  const uncontrolledState = internalState.state;\n  const prevValueRef = React.useRef(uncontrolledState);\n  React.useEffect(() => {\n    if (prevValueRef.current !== uncontrolledState) {\n      prevValueRef.current = uncontrolledState;\n      if (!isControlled) {\n        onChange(uncontrolledState);\n      }\n    }\n  }, [onChange, uncontrolledState, prevValueRef, isControlled]);\n\n  const state = React.useMemo(() => {\n    const isControlled = controlledState !== undefined;\n    if (isControlled) {\n      return { ...internalState, state: controlledState };\n    }\n\n    return internalState;\n  }, [internalState, controlledState]);\n\n  React.useEffect(() => {\n    // Sync internal state for controlled components so that reducer is called\n    // with the correct state values\n    if (isControlled && !Object.is(controlledState, internalState.state)) {\n      dispatch({ type: SYNC_STATE, state: controlledState });\n    }\n  }, [controlledState, internalState.state, isControlled]);\n\n  return [state, dispatch as React.Dispatch<A>];\n}\n"], "mappings": ";AAAA,YAAYA,KAAA,MAAW;AACvB,SAASC,eAAA,QAAuB;AAGhC,IAAMC,kBAAA,GACHF,KAAA,CAAc,uBAAuBG,IAAA,CAAK,EAAEC,QAAA,CAAS,CAAC,KAAKH,eAAA;AAYvD,SAASI,qBAAwB;EACtCC,IAAA;EACAC,WAAA;EACAC,QAAA,GAAWA,CAAA,KAAM,CAAC;EAClBC;AACF,GAAsD;EACpD,MAAM,CAACC,gBAAA,EAAkBC,mBAAA,EAAqBC,WAAW,IAAIC,oBAAA,CAAqB;IAChFN,WAAA;IACAC;EACF,CAAC;EACD,MAAMM,YAAA,GAAeR,IAAA,KAAS;EAC9B,MAAMS,KAAA,GAAQD,YAAA,GAAeR,IAAA,GAAOI,gBAAA;EAMpC,IAAI,MAAuC;IACzC,MAAMM,eAAA,GAAwBhB,KAAA,CAAAiB,MAAA,CAAOX,IAAA,KAAS,MAAS;IACjDN,KAAA,CAAAkB,SAAA,CAAU,MAAM;MACpB,MAAMC,aAAA,GAAgBH,eAAA,CAAgBI,OAAA;MACtC,IAAID,aAAA,KAAkBL,YAAA,EAAc;QAClC,MAAMO,IAAA,GAAOF,aAAA,GAAgB,eAAe;QAC5C,MAAMG,EAAA,GAAKR,YAAA,GAAe,eAAe;QACzCS,OAAA,CAAQC,IAAA,CACN,GAAGf,MAAM,qBAAqBY,IAAI,OAAOC,EAAE,4KAC7C;MACF;MACAN,eAAA,CAAgBI,OAAA,GAAUN,YAAA;IAC5B,GAAG,CAACA,YAAA,EAAcL,MAAM,CAAC;EAC3B;EAGA,MAAMgB,QAAA,GAAiBzB,KAAA,CAAA0B,WAAA,CACpBC,SAAA,IAAc;IACb,IAAIb,YAAA,EAAc;MAChB,MAAMc,MAAA,GAAQC,UAAA,CAAWF,SAAS,IAAIA,SAAA,CAAUrB,IAAI,IAAIqB,SAAA;MACxD,IAAIC,MAAA,KAAUtB,IAAA,EAAM;QAClBM,WAAA,CAAYQ,OAAA,GAAUQ,MAAK;MAC7B;IACF,OAAO;MACLjB,mBAAA,CAAoBgB,SAAS;IAC/B;EACF,GACA,CAACb,YAAA,EAAcR,IAAA,EAAMK,mBAAA,EAAqBC,WAAW,CACvD;EAEA,OAAO,CAACG,KAAA,EAAOU,QAAQ;AACzB;AAEA,SAASZ,qBAAwB;EAC/BN,WAAA;EACAC;AACF,GAIE;EACA,MAAM,CAACO,KAAA,EAAOU,QAAQ,IAAUzB,KAAA,CAAA8B,QAAA,CAASvB,WAAW;EACpD,MAAMwB,YAAA,GAAqB/B,KAAA,CAAAiB,MAAA,CAAOF,KAAK;EAEvC,MAAMH,WAAA,GAAoBZ,KAAA,CAAAiB,MAAA,CAAOT,QAAQ;EACzCN,kBAAA,CAAmB,MAAM;IACvBU,WAAA,CAAYQ,OAAA,GAAUZ,QAAA;EACxB,GAAG,CAACA,QAAQ,CAAC;EAEPR,KAAA,CAAAkB,SAAA,CAAU,MAAM;IACpB,IAAIa,YAAA,CAAaX,OAAA,KAAYL,KAAA,EAAO;MAClCH,WAAA,CAAYQ,OAAA,GAAUL,KAAK;MAC3BgB,YAAA,CAAaX,OAAA,GAAUL,KAAA;IACzB;EACF,GAAG,CAACA,KAAA,EAAOgB,YAAY,CAAC;EAExB,OAAO,CAAChB,KAAA,EAAOU,QAAA,EAAUb,WAAW;AACtC;AAEA,SAASiB,WAAWd,KAAA,EAAkD;EACpE,OAAO,OAAOA,KAAA,KAAU;AAC1B;;;AC/FA,YAAYiB,MAAA,MAAW;AACvB,SAASC,cAAA,QAAsB;AAe/B,IAAMC,UAAA,GAAaC,MAAA,CAAO,kBAAkB;AAoBrC,SAASC,4BACdC,OAAA,EACAC,QAAA,EACAC,UAAA,EACAC,IAAA,EACuC;EACvC,MAAM;IAAElC,IAAA,EAAMmC,eAAA;IAAiBlC,WAAA;IAAaC,QAAA,EAAUkC,YAAA;IAAcjC;EAAO,IAAI6B,QAAA;EAC/E,MAAMxB,YAAA,GAAe2B,eAAA,KAAoB;EAEzC,MAAMjC,QAAA,GAAWyB,cAAA,CAAeS,YAAY;EAM5C,IAAI,MAAuC;IACzC,MAAM1B,eAAA,GAAwBgB,MAAA,CAAAf,MAAA,CAAOwB,eAAA,KAAoB,MAAS;IAC5DT,MAAA,CAAAd,SAAA,CAAU,MAAM;MACpB,MAAMC,aAAA,GAAgBH,eAAA,CAAgBI,OAAA;MACtC,IAAID,aAAA,KAAkBL,YAAA,EAAc;QAClC,MAAMO,IAAA,GAAOF,aAAA,GAAgB,eAAe;QAC5C,MAAMG,EAAA,GAAKR,YAAA,GAAe,eAAe;QACzCS,OAAA,CAAQC,IAAA,CACN,GAAGf,MAAM,qBAAqBY,IAAI,OAAOC,EAAE,4KAC7C;MACF;MACAN,eAAA,CAAgBI,OAAA,GAAUN,YAAA;IAC5B,GAAG,CAACA,YAAA,EAAcL,MAAM,CAAC;EAC3B;EAIA,MAAMkC,IAAA,GAAwB,CAAC;IAAE,GAAGJ,UAAA;IAAYK,KAAA,EAAOrC;EAAY,CAAC;EACpE,IAAIiC,IAAA,EAAM;IAERG,IAAA,CAAKE,IAAA,CAAKL,IAAI;EAChB;EAEA,MAAM,CAACM,aAAA,EAAeC,QAAQ,IAAUf,MAAA,CAAAgB,UAAA,CACtC,CAACC,MAAA,EAAsBC,MAAA,KAAkD;IACvE,IAAIA,MAAA,CAAOC,IAAA,KAASjB,UAAA,EAAY;MAC9B,OAAO;QAAE,GAAGe,MAAA;QAAOL,KAAA,EAAOM,MAAA,CAAON;MAAM;IACzC;IAEA,MAAMQ,IAAA,GAAOf,OAAA,CAAQY,MAAA,EAAOC,MAAM;IAClC,IAAIpC,YAAA,IAAgB,CAACuC,MAAA,CAAOC,EAAA,CAAGF,IAAA,CAAKR,KAAA,EAAOK,MAAA,CAAML,KAAK,GAAG;MACvDpC,QAAA,CAAS4C,IAAA,CAAKR,KAAK;IACrB;IACA,OAAOQ,IAAA;EACT,GACA,GAAGT,IACL;EAEA,MAAMY,iBAAA,GAAoBT,aAAA,CAAcF,KAAA;EACxC,MAAMb,YAAA,GAAqBC,MAAA,CAAAf,MAAA,CAAOsC,iBAAiB;EAC7CvB,MAAA,CAAAd,SAAA,CAAU,MAAM;IACpB,IAAIa,YAAA,CAAaX,OAAA,KAAYmC,iBAAA,EAAmB;MAC9CxB,YAAA,CAAaX,OAAA,GAAUmC,iBAAA;MACvB,IAAI,CAACzC,YAAA,EAAc;QACjBN,QAAA,CAAS+C,iBAAiB;MAC5B;IACF;EACF,GAAG,CAAC/C,QAAA,EAAU+C,iBAAA,EAAmBxB,YAAA,EAAcjB,YAAY,CAAC;EAE5D,MAAM8B,KAAA,GAAcZ,MAAA,CAAAwB,OAAA,CAAQ,MAAM;IAChC,MAAMC,aAAA,GAAehB,eAAA,KAAoB;IACzC,IAAIgB,aAAA,EAAc;MAChB,OAAO;QAAE,GAAGX,aAAA;QAAeF,KAAA,EAAOH;MAAgB;IACpD;IAEA,OAAOK,aAAA;EACT,GAAG,CAACA,aAAA,EAAeL,eAAe,CAAC;EAE7BT,MAAA,CAAAd,SAAA,CAAU,MAAM;IAGpB,IAAIJ,YAAA,IAAgB,CAACuC,MAAA,CAAOC,EAAA,CAAGb,eAAA,EAAiBK,aAAA,CAAcF,KAAK,GAAG;MACpEG,QAAA,CAAS;QAAEI,IAAA,EAAMjB,UAAA;QAAYU,KAAA,EAAOH;MAAgB,CAAC;IACvD;EACF,GAAG,CAACA,eAAA,EAAiBK,aAAA,CAAcF,KAAA,EAAO9B,YAAY,CAAC;EAEvD,OAAO,CAAC8B,KAAA,EAAOG,QAA6B;AAC9C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}