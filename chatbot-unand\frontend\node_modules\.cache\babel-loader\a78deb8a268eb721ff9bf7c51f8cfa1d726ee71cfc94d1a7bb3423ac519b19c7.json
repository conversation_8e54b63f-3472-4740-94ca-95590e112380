{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 22v-4\",\n  key: \"1utk9m\"\n}], [\"path\", {\n  d: \"M7 12c-1.5 0-4.5 1.5-5 3 3.5 1.5 6 1 6 1-1.5 1.5-2 3.5-2 5 2.5 0 4.5-1.5 6-3 1.5 1.5 3.5 3 6 3 0-1.5-.5-3.5-2-5 0 0 2.5.5 6-1-.5-1.5-3.5-3-5-3 1.5-1 4-4 4-6-2.5 0-5.5 1.5-7 3 0-2.5-.5-5-2-7-1.5 2-2 4.5-2 7-1.5-1.5-4.5-3-7-3 0 2 2.5 5 4 6\",\n  key: \"1mezod\"\n}]];\nconst Cannabis = createLucideIcon(\"cannabis\", __iconNode);\nexport { __iconNode, Cannabis as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "Cannabis", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\node_modules\\lucide-react\\src\\icons\\cannabis.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 22v-4', key: '1utk9m' }],\n  [\n    'path',\n    {\n      d: 'M7 12c-1.5 0-4.5 1.5-5 3 3.5 1.5 6 1 6 1-1.5 1.5-2 3.5-2 5 2.5 0 4.5-1.5 6-3 1.5 1.5 3.5 3 6 3 0-1.5-.5-3.5-2-5 0 0 2.5.5 6-1-.5-1.5-3.5-3-5-3 1.5-1 4-4 4-6-2.5 0-5.5 1.5-7 3 0-2.5-.5-5-2-7-1.5 2-2 4.5-2 7-1.5-1.5-4.5-3-7-3 0 2 2.5 5 4 6',\n      key: '1mezod',\n    },\n  ],\n];\n\n/**\n * @component @name Cannabis\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMjJ2LTQiIC8+CiAgPHBhdGggZD0iTTcgMTJjLTEuNSAwLTQuNSAxLjUtNSAzIDMuNSAxLjUgNiAxIDYgMS0xLjUgMS41LTIgMy41LTIgNSAyLjUgMCA0LjUtMS41IDYtMyAxLjUgMS41IDMuNSAzIDYgMyAwLTEuNS0uNS0zLjUtMi01IDAgMCAyLjUuNSA2LTEtLjUtMS41LTMuNS0zLTUtMyAxLjUtMSA0LTQgNC02LTIuNSAwLTUuNSAxLjUtNyAzIDAtMi41LS41LTUtMi03LTEuNSAyLTIgNC41LTIgNy0xLjUtMS41LTQuNS0zLTctMyAwIDIgMi41IDUgNCA2IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/cannabis\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Cannabis = createLucideIcon('cannabis', __iconNode);\n\nexport default Cannabis;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CACE,QACA;EACED,CAAG;EACHC,GAAK;AAAA,EACP,CAEJ;AAaM,MAAAC,QAAA,GAAWC,gBAAiB,aAAYJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}