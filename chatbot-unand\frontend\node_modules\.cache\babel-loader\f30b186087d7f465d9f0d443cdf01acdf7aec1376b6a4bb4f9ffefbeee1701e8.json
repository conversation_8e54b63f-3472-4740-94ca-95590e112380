{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m11 7.601-5.994 8.19a1 1 0 0 0 .1 1.298l.817.818a1 1 0 0 0 1.314.087L15.09 12\",\n  key: \"80a601\"\n}], [\"path\", {\n  d: \"M16.5 21.174C15.5 20.5 14.372 20 13 20c-2.058 0-3.928 2.356-6 2-2.072-.356-2.775-3.369-1.5-4.5\",\n  key: \"j0ngtp\"\n}], [\"circle\", {\n  cx: \"16\",\n  cy: \"7\",\n  r: \"5\",\n  key: \"d08jfb\"\n}]];\nconst MicVocal = createLucideIcon(\"mic-vocal\", __iconNode);\nexport { __iconNode, MicVocal as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "cx", "cy", "r", "MicVocal", "createLucideIcon"], "sources": ["D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\node_modules\\lucide-react\\src\\icons\\mic-vocal.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'm11 7.601-5.994 8.19a1 1 0 0 0 .1 1.298l.817.818a1 1 0 0 0 1.314.087L15.09 12',\n      key: '80a601',\n    },\n  ],\n  [\n    'path',\n    {\n      d: 'M16.5 21.174C15.5 20.5 14.372 20 13 20c-2.058 0-3.928 2.356-6 2-2.072-.356-2.775-3.369-1.5-4.5',\n      key: 'j0ngtp',\n    },\n  ],\n  ['circle', { cx: '16', cy: '7', r: '5', key: 'd08jfb' }],\n];\n\n/**\n * @component @name MicVocal\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTEgNy42MDEtNS45OTQgOC4xOWExIDEgMCAwIDAgLjEgMS4yOThsLjgxNy44MThhMSAxIDAgMCAwIDEuMzE0LjA4N0wxNS4wOSAxMiIgLz4KICA8cGF0aCBkPSJNMTYuNSAyMS4xNzRDMTUuNSAyMC41IDE0LjM3MiAyMCAxMyAyMGMtMi4wNTggMC0zLjkyOCAyLjM1Ni02IDItMi4wNzItLjM1Ni0yLjc3NS0zLjM2OS0xLjUtNC41IiAvPgogIDxjaXJjbGUgY3g9IjE2IiBjeT0iNyIgcj0iNSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/mic-vocal\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst MicVocal = createLucideIcon('mic-vocal', __iconNode);\n\nexport default MicVocal;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AAAA,EAET,EACA,CACE,QACA;EACED,CAAG;EACHC,GAAK;AAAA,EAET,EACA,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAKC,CAAG;EAAKH,GAAK;AAAU,GACzD;AAaM,MAAAI,QAAA,GAAWC,gBAAiB,cAAaP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}