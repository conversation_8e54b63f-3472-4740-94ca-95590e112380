{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M8 6v6\",\n  key: \"18i7km\"\n}], [\"path\", {\n  d: \"M15 6v6\",\n  key: \"1sg6z9\"\n}], [\"path\", {\n  d: \"M2 12h19.6\",\n  key: \"de5uta\"\n}], [\"path\", {\n  d: \"M18 18h3s.5-1.7.8-2.8c.1-.4.2-.8.2-1.2 0-.4-.1-.8-.2-1.2l-1.4-5C20.1 6.8 19.1 6 18 6H4a2 2 0 0 0-2 2v10h3\",\n  key: \"1wwztk\"\n}], [\"circle\", {\n  cx: \"7\",\n  cy: \"18\",\n  r: \"2\",\n  key: \"19iecd\"\n}], [\"path\", {\n  d: \"M9 18h5\",\n  key: \"lrx6i\"\n}], [\"circle\", {\n  cx: \"16\",\n  cy: \"18\",\n  r: \"2\",\n  key: \"1v4tcr\"\n}]];\nconst Bus = createLucideIcon(\"bus\", __iconNode);\nexport { __iconNode, Bus as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "cx", "cy", "r", "Bus", "createLucideIcon"], "sources": ["D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\node_modules\\lucide-react\\src\\icons\\bus.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M8 6v6', key: '18i7km' }],\n  ['path', { d: 'M15 6v6', key: '1sg6z9' }],\n  ['path', { d: 'M2 12h19.6', key: 'de5uta' }],\n  [\n    'path',\n    {\n      d: 'M18 18h3s.5-1.7.8-2.8c.1-.4.2-.8.2-1.2 0-.4-.1-.8-.2-1.2l-1.4-5C20.1 6.8 19.1 6 18 6H4a2 2 0 0 0-2 2v10h3',\n      key: '1wwztk',\n    },\n  ],\n  ['circle', { cx: '7', cy: '18', r: '2', key: '19iecd' }],\n  ['path', { d: 'M9 18h5', key: 'lrx6i' }],\n  ['circle', { cx: '16', cy: '18', r: '2', key: '1v4tcr' }],\n];\n\n/**\n * @component @name Bus\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNOCA2djYiIC8+CiAgPHBhdGggZD0iTTE1IDZ2NiIgLz4KICA8cGF0aCBkPSJNMiAxMmgxOS42IiAvPgogIDxwYXRoIGQ9Ik0xOCAxOGgzcy41LTEuNy44LTIuOGMuMS0uNC4yLS44LjItMS4yIDAtLjQtLjEtLjgtLjItMS4ybC0xLjQtNUMyMC4xIDYuOCAxOS4xIDYgMTggNkg0YTIgMiAwIDAgMC0yIDJ2MTBoMyIgLz4KICA8Y2lyY2xlIGN4PSI3IiBjeT0iMTgiIHI9IjIiIC8+CiAgPHBhdGggZD0iTTkgMThoNSIgLz4KICA8Y2lyY2xlIGN4PSIxNiIgY3k9IjE4IiByPSIyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/bus\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Bus = createLucideIcon('bus', __iconNode);\n\nexport default Bus;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,QAAU;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CACE,QACA;EACED,CAAG;EACHC,GAAK;AAAA,EAET,EACA,CAAC,QAAU;EAAEC,EAAI;EAAKC,EAAI;EAAMC,CAAG;EAAKH,GAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAS,GACvC,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKH,GAAK;AAAU,GAC1D;AAaM,MAAAI,GAAA,GAAMC,gBAAiB,QAAOP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}