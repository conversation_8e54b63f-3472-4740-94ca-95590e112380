import os
import json
import uuid
from typing import List, Dict, Any, Optional
from qdrant_client import QdrantClient
from qdrant_client.models import Distance, VectorParams, PointStruct, Filter, FieldCondition, MatchValue
import google.generativeai as genai
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class QdrantService:
    """
    Pure Qdrant implementation using Qdrant client
    """
    
    def __init__(self, collection_name: str = "unand_documents"):
        """Initialize Qdrant service with local storage"""
        self.collection_name = collection_name
        self.embedding_model = "models/text-embedding-004"
        self.vector_size = 768  # Google text-embedding-004 dimension
        
        # Initialize Qdrant client with local storage
        self.data_dir = os.path.join(os.path.dirname(__file__), "qdrant_storage")
        os.makedirs(self.data_dir, exist_ok=True)
        
        try:
            # Use local file-based Qdrant
            self.client = QdrantClient(path=self.data_dir)
            logger.info(f"Qdrant client initialized with local storage: {self.data_dir}")
            
            # Create collection if it doesn't exist
            self._create_collection()
            
        except Exception as e:
            logger.error(f"Failed to initialize Qdrant client: {e}")
            raise
        
    def _create_collection(self):
        """Create Qdrant collection if it doesn't exist"""
        try:
            # Check if collection exists
            collections = self.client.get_collections()
            collection_names = [col.name for col in collections.collections]
            
            if self.collection_name not in collection_names:
                # Create new collection
                self.client.create_collection(
                    collection_name=self.collection_name,
                    vectors_config=VectorParams(
                        size=self.vector_size,
                        distance=Distance.COSINE
                    )
                )
                logger.info(f"Created Qdrant collection: {self.collection_name}")
            else:
                logger.info(f"Qdrant collection already exists: {self.collection_name}")
                
        except Exception as e:
            logger.error(f"Error creating Qdrant collection: {e}")
            raise
    
    def generate_embedding(self, text: str) -> List[float]:
        """Generate embedding for text using Google Gemini"""
        try:
            result = genai.embed_content(
                model=self.embedding_model,
                content=text,
                task_type="retrieval_document"
            )
            return result['embedding']
        except Exception as e:
            logger.error(f"Error generating embedding: {e}")
            raise
    
    def add_document(self, text: str, metadata: Dict[str, Any]) -> str:
        """Add a single document to Qdrant"""
        try:
            # Generate embedding
            embedding = self.generate_embedding(text)
            
            # Create unique ID
            doc_id = str(uuid.uuid4())
            
            # Create point
            point = PointStruct(
                id=doc_id,
                vector=embedding,
                payload={
                    "text": text,
                    "filename": metadata.get("filename", ""),
                    "filepath": metadata.get("filepath", ""),
                    "chunk_index": metadata.get("chunk_index", 0),
                    "total_chunks": metadata.get("total_chunks", 1)
                }
            )
            
            # Insert point
            self.client.upsert(
                collection_name=self.collection_name,
                points=[point]
            )
            
            logger.info(f"Added document to Qdrant: {doc_id}")
            return doc_id
            
        except Exception as e:
            logger.error(f"Error adding document to Qdrant: {e}")
            raise
    
    def add_documents_batch(self, documents: List[Dict[str, Any]]) -> List[str]:
        """Add multiple documents to Qdrant in batch"""
        try:
            points = []
            doc_ids = []
            
            for doc in documents:
                text = doc["text"]
                metadata = doc.get("metadata", {})
                
                # Generate embedding
                embedding = self.generate_embedding(text)
                
                # Create unique ID
                doc_id = str(uuid.uuid4())
                doc_ids.append(doc_id)
                
                # Create point
                point = PointStruct(
                    id=doc_id,
                    vector=embedding,
                    payload={
                        "text": text,
                        "filename": metadata.get("filename", ""),
                        "filepath": metadata.get("filepath", ""),
                        "chunk_index": metadata.get("chunk_index", 0),
                        "total_chunks": metadata.get("total_chunks", 1)
                    }
                )
                points.append(point)
            
            # Batch insert
            self.client.upsert(
                collection_name=self.collection_name,
                points=points
            )
            
            logger.info(f"Added {len(points)} documents to Qdrant")
            return doc_ids
            
        except Exception as e:
            logger.error(f"Error adding documents batch to Qdrant: {e}")
            raise
    
    def search(self, query: str, limit: int = 10, score_threshold: float = 0.6) -> List[Dict[str, Any]]:
        """Search for similar documents in Qdrant"""
        try:
            # Generate query embedding
            query_embedding = self.generate_embedding(query)
            
            # Search in Qdrant
            search_results = self.client.search(
                collection_name=self.collection_name,
                query_vector=query_embedding,
                limit=limit,
                score_threshold=score_threshold
            )
            
            # Format results
            results = []
            for result in search_results:
                results.append({
                    "id": result.id,
                    "score": result.score,
                    "text": result.payload["text"],
                    "filename": result.payload.get("filename", ""),
                    "filepath": result.payload.get("filepath", ""),
                    "chunk_index": result.payload.get("chunk_index", 0)
                })
            
            logger.info(f"Found {len(results)} similar documents for query")
            return results
            
        except Exception as e:
            logger.error(f"Error searching in Qdrant: {e}")
            raise
    
    def delete_by_filename(self, filename: str) -> bool:
        """Delete all documents with specific filename"""
        try:
            # Create filter for filename
            filter_condition = Filter(
                must=[
                    FieldCondition(
                        key="filename",
                        match=MatchValue(value=filename)
                    )
                ]
            )
            
            # Delete documents
            self.client.delete(
                collection_name=self.collection_name,
                points_selector=filter_condition
            )
            
            logger.info(f"Deleted documents with filename: {filename}")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting documents by filename: {e}")
            return False
    
    def get_collection_info(self) -> Dict[str, Any]:
        """Get information about the collection"""
        try:
            info = self.client.get_collection(self.collection_name)
            return {
                "name": self.collection_name,
                "vectors_count": info.vectors_count,
                "points_count": info.points_count,
                "status": info.status.value if hasattr(info.status, 'value') else str(info.status)
            }
        except Exception as e:
            logger.error(f"Error getting collection info: {e}")
            return {
                "name": self.collection_name,
                "vectors_count": 0,
                "points_count": 0,
                "status": "error"
            }
    
    def clear_collection(self) -> bool:
        """Clear all documents from collection"""
        try:
            # Delete all points
            self.client.delete(
                collection_name=self.collection_name,
                points_selector=Filter()
            )
            logger.info("Cleared all documents from Qdrant collection")
            return True
        except Exception as e:
            logger.error(f"Error clearing collection: {e}")
            return False
