# =============================================================================
# CHATBOT UNAND - GITIGNORE
# =============================================================================

# -----------------------------------------------------------------------------
# Python
# -----------------------------------------------------------------------------
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# -----------------------------------------------------------------------------
# Virtual Environment
# -----------------------------------------------------------------------------
venv/
env/
ENV/
.venv/
.env/

# -----------------------------------------------------------------------------
# Environment Variables
# -----------------------------------------------------------------------------
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# -----------------------------------------------------------------------------
# Database
# -----------------------------------------------------------------------------
*.db
*.sqlite
*.sqlite3
chatbot_unand.db

# -----------------------------------------------------------------------------
# Vector Database Storage
# -----------------------------------------------------------------------------
qdrant_storage/
qdrant_data/
vector_db/
faiss_index/

# -----------------------------------------------------------------------------
# Logs
# -----------------------------------------------------------------------------
*.log
logs/
log/

# -----------------------------------------------------------------------------
# Node.js (Frontend)
# -----------------------------------------------------------------------------
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity

# -----------------------------------------------------------------------------
# Build Output
# -----------------------------------------------------------------------------
build/
dist/
.next/
out/

# -----------------------------------------------------------------------------
# IDE & Editor
# -----------------------------------------------------------------------------
.vscode/
.idea/
*.swp
*.swo
*~

# -----------------------------------------------------------------------------
# OS Generated
# -----------------------------------------------------------------------------
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# -----------------------------------------------------------------------------
# Temporary Files
# -----------------------------------------------------------------------------
*.tmp
*.temp
nul

# -----------------------------------------------------------------------------
# Google Cloud Credentials
# -----------------------------------------------------------------------------
*.json
!package.json
!package-lock.json
!jsconfig.json
!components.json

# -----------------------------------------------------------------------------
# Backup Files
# -----------------------------------------------------------------------------
*_backup.py
*_old.py
*.bak

# -----------------------------------------------------------------------------
# Test Coverage
# -----------------------------------------------------------------------------
.coverage
htmlcov/
.pytest_cache/

# -----------------------------------------------------------------------------
# Documentation
# -----------------------------------------------------------------------------
docs/_build/
