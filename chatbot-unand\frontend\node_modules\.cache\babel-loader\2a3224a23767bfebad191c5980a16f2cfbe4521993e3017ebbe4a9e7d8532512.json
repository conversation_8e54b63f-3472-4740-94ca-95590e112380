{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m17 2 4 4-4 4\",\n  key: \"nntrym\"\n}], [\"path\", {\n  d: \"M3 11v-1a4 4 0 0 1 4-4h14\",\n  key: \"84bu3i\"\n}], [\"path\", {\n  d: \"m7 22-4-4 4-4\",\n  key: \"1wqhfi\"\n}], [\"path\", {\n  d: \"M21 13v1a4 4 0 0 1-4 4H3\",\n  key: \"1rx37r\"\n}], [\"path\", {\n  d: \"M11 10h1v4\",\n  key: \"70cz1p\"\n}]];\nconst Repeat1 = createLucideIcon(\"repeat-1\", __iconNode);\nexport { __iconNode, Repeat1 as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "Repeat1", "createLucideIcon"], "sources": ["D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\node_modules\\lucide-react\\src\\icons\\repeat-1.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm17 2 4 4-4 4', key: 'nntrym' }],\n  ['path', { d: 'M3 11v-1a4 4 0 0 1 4-4h14', key: '84bu3i' }],\n  ['path', { d: 'm7 22-4-4 4-4', key: '1wqhfi' }],\n  ['path', { d: 'M21 13v1a4 4 0 0 1-4 4H3', key: '1rx37r' }],\n  ['path', { d: 'M11 10h1v4', key: '70cz1p' }],\n];\n\n/**\n * @component @name Repeat1\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTcgMiA0IDQtNCA0IiAvPgogIDxwYXRoIGQ9Ik0zIDExdi0xYTQgNCAwIDAgMSA0LTRoMTQiIC8+CiAgPHBhdGggZD0ibTcgMjItNC00IDQtNCIgLz4KICA8cGF0aCBkPSJNMjEgMTN2MWE0IDQgMCAwIDEtNCA0SDMiIC8+CiAgPHBhdGggZD0iTTExIDEwaDF2NCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/repeat-1\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Repeat1 = createLucideIcon('repeat-1', __iconNode);\n\nexport default Repeat1;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,eAAiB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9C,CAAC,MAAQ;EAAED,CAAA,EAAG,2BAA6B;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1D,CAAC,MAAQ;EAAED,CAAA,EAAG,eAAiB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9C,CAAC,MAAQ;EAAED,CAAA,EAAG,0BAA4B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzD,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAU,GAC7C;AAaM,MAAAC,OAAA,GAAUC,gBAAiB,aAAYJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}