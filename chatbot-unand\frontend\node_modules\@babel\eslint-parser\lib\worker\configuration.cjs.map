{"version": 3, "names": ["babel", "require", "ESLINT_VERSION", "get<PERSON><PERSON>erPlugins", "babelOptions", "_babelOptions$parserO", "_babelOptions$parserO2", "babelParserPlugins", "parserOpts", "plugins", "estreeOptions", "classFeatures", "plugin", "Array", "isArray", "Object", "assign", "normalizeParserOptions", "options", "_options$allowImportE", "_options$ecmaFeatures", "_options$ecmaFeatures2", "sourceType", "filename", "filePath", "allowImportExportEverywhere", "allowSuperOutsideMethod", "allowReturnOutsideFunction", "ecmaFeatures", "globalReturn", "attachComment", "ranges", "tokens", "caller", "name", "validateResolvedConfig", "config", "parseOptions", "requireConfigFile", "hasFilesystemConfig", "error", "includes", "Error", "getDefaultParserOptions", "babelrc", "configFile", "browserslistConfigFile", "ignore", "only", "normalizeBabelParseConfig", "_x", "_normalizeBabelParseConfig", "apply", "arguments", "_asyncToGenerator", "loadPartialConfigAsync", "normalizeBabelParseConfigSync", "loadPartialConfigSync"], "sources": ["../../src/worker/configuration.cts"], "sourcesContent": ["import babel = require(\"./babel-core.cts\");\nimport ESLINT_VERSION = require(\"../utils/eslint-version.cts\");\nimport type { InputOptions } from \"@babel/core\";\nimport type { Options } from \"../types.cts\";\nimport type { PartialConfig } from \"../../../../packages/babel-core/src/config\";\n\n/**\n * Merge user supplied estree plugin options to default estree plugin options\n *\n * @returns {Array} Merged parser plugin descriptors\n */\nfunction getParserPlugins(\n  babelOptions: InputOptions,\n): InputOptions[\"parserOpts\"][\"plugins\"] {\n  const babelParserPlugins = babelOptions.parserOpts?.plugins ?? [];\n  const estreeOptions = { classFeatures: ESLINT_VERSION >= 8 };\n  for (const plugin of babelParserPlugins) {\n    if (Array.isArray(plugin) && plugin[0] === \"estree\") {\n      Object.assign(estreeOptions, plugin[1]);\n      break;\n    }\n  }\n  // estree must be the first parser plugin to work with other parser plugins\n  return [[\"estree\", estreeOptions], ...babelParserPlugins];\n}\n\nfunction normalizeParserOptions(options: Options): InputOptions & {\n  showIgnoredFiles?: boolean;\n} {\n  return {\n    // https://github.com/eslint/js/issues/519\n    sourceType: options.sourceType as \"module\" | \"script\",\n    filename: options.filePath,\n    ...options.babelOptions,\n    parserOpts: {\n      ...(process.env.BABEL_8_BREAKING\n        ? {}\n        : {\n            allowImportExportEverywhere:\n              options.allowImportExportEverywhere ?? false,\n            allowSuperOutsideMethod: true,\n          }),\n      allowReturnOutsideFunction:\n        options.ecmaFeatures?.globalReturn ??\n        (process.env.BABEL_8_BREAKING ? false : true),\n      ...options.babelOptions.parserOpts,\n      plugins: getParserPlugins(options.babelOptions),\n      // skip comment attaching for parsing performance\n      attachComment: false,\n      ranges: true,\n      tokens: true,\n    },\n    caller: {\n      name: \"@babel/eslint-parser\",\n      ...options.babelOptions.caller,\n    },\n  };\n}\n\nfunction validateResolvedConfig(\n  config: PartialConfig,\n  options: Options,\n  parseOptions: InputOptions,\n) {\n  if (config !== null) {\n    if (options.requireConfigFile !== false) {\n      if (!config.hasFilesystemConfig()) {\n        let error = `No Babel config file detected for ${config.options.filename}. Either disable config file checking with requireConfigFile: false, or configure Babel so that it can find the config files.`;\n\n        if (config.options.filename.includes(\"node_modules\")) {\n          error += `\\nIf you have a .babelrc.js file or use package.json#babel, keep in mind that it's not used when parsing dependencies. If you want your config to be applied to your whole app, consider using babel.config.js or babel.config.json instead.`;\n        }\n\n        throw new Error(error);\n      }\n    }\n    if (config.options) return config.options;\n  }\n\n  return getDefaultParserOptions(parseOptions);\n}\n\nfunction getDefaultParserOptions(options: InputOptions): InputOptions {\n  return {\n    plugins: [],\n    ...options,\n    babelrc: false,\n    configFile: false,\n    browserslistConfigFile: false,\n    ignore: null,\n    only: null,\n  };\n}\n\nexport async function normalizeBabelParseConfig(\n  options: Options,\n): Promise<InputOptions> {\n  const parseOptions = normalizeParserOptions(options);\n  const config = await babel.loadPartialConfigAsync(parseOptions);\n  return validateResolvedConfig(config, options, parseOptions);\n}\n\nexport function normalizeBabelParseConfigSync(options: Options): InputOptions {\n  const parseOptions = normalizeParserOptions(options);\n  const config = babel.loadPartialConfigSync(parseOptions);\n  return validateResolvedConfig(config, options, parseOptions);\n}\n"], "mappings": ";;;;;;;;;MAAOA,KAAK,GAAAC,OAAA,CAAW,kBAAkB;AAAA,MAClCC,cAAc,GAAAD,OAAA,CAAW,6BAA6B;AAU7D,SAASE,gBAAgBA,CACvBC,YAA0B,EACa;EAAA,IAAAC,qBAAA,EAAAC,sBAAA;EACvC,MAAMC,kBAAkB,IAAAF,qBAAA,IAAAC,sBAAA,GAAGF,YAAY,CAACI,UAAU,qBAAvBF,sBAAA,CAAyBG,OAAO,YAAAJ,qBAAA,GAAI,EAAE;EACjE,MAAMK,aAAa,GAAG;IAAEC,aAAa,EAAET,cAAc,IAAI;EAAE,CAAC;EAC5D,KAAK,MAAMU,MAAM,IAAIL,kBAAkB,EAAE;IACvC,IAAIM,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,IAAIA,MAAM,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;MACnDG,MAAM,CAACC,MAAM,CAACN,aAAa,EAAEE,MAAM,CAAC,CAAC,CAAC,CAAC;MACvC;IACF;EACF;EAEA,OAAO,CAAC,CAAC,QAAQ,EAAEF,aAAa,CAAC,EAAE,GAAGH,kBAAkB,CAAC;AAC3D;AAEA,SAASU,sBAAsBA,CAACC,OAAgB,EAE9C;EAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA;EACA,OAAAN,MAAA,CAAAC,MAAA;IAEEM,UAAU,EAAEJ,OAAO,CAACI,UAAiC;IACrDC,QAAQ,EAAEL,OAAO,CAACM;EAAQ,GACvBN,OAAO,CAACd,YAAY;IACvBI,UAAU,EAAAO,MAAA,CAAAC,MAAA,KAGJ;MACES,2BAA2B,GAAAN,qBAAA,GACzBD,OAAO,CAACO,2BAA2B,YAAAN,qBAAA,GAAI,KAAK;MAC9CO,uBAAuB,EAAE;IAC3B,CAAC;MACLC,0BAA0B,GAAAP,qBAAA,IAAAC,sBAAA,GACxBH,OAAO,CAACU,YAAY,qBAApBP,sBAAA,CAAsBQ,YAAY,YAAAT,qBAAA,GACM;IAAK,GAC5CF,OAAO,CAACd,YAAY,CAACI,UAAU;MAClCC,OAAO,EAAEN,gBAAgB,CAACe,OAAO,CAACd,YAAY,CAAC;MAE/C0B,aAAa,EAAE,KAAK;MACpBC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;IAAI,EACb;IACDC,MAAM,EAAAlB,MAAA,CAAAC,MAAA;MACJkB,IAAI,EAAE;IAAsB,GACzBhB,OAAO,CAACd,YAAY,CAAC6B,MAAM;EAC/B;AAEL;AAEA,SAASE,sBAAsBA,CAC7BC,MAAqB,EACrBlB,OAAgB,EAChBmB,YAA0B,EAC1B;EACA,IAAID,MAAM,KAAK,IAAI,EAAE;IACnB,IAAIlB,OAAO,CAACoB,iBAAiB,KAAK,KAAK,EAAE;MACvC,IAAI,CAACF,MAAM,CAACG,mBAAmB,CAAC,CAAC,EAAE;QACjC,IAAIC,KAAK,GAAG,qCAAqCJ,MAAM,CAAClB,OAAO,CAACK,QAAQ,+HAA+H;QAEvM,IAAIa,MAAM,CAAClB,OAAO,CAACK,QAAQ,CAACkB,QAAQ,CAAC,cAAc,CAAC,EAAE;UACpDD,KAAK,IAAI,8OAA8O;QACzP;QAEA,MAAM,IAAIE,KAAK,CAACF,KAAK,CAAC;MACxB;IACF;IACA,IAAIJ,MAAM,CAAClB,OAAO,EAAE,OAAOkB,MAAM,CAAClB,OAAO;EAC3C;EAEA,OAAOyB,uBAAuB,CAACN,YAAY,CAAC;AAC9C;AAEA,SAASM,uBAAuBA,CAACzB,OAAqB,EAAgB;EACpE,OAAAH,MAAA,CAAAC,MAAA;IACEP,OAAO,EAAE;EAAE,GACRS,OAAO;IACV0B,OAAO,EAAE,KAAK;IACdC,UAAU,EAAE,KAAK;IACjBC,sBAAsB,EAAE,KAAK;IAC7BC,MAAM,EAAE,IAAI;IACZC,IAAI,EAAE;EAAI;AAEd;AAAC,SAEqBC,yBAAyBA,CAAAC,EAAA;EAAA,OAAAC,0BAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAF,2BAAA;EAAAA,0BAAA,GAAAG,iBAAA,CAAxC,WACLpC,OAAgB,EACO;IACvB,MAAMmB,YAAY,GAAGpB,sBAAsB,CAACC,OAAO,CAAC;IACpD,MAAMkB,MAAM,SAASpC,KAAK,CAACuD,sBAAsB,CAAClB,YAAY,CAAC;IAC/D,OAAOF,sBAAsB,CAACC,MAAM,EAAElB,OAAO,EAAEmB,YAAY,CAAC;EAC9D,CAAC;EAAA,OAAAc,0BAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAEM,SAASG,6BAA6BA,CAACtC,OAAgB,EAAgB;EAC5E,MAAMmB,YAAY,GAAGpB,sBAAsB,CAACC,OAAO,CAAC;EACpD,MAAMkB,MAAM,GAAGpC,KAAK,CAACyD,qBAAqB,CAACpB,YAAY,CAAC;EACxD,OAAOF,sBAAsB,CAACC,MAAM,EAAElB,OAAO,EAAEmB,YAAY,CAAC;AAC9D", "ignoreList": []}