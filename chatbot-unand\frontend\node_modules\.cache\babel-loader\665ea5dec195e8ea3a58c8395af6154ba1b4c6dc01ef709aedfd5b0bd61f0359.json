{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M16.05 10.966a5 2.5 0 0 1-8.1 0\",\n  key: \"m5jpwb\"\n}], [\"path\", {\n  d: \"m16.923 14.049 4.48 2.04a1 1 0 0 1 .001 1.831l-8.574 3.9a2 2 0 0 1-1.66 0l-8.574-3.91a1 1 0 0 1 0-1.83l4.484-2.04\",\n  key: \"rbg3g8\"\n}], [\"path\", {\n  d: \"M16.949 14.14a5 2.5 0 1 1-9.9 0L10.063 3.5a2 2 0 0 1 3.874 0z\",\n  key: \"vap8c8\"\n}], [\"path\", {\n  d: \"M9.194 6.57a5 2.5 0 0 0 5.61 0\",\n  key: \"15hn5c\"\n}]];\nconst TrafficCone = createLucideIcon(\"traffic-cone\", __iconNode);\nexport { __iconNode, TrafficCone as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "TrafficCone", "createLucideIcon"], "sources": ["D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\node_modules\\lucide-react\\src\\icons\\traffic-cone.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M16.05 10.966a5 2.5 0 0 1-8.1 0', key: 'm5jpwb' }],\n  [\n    'path',\n    {\n      d: 'm16.923 14.049 4.48 2.04a1 1 0 0 1 .001 1.831l-8.574 3.9a2 2 0 0 1-1.66 0l-8.574-3.91a1 1 0 0 1 0-1.83l4.484-2.04',\n      key: 'rbg3g8',\n    },\n  ],\n  ['path', { d: 'M16.949 14.14a5 2.5 0 1 1-9.9 0L10.063 3.5a2 2 0 0 1 3.874 0z', key: 'vap8c8' }],\n  ['path', { d: 'M9.194 6.57a5 2.5 0 0 0 5.61 0', key: '15hn5c' }],\n];\n\n/**\n * @component @name TrafficCone\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYuMDUgMTAuOTY2YTUgMi41IDAgMCAxLTguMSAwIiAvPgogIDxwYXRoIGQ9Im0xNi45MjMgMTQuMDQ5IDQuNDggMi4wNGExIDEgMCAwIDEgLjAwMSAxLjgzMWwtOC41NzQgMy45YTIgMiAwIDAgMS0xLjY2IDBsLTguNTc0LTMuOTFhMSAxIDAgMCAxIDAtMS44M2w0LjQ4NC0yLjA0IiAvPgogIDxwYXRoIGQ9Ik0xNi45NDkgMTQuMTRhNSAyLjUgMCAxIDEtOS45IDBMMTAuMDYzIDMuNWEyIDIgMCAwIDEgMy44NzQgMHoiIC8+CiAgPHBhdGggZD0iTTkuMTk0IDYuNTdhNSAyLjUgMCAwIDAgNS42MSAwIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/traffic-cone\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst TrafficCone = createLucideIcon('traffic-cone', __iconNode);\n\nexport default TrafficCone;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,iCAAmC;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChE,CACE,QACA;EACED,CAAG;EACHC,GAAK;AAAA,EAET,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,+DAAiE;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9F,CAAC,MAAQ;EAAED,CAAA,EAAG,gCAAkC;EAAAC,GAAA,EAAK;AAAU,GACjE;AAaM,MAAAC,WAAA,GAAcC,gBAAiB,iBAAgBJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}