{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"18\",\n  height: \"14\",\n  x: \"3\",\n  y: \"5\",\n  rx: \"2\",\n  ry: \"2\",\n  key: \"12ruh7\"\n}], [\"path\", {\n  d: \"M7 15h4M15 15h2M7 11h2M13 11h4\",\n  key: \"1ueiar\"\n}]];\nconst Captions = createLucideIcon(\"captions\", __iconNode);\nexport { __iconNode, Captions as default };", "map": {"version": 3, "names": ["__iconNode", "width", "height", "x", "y", "rx", "ry", "key", "d", "Captions", "createLucideIcon"], "sources": ["D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\node_modules\\lucide-react\\src\\icons\\captions.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['rect', { width: '18', height: '14', x: '3', y: '5', rx: '2', ry: '2', key: '12ruh7' }],\n  ['path', { d: 'M7 15h4M15 15h2M7 11h2M13 11h4', key: '1ueiar' }],\n];\n\n/**\n * @component @name Captions\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTgiIGhlaWdodD0iMTQiIHg9IjMiIHk9IjUiIHJ4PSIyIiByeT0iMiIgLz4KICA8cGF0aCBkPSJNNyAxNWg0TTE1IDE1aDJNNyAxMWgyTTEzIDExaDQiIC8+Cjwvc3ZnPg==) - https://lucide.dev/icons/captions\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Captions = createLucideIcon('captions', __iconNode);\n\nexport default Captions;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,KAAO;EAAMC,MAAA,EAAQ,IAAM;EAAAC,CAAA,EAAG,GAAK;EAAAC,CAAA,EAAG;EAAKC,EAAI;EAAKC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvF,CAAC,MAAQ;EAAEC,CAAA,EAAG,gCAAkC;EAAAD,GAAA,EAAK;AAAU,GACjE;AAaM,MAAAE,QAAA,GAAWC,gBAAiB,aAAYV,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}