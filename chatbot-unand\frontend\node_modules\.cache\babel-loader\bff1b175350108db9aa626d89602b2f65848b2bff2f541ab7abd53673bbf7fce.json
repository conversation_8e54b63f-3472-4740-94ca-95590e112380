{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"6\",\n  cy: \"15\",\n  r: \"4\",\n  key: \"vux9w4\"\n}], [\"circle\", {\n  cx: \"18\",\n  cy: \"15\",\n  r: \"4\",\n  key: \"18o8ve\"\n}], [\"path\", {\n  d: \"M14 15a2 2 0 0 0-2-2 2 2 0 0 0-2 2\",\n  key: \"1ag4bs\"\n}], [\"path\", {\n  d: \"M2.5 13 5 7c.7-1.3 1.4-2 3-2\",\n  key: \"1hm1gs\"\n}], [\"path\", {\n  d: \"M21.5 13 19 7c-.7-1.3-1.5-2-3-2\",\n  key: \"1r31ai\"\n}]];\nconst Glasses = createLucideIcon(\"glasses\", __iconNode);\nexport { __iconNode, Glass<PERSON> as default };", "map": {"version": 3, "names": ["__iconNode", "cx", "cy", "r", "key", "d", "Glasses", "createLucideIcon"], "sources": ["D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\node_modules\\lucide-react\\src\\icons\\glasses.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '6', cy: '15', r: '4', key: 'vux9w4' }],\n  ['circle', { cx: '18', cy: '15', r: '4', key: '18o8ve' }],\n  ['path', { d: 'M14 15a2 2 0 0 0-2-2 2 2 0 0 0-2 2', key: '1ag4bs' }],\n  ['path', { d: 'M2.5 13 5 7c.7-1.3 1.4-2 3-2', key: '1hm1gs' }],\n  ['path', { d: 'M21.5 13 19 7c-.7-1.3-1.5-2-3-2', key: '1r31ai' }],\n];\n\n/**\n * @component @name Glasses\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSI2IiBjeT0iMTUiIHI9IjQiIC8+CiAgPGNpcmNsZSBjeD0iMTgiIGN5PSIxNSIgcj0iNCIgLz4KICA8cGF0aCBkPSJNMTQgMTVhMiAyIDAgMCAwLTItMiAyIDIgMCAwIDAtMiAyIiAvPgogIDxwYXRoIGQ9Ik0yLjUgMTMgNSA3Yy43LTEuMyAxLjQtMiAzLTIiIC8+CiAgPHBhdGggZD0iTTIxLjUgMTMgMTkgN2MtLjctMS4zLTEuNS0yLTMtMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/glasses\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Glasses = createLucideIcon('glasses', __iconNode);\n\nexport default Glasses;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,QAAU;EAAEC,EAAI;EAAKC,EAAI;EAAMC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACvD,CAAC,QAAU;EAAEH,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAEC,CAAA,EAAG,oCAAsC;EAAAD,GAAA,EAAK;AAAA,CAAU,GACnE,CAAC,MAAQ;EAAEC,CAAA,EAAG,8BAAgC;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC7D,CAAC,MAAQ;EAAEC,CAAA,EAAG,iCAAmC;EAAAD,GAAA,EAAK;AAAU,GAClE;AAaM,MAAAE,OAAA,GAAUC,gBAAiB,YAAWP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}