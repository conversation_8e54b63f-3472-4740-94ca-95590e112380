{"version": 3, "names": ["path", "require", "ACTIONS", "exports", "GET_VERSION", "GET_TYPES_INFO", "GET_VISITOR_KEYS", "GET_TOKEN_LABELS", "MAYBE_PARSE", "MAYBE_PARSE_SYNC", "_send", "WeakMap", "_vCache", "_tiCache", "_vkCache", "_tlCache", "Client", "constructor", "send", "_classPrivateFieldInitSpec", "_classPrivateFieldSet", "getVersion", "_classPrivateFieldGet2", "_classPrivateFieldGet", "call", "undefined", "getTypesInfo", "_classPrivateFieldGet3", "getVisitorKeys", "_classPrivateFieldGet4", "getTokLabels", "_classPrivateFieldGet5", "<PERSON><PERSON><PERSON><PERSON>", "code", "options", "_worker", "WorkerClient", "action", "payload", "signal", "Int32Array", "SharedArrayBuffer", "subChannel", "_get_worker_threads", "MessageChannel", "postMessage", "port", "port1", "Atomics", "wait", "message", "receiveMessageOnPort", "port2", "error", "Object", "assign", "errorData", "result", "Worker", "resolve", "__dirname", "env", "SHARE_ENV", "unref", "_this", "_worker_threads_cache2", "_worker_threads_cache", "_", "_LocalClient", "_handleMessage", "LocalClient", "_assertClassBrand$_", "_assert<PERSON>lassBrand"], "sources": ["../src/client.cts"], "sourcesContent": ["import type { Options } from \"./types.cts\";\n\nimport path = require(\"path\");\n\nexport const enum ACTIONS {\n  GET_VERSION = \"GET_VERSION\",\n  GET_TYPES_INFO = \"GET_TYPES_INFO\",\n  GET_VISITOR_KEYS = \"GET_VISITOR_KEYS\",\n  GET_TOKEN_LABELS = \"GET_TOKEN_LABELS\",\n  MAYBE_PARSE = \"MAYBE_PARSE\",\n  MAYBE_PARSE_SYNC = \"MAYBE_PARSE_SYNC\",\n}\n\nexport class Client {\n  #send;\n\n  constructor(send: Function) {\n    this.#send = send;\n  }\n\n  #vCache: string;\n  getVersion() {\n    return (this.#vCache ??= this.#send(ACTIONS.GET_VERSION, undefined));\n  }\n\n  #tiCache: any;\n  getTypesInfo() {\n    return (this.#tiCache ??= this.#send(ACTIONS.GET_TYPES_INFO, undefined));\n  }\n\n  #vkCache: any;\n  getVisitorKeys() {\n    return (this.#vkCache ??= this.#send(ACTIONS.GET_VISITOR_KEYS, undefined));\n  }\n\n  #tlCache: any;\n  getTokLabels() {\n    return (this.#tlCache ??= this.#send(ACTIONS.GET_TOKEN_LABELS, undefined));\n  }\n\n  maybeParse(code: string, options: Options) {\n    return this.#send(ACTIONS.MAYBE_PARSE, { code, options });\n  }\n}\n\n// We need to run Babel in a worker for two reasons:\n// 1. ESLint workers must be CJS files, and this is a problem\n//    since Babel 8+ uses native ESM\n// 2. ESLint parsers must run synchronously, but many steps\n//    of Babel's config loading (which is done for each file)\n//    can be asynchronous\n// If ESLint starts supporting async parsers, we can move\n// everything back to the main thread.\nexport class WorkerClient extends Client {\n  static #worker_threads_cache: typeof import(\"worker_threads\");\n  static get #worker_threads() {\n    return (WorkerClient.#worker_threads_cache ??= require(\"node:worker_threads\"));\n  }\n\n  #worker = new WorkerClient.#worker_threads.Worker(\n    path.resolve(__dirname, \"../lib/worker/index.cjs\"),\n    { env: WorkerClient.#worker_threads.SHARE_ENV },\n  );\n\n  constructor() {\n    super((action: ACTIONS, payload: any) => {\n      // We create a new SharedArrayBuffer every time rather than reusing\n      // the same one, otherwise sometimes its contents get corrupted and\n      // Atomics.wait wakes up too early.\n      // https://github.com/babel/babel/pull/14541\n      const signal = new Int32Array(new SharedArrayBuffer(8));\n\n      const subChannel = new WorkerClient.#worker_threads.MessageChannel();\n\n      this.#worker.postMessage(\n        { signal, port: subChannel.port1, action, payload },\n        [subChannel.port1],\n      );\n\n      Atomics.wait(signal, 0, 0);\n      const { message } = WorkerClient.#worker_threads.receiveMessageOnPort(\n        subChannel.port2,\n      );\n\n      if (message.error) throw Object.assign(message.error, message.errorData);\n      else return message.result;\n    });\n\n    // The worker will never exit by itself. Prevent it from keeping\n    // the main process alive.\n    this.#worker.unref();\n  }\n}\n\nif (!USE_ESM) {\n  exports.LocalClient = class LocalClient extends Client {\n    static #handleMessage: Function;\n\n    constructor() {\n      LocalClient.#handleMessage ??= require(\"./worker/handle-message.cjs\");\n\n      super((action: ACTIONS, payload: any) => {\n        return LocalClient.#handleMessage(\n          action === ACTIONS.MAYBE_PARSE ? ACTIONS.MAYBE_PARSE_SYNC : action,\n          payload,\n        );\n      });\n    }\n  };\n}\n"], "mappings": ";;;;;;;;;;;MAEOA,IAAI,GAAAC,OAAA,CAAW,MAAM;AAAA,MAEVC,OAAO,GAAAC,OAAA,CAAAD,OAAA;EAAAE,WAAA;EAAAC,cAAA;EAAAC,gBAAA;EAAAC,gBAAA;EAAAC,WAAA;EAAAC,gBAAA;AAAA;AAAA,IAAAC,KAAA,OAAAC,OAAA;AAAA,IAAAC,OAAA,OAAAD,OAAA;AAAA,IAAAE,QAAA,OAAAF,OAAA;AAAA,IAAAG,QAAA,OAAAH,OAAA;AAAA,IAAAI,QAAA,OAAAJ,OAAA;AASlB,MAAMK,MAAM,CAAC;EAGlBC,WAAWA,CAACC,IAAc,EAAE;IAF5BC,0BAAA,OAAAT,KAAK;IAMLS,0BAAA,OAAAP,OAAO;IAKPO,0BAAA,OAAAN,QAAQ;IAKRM,0BAAA,OAAAL,QAAQ;IAKRK,0BAAA,OAAAJ,QAAQ;IAlBNK,qBAAA,CAAKV,KAAK,EAAV,IAAI,EAASQ,IAAJ,CAAC;EACZ;EAGAG,UAAUA,CAAA,EAAG;IAAA,IAAAC,sBAAA;IACX,QAAAA,sBAAA,GAAQC,qBAAA,CAAKX,OAAO,EAAZ,IAAW,CAAC,YAAAU,sBAAA,GAAZF,qBAAA,CAAKR,OAAO,EAAZ,IAAI,EAAaW,qBAAA,CAAKb,KAAK,EAAV,IAAS,CAAC,CAAAc,IAAA,CAAV,IAAI,EAAOtB,OAAO,CAACE,WAAW,EAAEqB,SAAS,CAA/C,CAAC;EACtB;EAGAC,YAAYA,CAAA,EAAG;IAAA,IAAAC,sBAAA;IACb,QAAAA,sBAAA,GAAQJ,qBAAA,CAAKV,QAAQ,EAAb,IAAY,CAAC,YAAAc,sBAAA,GAAbP,qBAAA,CAAKP,QAAQ,EAAb,IAAI,EAAcU,qBAAA,CAAKb,KAAK,EAAV,IAAS,CAAC,CAAAc,IAAA,CAAV,IAAI,EAAOtB,OAAO,CAACG,cAAc,EAAEoB,SAAS,CAAlD,CAAC;EACvB;EAGAG,cAAcA,CAAA,EAAG;IAAA,IAAAC,sBAAA;IACf,QAAAA,sBAAA,GAAQN,qBAAA,CAAKT,QAAQ,EAAb,IAAY,CAAC,YAAAe,sBAAA,GAAbT,qBAAA,CAAKN,QAAQ,EAAb,IAAI,EAAcS,qBAAA,CAAKb,KAAK,EAAV,IAAS,CAAC,CAAAc,IAAA,CAAV,IAAI,EAAOtB,OAAO,CAACI,gBAAgB,EAAEmB,SAAS,CAApD,CAAC;EACvB;EAGAK,YAAYA,CAAA,EAAG;IAAA,IAAAC,sBAAA;IACb,QAAAA,sBAAA,GAAQR,qBAAA,CAAKR,QAAQ,EAAb,IAAY,CAAC,YAAAgB,sBAAA,GAAbX,qBAAA,CAAKL,QAAQ,EAAb,IAAI,EAAcQ,qBAAA,CAAKb,KAAK,EAAV,IAAS,CAAC,CAAAc,IAAA,CAAV,IAAI,EAAOtB,OAAO,CAACK,gBAAgB,EAAEkB,SAAS,CAApD,CAAC;EACvB;EAEAO,UAAUA,CAACC,IAAY,EAAEC,OAAgB,EAAE;IACzC,OAAOX,qBAAA,CAAKb,KAAK,EAAV,IAAS,CAAC,CAAAc,IAAA,CAAV,IAAI,EAAOtB,OAAO,CAACM,WAAW,EAAE;MAAEyB,IAAI;MAAEC;IAAQ,CAAC;EAC1D;AACF;AAAC/B,OAAA,CAAAa,MAAA,GAAAA,MAAA;AAAA,IAAAmB,OAAA,OAAAxB,OAAA;AAUM,MAAMyB,YAAY,SAASpB,MAAM,CAAC;EAWvCC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAACoB,MAAe,EAAEC,OAAY,KAAK;MAKvC,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,IAAIC,iBAAiB,CAAC,CAAC,CAAC,CAAC;MAEvD,MAAMC,UAAU,GAAG,KAAiBC,mBAAe,CAA5BP,YAA2B,CAAC,CAACQ,cAAc,EAAC,CAAC;MAEpErB,qBAAA,CAAKY,OAAO,EAAZ,IAAW,CAAC,CAACU,WAAW,CACtB;QAAEN,MAAM;QAAEO,IAAI,EAAEJ,UAAU,CAACK,KAAK;QAAEV,MAAM;QAAEC;MAAQ,CAAC,EACnD,CAACI,UAAU,CAACK,KAAK,CACnB,CAAC;MAEDC,OAAO,CAACC,IAAI,CAACV,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;MAC1B,MAAM;QAAEW;MAAQ,CAAC,GAAgBP,mBAAe,CAA5BP,YAA2B,CAAC,CAACe,oBAAoB,CACnET,UAAU,CAACU,KACb,CAAC;MAED,IAAIF,OAAO,CAACG,KAAK,EAAE,MAAMC,MAAM,CAACC,MAAM,CAACL,OAAO,CAACG,KAAK,EAAEH,OAAO,CAACM,SAAS,CAAC,CAAC,KACpE,OAAON,OAAO,CAACO,MAAM;IAC5B,CAAC,CAAC;IA3BJtC,0BAAA,OAAAgB,OAAO,EAAG,KAAiBQ,mBAAe,CAA5BP,YAA2B,CAAC,CAACsB,MAAM,EAC/C1D,IAAI,CAAC2D,OAAO,CAACC,SAAS,EAAE,yBAAyB,CAAC,EAClD;MAAEC,GAAG,EAAelB,mBAAe,CAA5BP,YAA2B,CAAC,CAAC0B;IAAU,CAChD,CAAC;IA4BCvC,qBAAA,CAAKY,OAAO,EAAZ,IAAW,CAAC,CAAC4B,KAAK,CAAC,CAAC;EACtB;AACF;AAAC5D,OAAA,CAAAiC,YAAA,GAAAA,YAAA;AAAA,SAAAO,oBAAAqB,KAAA,EArC8B;EAAA,IAAAC,sBAAA;EAC3B,QAAAA,sBAAA,GAAqBC,qBAAqB,CAAAC,CAAA,YAAAF,sBAAA,GAArBC,qBAAqB,CAAAC,CAAA,GAAKlE,OAAO,CAAC,gBAAqB,CAAlC;AAC5C;AAAC,IAAAiE,qBAAA;EAAAC,CAAA;AAAA;AAqCW;EAAA,IAAAC,YAAA,EAAAC,cAAA;EACZlE,OAAO,CAACmE,WAAW,IAAAF,YAAA,GAAG,MAAME,WAAW,SAAStD,MAAM,CAAC;IAGrDC,WAAWA,CAAA,EAAG;MAAA,IAAAsD,mBAAA;MACZ,CAAAA,mBAAA,GAAAC,iBAAA,CAAAJ,YAAA,EAAAE,WAAW,EAACD,cAAc,EAAAF,CAAA,YAAAI,mBAAA,GAAdF,cAAc,CAAAF,CAAA,GAAAK,iBAAA,CAAAJ,YAAA,EAA1BE,WAAW,EAAoBrE,OAAO,CAAC,6BAA6B,CAAC,CAA3C;MAE1B,KAAK,CAAC,CAACoC,MAAe,EAAEC,OAAY,KAAK;QACvC,OAAOkC,iBAAA,CAAAJ,YAAA,EAAAE,WAAW,EAACD,cAAc,EAAAF,CAAA,CAAA3C,IAAA,CAA1B8C,WAAW,EAChBjC,MAAM,KAAKnC,OAAO,CAACM,WAAW,GAAGN,OAAO,CAACO,gBAAgB,GAAG4B,MAAM,EAClEC,OAAO;MAEX,CAAC,CAAC;IACJ;EACF,CAAC,EAAA+B,cAAA;IAAAF,CAAA;EAAA,GAAAC,YAAA;AACH", "ignoreList": []}