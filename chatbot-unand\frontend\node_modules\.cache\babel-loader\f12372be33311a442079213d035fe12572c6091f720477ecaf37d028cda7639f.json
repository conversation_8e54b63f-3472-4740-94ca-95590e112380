{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M17 3h4v4\",\n  key: \"19p9u1\"\n}], [\"path\", {\n  d: \"M18.575 11.082a13 13 0 0 1 1.048 9.027 1.17 1.17 0 0 1-1.914.597L14 17\",\n  key: \"12t3w9\"\n}], [\"path\", {\n  d: \"M7 10 3.29 6.29a1.17 1.17 0 0 1 .6-1.91 13 13 0 0 1 9.03 1.05\",\n  key: \"ogng5l\"\n}], [\"path\", {\n  d: \"M7 14a1.7 1.7 0 0 0-1.207.5l-2.646 2.646A.5.5 0 0 0 3.5 18H5a1 1 0 0 1 1 1v1.5a.5.5 0 0 0 .854.354L9.5 18.207A1.7 1.7 0 0 0 10 17v-2a1 1 0 0 0-1-1z\",\n  key: \"8v3fy2\"\n}], [\"path\", {\n  d: \"M9.707 14.293 21 3\",\n  key: \"ydm3bn\"\n}]];\nconst BowArrow = createLucideIcon(\"bow-arrow\", __iconNode);\nexport { __iconNode, BowArrow as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "BowArrow", "createLucideIcon"], "sources": ["D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\node_modules\\lucide-react\\src\\icons\\bow-arrow.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M17 3h4v4', key: '19p9u1' }],\n  [\n    'path',\n    { d: 'M18.575 11.082a13 13 0 0 1 1.048 9.027 1.17 1.17 0 0 1-1.914.597L14 17', key: '12t3w9' },\n  ],\n  ['path', { d: 'M7 10 3.29 6.29a1.17 1.17 0 0 1 .6-1.91 13 13 0 0 1 9.03 1.05', key: 'ogng5l' }],\n  [\n    'path',\n    {\n      d: 'M7 14a1.7 1.7 0 0 0-1.207.5l-2.646 2.646A.5.5 0 0 0 3.5 18H5a1 1 0 0 1 1 1v1.5a.5.5 0 0 0 .854.354L9.5 18.207A1.7 1.7 0 0 0 10 17v-2a1 1 0 0 0-1-1z',\n      key: '8v3fy2',\n    },\n  ],\n  ['path', { d: 'M9.707 14.293 21 3', key: 'ydm3bn' }],\n];\n\n/**\n * @component @name BowArrow\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTcgM2g0djQiIC8+CiAgPHBhdGggZD0iTTE4LjU3NSAxMS4wODJhMTMgMTMgMCAwIDEgMS4wNDggOS4wMjcgMS4xNyAxLjE3IDAgMCAxLTEuOTE0LjU5N0wxNCAxNyIgLz4KICA8cGF0aCBkPSJNNyAxMCAzLjI5IDYuMjlhMS4xNyAxLjE3IDAgMCAxIC42LTEuOTEgMTMgMTMgMCAwIDEgOS4wMyAxLjA1IiAvPgogIDxwYXRoIGQ9Ik03IDE0YTEuNyAxLjcgMCAwIDAtMS4yMDcuNWwtMi42NDYgMi42NDZBLjUuNSAwIDAgMCAzLjUgMThINWExIDEgMCAwIDEgMSAxdjEuNWEuNS41IDAgMCAwIC44NTQuMzU0TDkuNSAxOC4yMDdBMS43IDEuNyAwIDAgMCAxMCAxN3YtMmExIDEgMCAwIDAtMS0xeiIgLz4KICA8cGF0aCBkPSJNOS43MDcgMTQuMjkzIDIxIDMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/bow-arrow\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst BowArrow = createLucideIcon('bow-arrow', __iconNode);\n\nexport default BowArrow;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CACE,QACA;EAAED,CAAA,EAAG,wEAA0E;EAAAC,GAAA,EAAK;AAAS,EAC/F,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,+DAAiE;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9F,CACE,QACA;EACED,CAAG;EACHC,GAAK;AAAA,EAET,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,oBAAsB;EAAAC,GAAA,EAAK;AAAU,GACrD;AAaM,MAAAC,QAAA,GAAWC,gBAAiB,cAAaJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}