{"mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AEAe,uDAA4B;IAOzC,YACE,QAAgB,EAChB,GAAW,EACX,MAAc,EACd,MAAc,EACd,GAAW,CACX;QACA,KAAK,CAAC,WAAW,MAAM,SAAS,MAAM,SAAS,OAAO;QACtD,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,MAAM,GAAG;IAChB;AACF;;;;;;;;ACrBA;;CAEC,GACc;IAKb,YACE,KAAqC,EACrC,GAAmC,EACnC,MAAc,CACd;QACA,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,MAAM,GAAG;IAChB;AACF;;;;;;ACdO,IAAA,AAAK,mEAAA;;;;;;;;;;;;;;;;;;;;WAAA;;;;ACHL,MAAM,4CAAW;AAkBjB,MAAM,4CAAyB,CACpC,QACA,QACA;IAEA,IAAI,kBAAkB;IACtB,IAAI,UAAU;IACd,GAAG;QACD,MAAM,MAAM,OAAO,GAAG,CAAC,CAAA,IAAK,OAAO,OAAO,CAAC,GAAG;QAC9C,IAAI,IAAI,CAAC,OAAO,OAAO,CAAC,MAAM;QAC9B,MAAM,WAAW,IAAI,MAAM,CAAC,CAAA,IAAK,MAAM;QACvC,IAAI,SAAS,MAAM,KAAK,GACtB,OAAO;QAGT,MAAM,QAAQ,KAAK,GAAG,IAAI;QAC1B,IAAI,MAAM,CAAC,MAAM,KAAK,MAAM;YAC1B,kBAAkB,QAAQ;YAC1B;QACF,OACE,OAAO;IAEX,QAAS,UAAU,GAAG;IAEtB,MAAM,IAAI,MAAM;AAClB;AAyBO,MAAM,4CAAyC,CACpD,QACA,QACA;IAEA,IAAI,wBAAwB;IAC5B,IAAI,UAAU;IAEd,GAAG;QACD,MAAM,MAAM,OAAO,GAAG,CAAC,CAAA,IAAK,OAAO,OAAO,CAAC,GAAG;QAE9C,IAAI,IAAI,CAAC,OAAO,OAAO,CAAC,KAAK;QAC7B,IAAI,IAAI,CAAC,OAAO,OAAO,CAAC,KAAK;QAC7B,IAAI,IAAI,CAAC,OAAO,OAAO,CAAC,KAAK;QAC7B,IAAI,IAAI,CAAC,OAAO,OAAO,CAAC,MAAM;QAE9B,MAAM,WAAW,IAAI,MAAM,CAAC,CAAA,IAAK,MAAM;QACvC,IAAI,SAAS,MAAM,KAAK,GACtB,OAAO;QAGT,MAAM,gBAAgB,KAAK,GAAG,IAAI;QAClC,MAAM,OAAO,MAAM,CAAC,cAAc;QAClC,OAAQ;YACN,KAAK;gBACH,wBAAwB,gBAAgB;gBACxC;YACF,KAAK;gBACH;oBACE,MAAM,cAAc,0CAClB,QACA;wBAAC;qBAAI,EACL,gBAAgB;oBAElB,IAAI,gBAAgB,IAClB,OAAO;oBAET,wBAAwB,cAAc;gBACxC;gBACA;YACF,KAAK;gBACH;oBACE,MAAM,mBAAmB,0CACvB,QACA;wBAAC;qBAAI,EACL,gBAAgB;oBAElB,IAAI,qBAAqB,IACvB,OAAO;oBAET,wBAAwB,mBAAmB;gBAC7C;gBACA;YACF,KAAK;gBACH;oBACE,MAAM,mBAAmB,0CACvB,QACA;wBAAC;qBAAI,EACL,gBAAgB;oBAElB,IAAI,qBAAqB,IACvB,OAAO;oBAET,wBAAwB,mBAAmB;gBAC7C;gBACA;YACF;gBACE,OAAO;QACX;QACA;IACF,QAAS,UAAU,GAAG;IAEtB,MAAM,IAAI,MAAM;AAClB;AAYO,MAAM,4CAAkC,CAC7C,QACA;IAEA,MAAM,SAAwB,EAAE;IAChC,IAAI,kBAAkB;IACtB,MAAO,kBAAkB,OAAO,MAAM,CAAE;QACtC,MAAM,QAAQ,0CACZ,QACA,QACA;QAEF,IAAI,UAAU,IAAI;YAChB,OAAO,IAAI,CAAC,OAAO,SAAS,CAAC;YAC7B,OAAO;QACT;QACA,OAAO,IAAI,CAAC,OAAO,SAAS,CAAC,iBAAiB;QAC9C,kBAAkB,QAAQ;IAC5B;IACA,OAAO;AACT;;;AJ9IA,0CAA0C;AAC1C,yEAAyE;AACzE,gEAAgE;AAChE,+BAA+B;AAC/B,MAAM,kCAAY;AAEX,MAAM,4CAAQ,CACnB,KACA;IAEA,UAAU,WAAW,CAAC;IAEtB;;GAEC,GACD,IAAI,SAAS;IACb,IAAI,SAAS;IAEb;;GAEC,GACD,SAAS,eAAe,GAAW;QACjC,MAAM,QAAQ,IAAI,KAAK,CAAC;QACxB,IAAI,OACF,UAAU,MAAM,MAAM;QAExB,MAAM,IAAI,IAAI,WAAW,CAAC;QAC1B,SAAS,CAAC,IAAI,IAAI,MAAM,GAAG,IAAI,SAAS,IAAI,MAAM;IACpD;IAEA;;GAEC,GACD,SAAS;QACP,MAAM,QAAQ;YAAC,MAAM;YAAQ,QAAQ;QAAM;QAC3C,OAAO,SACL,IAA0B;YAEzB,KAAY,QAAQ,GAAG,IAAI,CAAA,GAAA,wCAAO,EACjC,OACA;gBAAC,MAAM;gBAAQ,QAAQ;YAAM,GAC7B,SAAS,UAAU;YAErB;YACA,OAAO;QACT;IACF;IAEA;;GAEC,GACD,MAAM,aAAmC,EAAE;IAE3C,SAAS,MAAM,GAAW;QACxB,MAAM,MAAM,IAAI,CAAA,GAAA,wCAAY,EAC1B,SAAS,UAAU,IACnB,KACA,QACA,QACA;QAGF,IAAI,SAAS,QACX,WAAW,IAAI,CAAC;aAEhB,MAAM;IAEV;IAEA;;GAEC,GACD,SAAS;QACP,MAAM,YAAY;QAElB,MAAM,SAA2B;YAC/B,MAAM,CAAA,GAAA,yCAAO,EAAE,UAAU;YACzB,YAAY;gBACV,QAAQ,SAAS;gBACjB,OAAO;gBACP,eAAe;YACjB;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,SAAS;QACP,MAAM,YAAY,QAAQ,IAAI,CAAC;QAC/B,IAAI,WAAW;YACb,aAAa;YACb,OAAO;QACT;QACA,OAAO;IACT;IAEA;;GAEC,GACD,SAAS;QACP,MAAM,aAAa,KAAK,IAAI,CAAC;QAC7B,IAAI,YAAY;YACd,aAAa;YACb,OAAO;QACT;QACA,OAAO;IACT;IAEA;;GAEC,GACD,SAAS;QACP,IAAI;QACJ,MAAM,QAA0C,EAAE;QAClD;QACA,SAAS;QACT,MAAO,IAAI,MAAM,IAAI,IAAI,MAAM,CAAC,OAAO,OAAQ,CAAA,OAAO,YAAY,MAAK,EACrE,IAAI,MAAM;YACR,MAAM,IAAI,CAAC;YACX,SAAS;QACX;QAEF,OAAO;IACT;IAEA;;GAEC,GACD,SAAS,aAAa,CAAkB;QACtC,MAAM,MAAM,CAAC,CAAC,EAAE;QAChB,eAAe;QACf,MAAM,IAAI,KAAK,CAAC,IAAI,MAAM;QAC1B,OAAO;IACT;IAEA;;GAEC,GACD,SAAS;QACP,MAAM,IAAI,OAAO,IAAI,CAAC;QACtB,IAAI,GACF,aAAa;IAEjB;IAEA;;GAEC,GACD,SAAS,SACP,KAAiC;QAEjC,IAAI;QACJ,QAAQ,SAAS,EAAE;QACnB,MAAQ,IAAI,UACV,IAAI,GACF,MAAM,IAAI,CAAC;QAGf,OAAO;IACT;IAEA;;GAEC,GACD,SAAS;QACP,MAAM,MAAM;QACZ,IAAI,QAAQ,IAAI,MAAM,CAAC,MAAM,QAAQ,IAAI,MAAM,CAAC,IAC9C;QAGF,MAAM,IAAI,iBAAiB,IAAI,CAAC;QAChC,IAAI,CAAC,GACH,OAAO,MAAM;QAEf,aAAa;QAEb,OAAO,IAAmB;YACxB,MAAM,CAAA,GAAA,yCAAO,EAAE,OAAO;YACtB,SAAS,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG;QACzB;IACF;IAEA;;GAEC,GACD,SAAS;QACP,MAAM,IAAI,WAAW,IAAI,CAAC;QAC1B,IAAI,CAAC,GACH;QAEF,aAAa;QAEb,8BAA8B;QAC9B,MAAM,MAAM,2BAAK,CAAC,CAAC,EAAE,EAAE,OAAO,CAAC,iCAAW;QAE1C,OAAO,CAAA,GAAA,yCAA8B,EAAE,KAAK;YAAC;SAAI,EAAE,GAAG,CAAC,CAAA,IAAK,2BAAK;IACnE;IAEA;;GAEC,GACD,SAAS;QACP,MAAM,MAAM;QAEZ,OAAO;QACP,MAAM,YAAY,yCAAyC,IAAI,CAAC;QAChE,IAAI,CAAC,WACH;QAEF,aAAa;QACb,MAAM,YAAY,2BAAK,SAAS,CAAC,EAAE;QAEnC,IAAI;QACJ,MAAM,kBAAkB,QAAQ,IAAI,CAAC;QACrC,IAAI,CAAC,iBACH,OAAO,MAAM;QAEf,aAAa;QAEb,MAAM;QACN,IAAI,QAAQ;QACZ,MAAM,mBAAmB,CAAA,GAAA,yCAAqC,EAAE,KAAK;YACnE;YACA;SACD;QACD,IAAI,qBAAqB,IAAI;YAC3B,QAAQ,IAAI,SAAS,CAAC,GAAG;YACzB,MAAM,YAAY;gBAAC;aAAM;YACzB,aAAa;YAEb,QAAQ,2BAAK,OAAO,OAAO,CAAC,iCAAW;QACzC;QAEA,MAAM,MAAM,IAAuB;YACjC,MAAM,CAAA,GAAA,yCAAO,EAAE,WAAW;YAC1B,UAAU,UAAU,OAAO,CAAC,iCAAW;YACvC,OAAO;QACT;QAEA,IAAI;QACJ,MAAM,WAAW,UAAU,IAAI,CAAC;QAChC,IAAI,UACF,aAAa;QAGf,OAAO;IACT;IAEA;;GAEC,GACD,SAAS;QACP,MAAM,QAAkD,EAAE;QAE1D,IAAI,CAAC,QACH,OAAO,MAAM;QAEf,SAAS;QAET,eAAe;QACf,IAAI;QACJ,MAAQ,OAAO,cACb,IAAI,MAAM;YACR,MAAM,IAAI,CAAC;YACX,SAAS;QACX;QAGF,IAAI,CAAC,SACH,OAAO,MAAM;QAEf,OAAO;IACT;IAEA;;GAEC,GACD,SAAS;QACP,IAAI;QACJ,MAAM,OAAO,EAAE;QACf,MAAM,MAAM;QAEZ,MAAQ,IAAI,sCAAsC,IAAI,CAAC,KAAO;YAC5D,MAAM,MAAM,aAAa;YACzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE;YAChB,MAAM,cAAc,QAAQ,IAAI,CAAC;YACjC,IAAI,aACF,aAAa;QAEjB;QAEA,IAAI,CAAC,KAAK,MAAM,EACd;QAGF,OAAO,IAAoB;YACzB,MAAM,CAAA,GAAA,yCAAO,EAAE,QAAQ;YACvB,QAAQ;YACR,cAAc,kBAAkB,EAAE;QACpC;IACF;IAEA;;GAEC,GACD,SAAS;QACP,MAAM,MAAM;QACZ,MAAM,KAAK,0BAA0B,IAAI,CAAC;QAE1C,IAAI,CAAC,IACH;QAEF,MAAM,SAAS,aAAa,GAAG,CAAC,EAAE;QAElC,aAAa;QACb,MAAM,KAAK,eAAe,IAAI,CAAC;QAC/B,IAAI,CAAC,IACH,OAAO,MAAM;QAEf,MAAM,OAAO,aAAa,GAAG,CAAC,EAAE;QAEhC,IAAI,CAAC,QACH,OAAO,MAAM;QAGf,IAAI;QACJ,IAAI,SAAgD;QACpD,MAAQ,QAAQ,WAAa;YAC3B,OAAO,IAAI,CAAC;YACZ,SAAS,OAAO,MAAM,CAAC;QACzB;QAEA,IAAI,CAAC,SACH,OAAO,MAAM;QAGf,OAAO,IAAqB;YAC1B,MAAM,CAAA,GAAA,yCAAO,EAAE,SAAS;YACxB,MAAM;YACN,QAAQ;YACR,WAAW;QACb;IACF;IAEA;;GAEC,GACD,SAAS;QACP,MAAM,MAAM;QACZ,MAAM,IAAI,sBAAsB,IAAI,CAAC;QAErC,IAAI,CAAC,GACH;QAEF,MAAM,WAAW,2BAAK,aAAa,EAAE,CAAC,EAAE;QAExC,IAAI,CAAC,QACH,OAAO,MAAM;QAGf,MAAM,QAAQ,WAAyB,MAAM,CAAC;QAE9C,IAAI,CAAC,SACH,OAAO,MAAM;QAGf,OAAO,IAAoB;YACzB,MAAM,CAAA,GAAA,yCAAO,EAAE,QAAQ;YACvB,UAAU;YACV,OAAO;QACT;IACF;IAEA;;GAEC,GACD,SAAS;QACP,MAAM,MAAM;QACZ,MAAM,IAAI,YAAY,IAAI,CAAC;QAE3B,IAAI,CAAC,GACH;QAEF,aAAa;QAEb,IAAI,CAAC,QACH,OAAO,MAAM;QAGf,MAAM,QAAQ,WAAyB,MAAM,CAAC;QAE9C,IAAI,CAAC,SACH,OAAO,MAAM;QAGf,OAAO,IAAgB;YACrB,MAAM,CAAA,GAAA,yCAAO,EAAE,IAAI;YACnB,OAAO;QACT;IACF;IAEA;;GAEC,GACD,SAAS;QACP,MAAM,MAAM;QACZ,MAAM,IAAI,uBAAuB,IAAI,CAAC;QAEtC,IAAI,CAAC,GACH;QAEF,MAAM,YAAY,2BAAK,aAAa,EAAE,CAAC,EAAE;QAEzC,IAAI,CAAC,QACH,OAAO,MAAM;QAGf,MAAM,QAAQ,WAAyB,MAAM,CAAC;QAE9C,IAAI,CAAC,SACH,OAAO,MAAM;QAGf,OAAO,IAAqB;YAC1B,MAAM,CAAA,GAAA,yCAAO,EAAE,SAAS;YACxB,WAAW;YACX,OAAO;QACT;IACF;IAEA;;GAEC,GACD,SAAS;QACP,MAAM,MAAM;QACZ,MAAM,IAAI,qBAAqB,IAAI,CAAC;QAEpC,IAAI,CAAC,GACH;QAEF,MAAM,QAAQ,2BAAK,aAAa,EAAE,CAAC,EAAE;QAErC,IAAI,CAAC,QAAQ;YACX,MAAM,KAAK,UAAU,IAAI,CAAC;YAC1B,IAAI,IACF,aAAa;YAEf,OAAO,IAAiB;gBACtB,MAAM,CAAA,GAAA,yCAAO,EAAE,KAAK;gBACpB,OAAO;YACT;QACF;QAEA,MAAM,QAAQ,WAAyB,MAAM,CAAC;QAE9C,IAAI,CAAC,SACH,OAAO,MAAM;QAGf,OAAO,IAAiB;YACtB,MAAM,CAAA,GAAA,yCAAO,EAAE,KAAK;YACpB,OAAO;YACP,OAAO;QACT;IACF;IAEA;;GAEC,GACD,SAAS;QACP,MAAM,MAAM;QACZ,MAAM,IAAI,mBAAmB,IAAI,CAAC;QAElC,IAAI,CAAC,GACH;QAEF,MAAM,QAAQ,2BAAK,aAAa,EAAE,CAAC,EAAE;QAErC,IAAI,CAAC,QACH,OAAO,MAAM;QAGf,MAAM,QAAQ,WAAyB,MAAM,CAAC;QAE9C,IAAI,CAAC,SACH,OAAO,MAAM;QAGf,OAAO,IAAiB;YACtB,MAAM,CAAA,GAAA,yCAAO,EAAE,KAAK;YACpB,OAAO;YACP,OAAO;QACT;IACF;IAEA;;GAEC,GACD,SAAS;QACP,MAAM,MAAM;QACZ,MAAM,IAAI,8CAA8C,IAAI,CAAC;QAC7D,IAAI,CAAC,GACH;QAEF,MAAM,MAAM,aAAa;QAEzB,OAAO,IAAuB;YAC5B,MAAM,CAAA,GAAA,yCAAO,EAAE,WAAW;YAC1B,MAAM,2BAAK,GAAG,CAAC,EAAE;YACjB,OAAO,2BAAK,GAAG,CAAC,EAAE;QACpB;IACF;IAEA;;GAEC,GACD,SAAS;QACP,MAAM,MAAM;QACZ,MAAM,IAAI,WAAW,IAAI,CAAC;QAC1B,IAAI,CAAC,GACH;QAEF,aAAa;QAEb,MAAM,MAAM,cAAc,EAAE;QAE5B,IAAI,CAAC,QACH,OAAO,MAAM;QAEf,IAAI,QAAQ;QAEZ,eAAe;QACf,IAAI;QACJ,MAAQ,OAAO,cAAgB;YAC7B,MAAM,IAAI,CAAC;YACX,QAAQ,MAAM,MAAM,CAAC;QACvB;QAEA,IAAI,CAAC,SACH,OAAO,MAAM;QAGf,OAAO,IAAgB;YACrB,MAAM,CAAA,GAAA,yCAAO,EAAE,IAAI;YACnB,WAAW;YACX,cAAc;QAChB;IACF;IAEA;;GAEC,GACD,SAAS;QACP,MAAM,MAAM;QACZ,MAAM,IAAI,+BAA+B,IAAI,CAAC;QAC9C,IAAI,CAAC,GACH;QAEF,MAAM,MAAM,aAAa;QAEzB,MAAM,SAAS,2BAAK,GAAG,CAAC,EAAE;QAC1B,MAAM,MAAM,2BAAK,GAAG,CAAC,EAAE;QAEvB,IAAI,CAAC,QACH,OAAO,MAAM;QAGf,MAAM,QAAQ,WAAyB,MAAM,CAAC;QAE9C,IAAI,CAAC,SACH,OAAO,MAAM;QAGf,OAAO,IAAoB;YACzB,MAAM,CAAA,GAAA,yCAAO,EAAE,QAAQ;YACvB,UAAU;YACV,QAAQ;YACR,OAAO;QACT;IACF;IAEA;;GAEC,GACD,SAAS;QACP,MAAM,MAAM;QACZ,MAAM,IAAI,iBAAiB,IAAI,CAAC;QAChC,IAAI,CAAC,GACH;QAEF,aAAa;QAEb,IAAI,CAAC,QACH,OAAO,MAAM;QAEf,IAAI,QAAQ;QAEZ,eAAe;QACf,IAAI;QACJ,MAAQ,OAAO,cAAgB;YAC7B,MAAM,IAAI,CAAC;YACX,QAAQ,MAAM,MAAM,CAAC;QACvB;QAEA,IAAI,CAAC,SACH,OAAO,MAAM;QAGf,OAAO,IAAoB;YACzB,MAAM,CAAA,GAAA,yCAAO,EAAE,QAAQ;YACvB,cAAc;QAChB;IACF;IAEA;;GAEC,GACD,SAAS;QACP,MAAM,MAAM;QACZ,MAAM,IAAI,sBAAsB,IAAI,CAAC;QACrC,IAAI,CAAC,GACH;QAEF,aAAa;QAEb,IAAI,CAAC,QACH,OAAO,MAAM;QAEf,MAAM,QAAQ,WAAyB,MAAM,CAAC;QAE9C,IAAI,CAAC,SACH,OAAO,MAAM;QAGf,OAAO,IAAyB;YAC9B,MAAM,CAAA,GAAA,yCAAO,EAAE,aAAa;YAC5B,OAAO;QACT;IACF;IAEA;;GAEC,GACD,MAAM,WAAW,eAA6B;IAE9C;;GAEC,GACD,MAAM,YAAY,eAA8B;IAEhD;;GAEC,GACD,MAAM,cAAc,eAAgC;IAEpD;;GAEC,GACD,SAAS,eACP,IAAY;QAEZ,MAAM,KAAK,IAAI,OACb,OACE,OACA;QAGJ,gDAAgD;QAEhD,OAAO;YACL,MAAM,MAAM;YACZ,MAAM,IAAI,GAAG,IAAI,CAAC;YAClB,IAAI,CAAC,GACH;YAEF,MAAM,MAAM,aAAa;YACzB,MAAM,MAA8B;gBAAC,MAAM;YAAI;YAC/C,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,EAAE,CAAC,IAAI;YACvB,OAAO,IAAQ;QACjB;IACF;IAEA;;GAEC,GACD,SAAS;QACP,IAAI,GAAG,CAAC,EAAE,KAAK,KACb;QAGF,OACE,iBACA,aACA,mBACA,gBACA,cACA,eACA,iBACA,gBACA,YACA,YACA,gBACA,iBACA,qBACA;IAEJ;IAEA;;GAEC,GACD,SAAS;QACP,MAAM,MAAM;QACZ,MAAM,MAAM;QAEZ,IAAI,CAAC,KACH,OAAO,MAAM;QAEf;QAEA,OAAO,IAAgB;YACrB,MAAM,CAAA,GAAA,yCAAO,EAAE,IAAI;YACnB,WAAW;YACX,cAAc,kBAAkB,EAAE;QACpC;IACF;IAEA,OAAO,gCAAU;AACnB;AAEA;;CAEC,GACD,SAAS,2BAAK,GAAW;IACvB,OAAO,MAAM,IAAI,IAAI,KAAK;AAC5B;AAEA;;CAEC,GACD,SAAS,gCAAsC,GAAO,EAAE,MAAgB;IACtE,MAAM,SAAS,OAAO,OAAO,IAAI,IAAI,KAAK;IAC1C,MAAM,cAAc,SAAS,MAAM;IAEnC,IAAK,MAAM,KAAK,IAAK;QACnB,MAAM,QAAQ,GAAG,CAAC,EAAE;QACpB,IAAI,MAAM,OAAO,CAAC,QAChB,MAAM,OAAO,CAAC,CAAA;YACZ,gCAAU,GAAG;QACf;aACK,IAAI,SAAS,OAAO,UAAU,UACnC,gCAAU,OAAO;IAErB;IAEA,IAAI,QACF,OAAO,cAAc,CAAC,KAAK,UAAU;QACnC,cAAc;QACd,UAAU;QACV,YAAY;QACZ,OAAO,UAAU;IACnB;IAGF,OAAO;AACT;IAEA,2CAAe;;;;AMnwBf,MAAM;IAKJ,YAAY,OAAyB,CAAE;aAJvC,QAAQ;aACR,cAAc;aACd,WAAW;QAGT,IAAI,OAAO,SAAS,WAAW,UAC7B,IAAI,CAAC,WAAW,GAAG,SAAS;QAE9B,IAAI,SAAS,UACX,IAAI,CAAC,QAAQ,GAAG;IAEpB;IAEA,uGAAuG;IACvG,6DAA6D;IAC7D,KAAK,GAAW,EAAE,SAA4C,EAAE;QAC9D,OAAO;IACT;IAEA;;GAEC,GACD,OAAO,KAAc,EAAE;QACrB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI;QAE3B,IAAI,OAAO;YACT,IAAI,CAAC,KAAK,IAAI;YACd,OAAO;QACT;QAEA,OAAO,MAAM,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW;IAChD;IAEA,MAAM,IAAoB,EAAU;QAClC,OAAQ,KAAK,IAAI;YACf,KAAK,CAAA,GAAA,yCAAO,EAAE,UAAU;gBACtB,OAAO,IAAI,CAAC,UAAU,CAAC;YACzB,KAAK,CAAA,GAAA,yCAAO,EAAE,IAAI;gBAChB,OAAO,IAAI,CAAC,IAAI,CAAC;YACnB,KAAK,CAAA,GAAA,yCAAO,EAAE,WAAW;gBACvB,OAAO,IAAI,CAAC,WAAW,CAAC;YAC1B,KAAK,CAAA,GAAA,yCAAO,EAAE,OAAO;gBACnB,OAAO,IAAI,CAAC,OAAO,CAAC;YACtB,KAAK,CAAA,GAAA,yCAAO,EAAE,SAAS;gBACrB,OAAO,IAAI,CAAC,SAAS,CAAC;YACxB,KAAK,CAAA,GAAA,yCAAO,EAAE,OAAO;gBACnB,OAAO,IAAI,CAAC,OAAO,CAAC;YACtB,KAAK,CAAA,GAAA,yCAAO,EAAE,QAAQ;gBACpB,OAAO,IAAI,CAAC,QAAQ,CAAC;YACvB,KAAK,CAAA,GAAA,yCAAO,EAAE,WAAW;gBACvB,OAAO,IAAI,CAAC,WAAW,CAAC;YAC1B,KAAK,CAAA,GAAA,yCAAO,EAAE,QAAQ;gBACpB,OAAO,IAAI,CAAC,QAAQ,CAAC;YACvB,KAAK,CAAA,GAAA,yCAAO,EAAE,IAAI;gBAChB,OAAO,IAAI,CAAC,IAAI,CAAC;YACnB,KAAK,CAAA,GAAA,yCAAO,EAAE,MAAM;gBAClB,OAAO,IAAI,CAAC,MAAM,CAAC;YACrB,KAAK,CAAA,GAAA,yCAAO,EAAE,SAAS;gBACrB,OAAO,IAAI,CAAC,SAAS,CAAC;YACxB,KAAK,CAAA,GAAA,yCAAO,EAAE,QAAQ;gBACpB,OAAO,IAAI,CAAC,QAAQ,CAAC;YACvB,KAAK,CAAA,GAAA,yCAAO,EAAE,KAAK;gBACjB,OAAO,IAAI,CAAC,KAAK,CAAC;YACpB,KAAK,CAAA,GAAA,yCAAO,EAAE,KAAK;gBACjB,OAAO,IAAI,CAAC,KAAK,CAAC;YACpB,KAAK,CAAA,GAAA,yCAAO,EAAE,SAAS;gBACrB,OAAO,IAAI,CAAC,SAAS,CAAC;YACxB,KAAK,CAAA,GAAA,yCAAO,EAAE,IAAI;gBAChB,OAAO,IAAI,CAAC,IAAI,CAAC;YACnB,KAAK,CAAA,GAAA,yCAAO,EAAE,aAAa;gBACzB,OAAO,IAAI,CAAC,aAAa,CAAC;YAC5B,KAAK,CAAA,GAAA,yCAAO,EAAE,QAAQ;gBACpB,OAAO,IAAI,CAAC,QAAQ,CAAC;QACzB;IACF;IAEA,SAAS,KAA4B,EAAE,KAAc,EAAE;QACrD,IAAI,MAAM;QACV,QAAQ,SAAS;QAEjB,IAAK,IAAI,IAAI,GAAG,SAAS,MAAM,MAAM,EAAE,IAAI,QAAQ,IAAK;YACtD,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;YAC1B,IAAI,SAAS,IAAI,SAAS,GACxB,OAAO,IAAI,CAAC,IAAI,CAAC;QAErB;QAEA,OAAO;IACT;IAEA,QAAQ,IAAsB,EAAE;QAC9B,IAAI,IAAI,CAAC,QAAQ,EACf,OAAO,KAAK,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;QAG1D,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB;IAEA;;GAEC,GACD,WAAW,IAAsB,EAAE;QACjC,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,UAAU,CAAC,KAAK,EAAE;IAC9C;IAEA;;GAEC,GACD,QAAQ,IAAmB,EAAE;QAC3B,IAAI,IAAI,CAAC,QAAQ,EACf,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,QAAQ;QAEpC,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,OAAO,KAAK,OAAO,GAAG,MAAM,KAAK,QAAQ;IAC5E;IAEA;;GAEC,GACD,UAAU,IAAqB,EAAE;QAC/B,IAAI,IAAI,CAAC,QAAQ,EACf,OACE,IAAI,CAAC,IAAI,CAAC,gBAAgB,KAAK,SAAS,EAAE,KAAK,QAAQ,IACvD,IAAI,CAAC,IAAI,CAAC,OACV,IAAI,CAAC,QAAQ,CAAC,KAAK,KAAK,IACxB,IAAI,CAAC,IAAI,CAAC;QAGd,OACE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,gBAAgB,KAAK,SAAS,EAAE,KAAK,QAAQ,IACvE,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,MAAM,CAAC,MAC/B,IAAI,CAAC,QAAQ,CAAC,KAAK,KAAK,EAAE,UAC1B,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,MAAM,KAAK;IAEvD;IAEA;;GAEC,GACD,MAAM,IAAiB,EAAE;QACvB,IAAI,IAAI,CAAC,QAAQ,EACf,OACE,IAAI,CAAC,IAAI,CAAC,YAAY,KAAK,KAAK,EAAE,KAAK,QAAQ,IAC9C,CAAA,KAAK,KAAK,GACP,IAAI,CAAC,IAAI,CAAC,OACV,IAAI,CAAC,QAAQ,CAAmB,KAAK,KAAK,IAC1C,IAAI,CAAC,IAAI,CAAC,OACV,GAAE;QAGV,OACE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,YAAY,KAAK,KAAK,EAAE,KAAK,QAAQ,IAC9D,CAAA,KAAK,KAAK,GACP,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,MAAM,CAAC,MAC/B,IAAI,CAAC,QAAQ,CAAmB,KAAK,KAAK,EAAE,UAC5C,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,MAAM,KAAK,OACnD,GAAE;IAEV;IAEA;;GAEC,GACD,OAAO,IAAkB,EAAE;QACzB,OAAO,IAAI,CAAC,IAAI,CAAC,aAAa,KAAK,MAAM,GAAG,KAAK,KAAK,QAAQ;IAChE;IAEA;;GAEC,GACD,MAAM,IAAiB,EAAE;QACvB,IAAI,IAAI,CAAC,QAAQ,EACf,OACE,IAAI,CAAC,IAAI,CAAC,YAAY,KAAK,KAAK,EAAE,KAAK,QAAQ,IAC/C,IAAI,CAAC,IAAI,CAAC,OACV,IAAI,CAAC,QAAQ,CAAC,KAAK,KAAK,IACxB,IAAI,CAAC,IAAI,CAAC;QAGd,OACE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,YAAY,KAAK,KAAK,EAAE,KAAK,QAAQ,IAC/D,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,MAAM,CAAC,MAC/B,IAAI,CAAC,QAAQ,CAAC,KAAK,KAAK,EAAE,UAC1B,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,MAAM,KAAK;IAEvD;IAEA;;GAEC,GACD,SAAS,IAAoB,EAAE;QAC7B,MAAM,MAAM,MAAO,CAAA,KAAK,MAAM,IAAI,EAAC,IAAK,cAAc,KAAK,QAAQ;QACnE,IAAI,IAAI,CAAC,QAAQ,EACf,OACE,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,QAAQ,IAC5B,IAAI,CAAC,IAAI,CAAC,OACV,IAAI,CAAC,QAAQ,CAAC,KAAK,KAAK,IACxB,IAAI,CAAC,IAAI,CAAC;QAGd,OACE,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,QAAQ,IAC5B,IAAI,CAAC,IAAI,CAAC,UAAe,IAAI,CAAC,MAAM,CAAC,MACrC,IAAI,CAAC,QAAQ,CAAC,KAAK,KAAK,EAAE,UAC1B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM;IAEhC;IAEA;;GAEC,GACD,QAAQ,IAAmB,EAAE;QAC3B,OAAO,IAAI,CAAC,IAAI,CAAC,cAAc,KAAK,OAAO,GAAG,KAAK,KAAK,QAAQ;IAClE;IAEA;;GAEC,GACD,UAAU,IAAqB,EAAE;QAC/B,OAAO,IAAI,CAAC,IAAI,CAAC,gBAAgB,KAAK,SAAS,GAAG,KAAK,KAAK,QAAQ;IACtE;IAEA;;GAEC,GACD,cAAc,IAAyB,EAAE;QACvC,IAAI,IAAI,CAAC,QAAQ,EACf,OACE,IAAI,CAAC,IAAI,CAAC,mBAAmB,KAAK,QAAQ,IAC1C,IAAI,CAAC,IAAI,CAAC,OACV,IAAI,CAAC,QAAQ,CAAC,KAAK,KAAK,IACxB,IAAI,CAAC,IAAI,CAAC;QAGd,OACE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,mBAAmB,KAAK,QAAQ,IAC1D,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,MAAM,CAAC,MAC/B,IAAI,CAAC,QAAQ,CAAC,KAAK,KAAK,EAAE,UAC1B,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,MAAM,KAAK;IAEvD;IAEA;;GAEC,GACD,SAAS,IAAoB,EAAE;QAC7B,IAAI,IAAI,CAAC,QAAQ,EACf,OACE,IAAI,CAAC,IAAI,CAAC,eAAe,KAAK,QAAQ,EAAE,KAAK,QAAQ,IACrD,IAAI,CAAC,IAAI,CAAC,OACV,IAAI,CAAC,QAAQ,CAAC,KAAK,KAAK,IACxB,IAAI,CAAC,IAAI,CAAC;QAGd,OACE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,eAAe,KAAK,QAAQ,EAAE,KAAK,QAAQ,IACrE,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,MAAM,CAAC,MAC/B,IAAI,CAAC,QAAQ,CAAC,KAAK,KAAK,EAAE,UAC1B,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,MAAM,KAAK;IAEvD;IAEA;;GAEC,GACD,UAAU,IAAqB,EAAE;QAC/B,IAAI,IAAI,CAAC,QAAQ,EACf,OACE,IAAI,CAAC,IAAI,CACP,MAAO,CAAA,KAAK,MAAM,IAAI,EAAC,IAAK,eAAe,KAAK,IAAI,EACpD,KAAK,QAAQ,IAEf,IAAI,CAAC,IAAI,CAAC,OACV,IAAI,CAAC,QAAQ,CAAC,KAAK,SAAS,IAC5B,IAAI,CAAC,IAAI,CAAC;QAGd,OACE,IAAI,CAAC,IAAI,CACP,MAAO,CAAA,KAAK,MAAM,IAAI,EAAC,IAAK,eAAe,KAAK,IAAI,EACpD,KAAK,QAAQ,IAEf,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,MAAM,CAAC,MAC/B,IAAI,CAAC,QAAQ,CAAC,KAAK,SAAS,EAAE,QAC9B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM;IAEhC;IAEA;;GAEC,GACD,SAAS,IAAoB,EAAE;QAC7B,MAAM,QAAQ,KAAK,YAAY;QAC/B,IAAI,IAAI,CAAC,QAAQ,EACf,OACE,IAAI,CAAC,IAAI,CAAC,KAAK,MAAM,CAAC,IAAI,CAAC,MAAM,KAAK,QAAQ,IAC9C,IAAI,CAAC,IAAI,CAAC,OACV,IAAI,CAAC,QAAQ,CAAC,SACd,IAAI,CAAC,IAAI,CAAC;QAId,OACE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,MACrB,IAAI,CAAC,IAAI,CAAC,KAAK,MAAM,CAAC,IAAI,CAAC,OAAO,KAAK,QAAQ,IAC/C,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,MAAM,CAAC,MAC/B,IAAI,CAAC,QAAQ,CAAC,OAAO,QACrB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,OAAO,IAAI,CAAC,MAAM,KAAK;IAEvD;IAEA;;GAEC,GACD,KAAK,IAAgB,EAAE;QACrB,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,MAAM,MAAM,KAAK,SAAS,CAAC,MAAM,GAAG,KAAK,SAAS,CAAC,IAAI,CAAC,QAAQ;YAEhE,OACE,IAAI,CAAC,IAAI,CAAC,WAAW,KAAK,KAAK,QAAQ,IACvC,IAAI,CAAC,IAAI,CAAC,OACV,IAAI,CAAC,QAAQ,CAAC,KAAK,YAAY,IAC/B,IAAI,CAAC,IAAI,CAAC;QAEd;QACA,MAAM,MAAM,KAAK,SAAS,CAAC,MAAM,GAAG,KAAK,SAAS,CAAC,IAAI,CAAC,QAAQ,MAAM;QAEtE,OACE,IAAI,CAAC,IAAI,CAAC,WAAW,KAAK,KAAK,QAAQ,IACvC,IAAI,CAAC,IAAI,CAAC,SACV,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MACtB,IAAI,CAAC,QAAQ,CAAC,KAAK,YAAY,EAAE,QACjC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OACtB,IAAI,CAAC,IAAI,CAAC;IAEd;IAEA;;GAEC,GACD,SAAS,IAAoB,EAAE;QAC7B,IAAI,IAAI,CAAC,QAAQ,EACf,OACE,IAAI,CAAC,IAAI,CAAC,cAAc,KAAK,QAAQ,IACrC,IAAI,CAAC,IAAI,CAAC,OACV,IAAI,CAAC,QAAQ,CAAC,KAAK,YAAY,IAC/B,IAAI,CAAC,IAAI,CAAC;QAGd,OACE,IAAI,CAAC,IAAI,CAAC,eAAe,KAAK,QAAQ,IACtC,IAAI,CAAC,IAAI,CAAC,SACV,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MACtB,IAAI,CAAC,QAAQ,CAAC,KAAK,YAAY,EAAE,QACjC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OACtB,IAAI,CAAC,IAAI,CAAC;IAEd;IAEA;;GAEC,GACD,KAAK,IAAgB,EAAE;QACrB,IAAI,IAAI,CAAC,QAAQ,EACf,OACE,IAAI,CAAC,IAAI,CAAC,SAAS,KAAK,QAAQ,IAChC,IAAI,CAAC,IAAI,CAAC,OACV,IAAI,CAAC,QAAQ,CAAC,KAAK,KAAK,IACxB,IAAI,CAAC,IAAI,CAAC;QAGd,OACE,IAAI,CAAC,IAAI,CAAC,SAAS,KAAK,QAAQ,IAChC,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,MAAM,CAAC,MAC/B,IAAI,CAAC,QAAQ,CAAC,KAAK,KAAK,EAAE,UAC1B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM;IAEhC;IAEA;;GAEC,GACD,YAAY,IAAuB,EAAE;QACnC,OAAO,IAAI,CAAC,IAAI,CACd,mBAAmB,KAAK,IAAI,GAAG,MAAM,KAAK,KAAK,GAAG,KAClD,KAAK,QAAQ;IAEjB;IAEA;;GAEC,GACD,KAAK,IAAgB,EAAE;QACrB,MAAM,QAAQ,KAAK,YAAY;QAC/B,IAAI,CAAC,MAAM,MAAM,EACf,OAAO;QAGT,IAAI,IAAI,CAAC,QAAQ,EACf,OACE,IAAI,CAAC,IAAI,CAAC,KAAK,SAAS,CAAC,IAAI,CAAC,MAAM,KAAK,QAAQ,IACjD,IAAI,CAAC,IAAI,CAAC,OACV,IAAI,CAAC,QAAQ,CAAC,SACd,IAAI,CAAC,IAAI,CAAC;QAGd,MAAM,SAAS,IAAI,CAAC,MAAM;QAE1B,OACE,IAAI,CAAC,IAAI,CACP,KAAK,SAAS,CACX,GAAG,CAAC,CAAA;YACH,OAAO,SAAS;QAClB,GACC,IAAI,CAAC,QACR,KAAK,QAAQ,IAEf,IAAI,CAAC,IAAI,CAAC,UACV,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MACtB,IAAI,CAAC,QAAQ,CAAC,OAAO,QACrB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OACtB,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,MAAM,KAAK;IAErC;IAEA;;GAEC,GACD,YAAY,IAAuB,EAAE;QACnC,IAAI,IAAI,CAAC,QAAQ,EACf,OACE,IAAI,CAAC,IAAI,CAAC,KAAK,QAAQ,GAAG,MAAM,KAAK,KAAK,EAAE,KAAK,QAAQ,IACzD,IAAI,CAAC,IAAI,CAAC;QAGd,IAAI,KAAK,QAAQ,KAAK,uBACpB,OACE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,MACrB,IAAI,CAAC,IAAI,CACP,KAAK,QAAQ,GACX,OACA,KAAK,KAAK,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC,KAAK,MAAM,CAAC,MAAM,IAAI,CAAC,MAAM,KAC3D,KAAK,QAAQ,IAEf,IAAI,CAAC,IAAI,CAAC;QAEd,OACE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,MACrB,IAAI,CAAC,IAAI,CAAC,KAAK,QAAQ,GAAG,OAAO,KAAK,KAAK,EAAE,KAAK,QAAQ,IAC1D,IAAI,CAAC,IAAI,CAAC;IAEd;AACF;IAEA,2CAAe;;;IDjef,2CAAe,CAAC,MAAwB;IACtC,MAAM,WAAW,IAAI,CAAA,GAAA,wCAAO,EAAE,WAAW,CAAC;IAC1C,OAAO,SAAS,OAAO,CAAC;AAC1B;;;;;;ANJO,MAAM,4CAAQ,CAAA,GAAA,wCAAM;AACpB,MAAM,4CAAY,CAAA,GAAA,wCAAU;IAInC,2CAAe;WAAC;eAAO;AAAS", "sources": ["src/index.ts", "src/parse/index.ts", "src/CssParseError.ts", "src/CssPosition.ts", "src/type.ts", "src/utils/stringSearch.ts", "src/stringify/index.ts", "src/stringify/compiler.ts"], "sourcesContent": ["import {default as parseFn} from './parse';\nimport {default as stringifyFn} from './stringify';\nexport const parse = parseFn;\nexport const stringify = stringifyFn;\nexport * from './type';\nexport * from './CssParseError';\nexport * from './CssPosition';\nexport default {parse, stringify};\n", "import CssParseError from '../CssParseError';\nimport Position from '../CssPosition';\nimport {\n  CssAtRuleAST,\n  CssCharsetAST,\n  CssCommentAST,\n  CssCommonPositionAST,\n  CssContainerAST,\n  CssCustomMediaAST,\n  CssDeclarationAST,\n  CssDocumentAST,\n  CssFontFaceAST,\n  CssHostAST,\n  CssImportAST,\n  CssKeyframeAST,\n  CssKeyframesAST,\n  CssLayerAST,\n  CssMediaAST,\n  CssNamespaceAST,\n  CssPageAST,\n  CssRuleAST,\n  CssStartingStyleAST,\n  CssStylesheetAST,\n  CssSupportsAST,\n  CssTypes,\n} from '../type';\nimport {\n  indexOfArrayWithBracketAndQuoteSupport,\n  splitWithBracketAndQuoteSupport,\n} from '../utils/stringSearch';\n\n// http://www.w3.org/TR/CSS21/grammar.html\n// https://github.com/visionmedia/css-parse/pull/49#issuecomment-30088027\n// New rule => https://www.w3.org/TR/CSS22/syndata.html#comments\n// [^] is equivalent to [.\\n\\r]\nconst commentre = /\\/\\*[^]*?(?:\\*\\/|$)/g;\n\nexport const parse = (\n  css: string,\n  options?: {source?: string; silent?: boolean},\n): CssStylesheetAST => {\n  options = options || {};\n\n  /**\n   * Positional.\n   */\n  let lineno = 1;\n  let column = 1;\n\n  /**\n   * Update lineno and column based on `str`.\n   */\n  function updatePosition(str: string) {\n    const lines = str.match(/\\n/g);\n    if (lines) {\n      lineno += lines.length;\n    }\n    const i = str.lastIndexOf('\\n');\n    column = ~i ? str.length - i : column + str.length;\n  }\n\n  /**\n   * Mark position and patch `node.position`.\n   */\n  function position() {\n    const start = {line: lineno, column: column};\n    return function <T1 extends CssCommonPositionAST>(\n      node: Omit<T1, 'position'>,\n    ): T1 {\n      (node as T1).position = new Position(\n        start,\n        {line: lineno, column: column},\n        options?.source || '',\n      );\n      whitespace();\n      return node as T1;\n    };\n  }\n\n  /**\n   * Error `msg`.\n   */\n  const errorsList: Array<CssParseError> = [];\n\n  function error(msg: string) {\n    const err = new CssParseError(\n      options?.source || '',\n      msg,\n      lineno,\n      column,\n      css,\n    );\n\n    if (options?.silent) {\n      errorsList.push(err);\n    } else {\n      throw err;\n    }\n  }\n\n  /**\n   * Parse stylesheet.\n   */\n  function stylesheet(): CssStylesheetAST {\n    const rulesList = rules();\n\n    const result: CssStylesheetAST = {\n      type: CssTypes.stylesheet,\n      stylesheet: {\n        source: options?.source,\n        rules: rulesList,\n        parsingErrors: errorsList,\n      },\n    };\n\n    return result;\n  }\n\n  /**\n   * Opening brace.\n   */\n  function open(): boolean {\n    const openMatch = /^{\\s*/.exec(css);\n    if (openMatch) {\n      processMatch(openMatch);\n      return true;\n    }\n    return false;\n  }\n\n  /**\n   * Closing brace.\n   */\n  function close() {\n    const closeMatch = /^}/.exec(css);\n    if (closeMatch) {\n      processMatch(closeMatch);\n      return true;\n    }\n    return false;\n  }\n\n  /**\n   * Parse ruleset.\n   */\n  function rules() {\n    let node: CssRuleAST | CssAtRuleAST | void;\n    const rules: Array<CssRuleAST | CssAtRuleAST> = [];\n    whitespace();\n    comments(rules);\n    while (css.length && css.charAt(0) !== '}' && (node = atrule() || rule())) {\n      if (node) {\n        rules.push(node);\n        comments(rules);\n      }\n    }\n    return rules;\n  }\n\n  /**\n   * Update position and css string. Return the matches\n   */\n  function processMatch(m: RegExpExecArray) {\n    const str = m[0];\n    updatePosition(str);\n    css = css.slice(str.length);\n    return m;\n  }\n\n  /**\n   * Parse whitespace.\n   */\n  function whitespace() {\n    const m = /^\\s*/.exec(css);\n    if (m) {\n      processMatch(m);\n    }\n  }\n\n  /**\n   * Parse comments;\n   */\n  function comments<T1 extends CssCommonPositionAST>(\n    rules?: Array<T1 | CssCommentAST>,\n  ) {\n    let c;\n    rules = rules || [];\n    while ((c = comment())) {\n      if (c) {\n        rules.push(c);\n      }\n    }\n    return rules;\n  }\n\n  /**\n   * Parse comment.\n   */\n  function comment(): CssCommentAST | void {\n    const pos = position();\n    if ('/' !== css.charAt(0) || '*' !== css.charAt(1)) {\n      return;\n    }\n\n    const m = /^\\/\\*[^]*?\\*\\//.exec(css);\n    if (!m) {\n      return error('End of comment missing');\n    }\n    processMatch(m);\n\n    return pos<CssCommentAST>({\n      type: CssTypes.comment,\n      comment: m[0].slice(2, -2),\n    });\n  }\n\n  /**\n   * Parse selector.\n   */\n  function selector() {\n    const m = /^([^{]+)/.exec(css);\n    if (!m) {\n      return;\n    }\n    processMatch(m);\n\n    // remove comment in selector;\n    const res = trim(m[0]).replace(commentre, '');\n\n    return splitWithBracketAndQuoteSupport(res, [',']).map(v => trim(v));\n  }\n\n  /**\n   * Parse declaration.\n   */\n  function declaration(): CssDeclarationAST | void {\n    const pos = position();\n\n    // prop\n    const propMatch = /^(\\*?[-#/*\\\\\\w]+(\\[[0-9a-z_-]+\\])?)\\s*/.exec(css);\n    if (!propMatch) {\n      return;\n    }\n    processMatch(propMatch);\n    const propValue = trim(propMatch[0]);\n\n    // :\n    const sepratotorMatch = /^:\\s*/.exec(css);\n    if (!sepratotorMatch) {\n      return error(\"property missing ':'\");\n    }\n    processMatch(sepratotorMatch);\n\n    // val\n    let value = '';\n    const endValuePosition = indexOfArrayWithBracketAndQuoteSupport(css, [\n      ';',\n      '}',\n    ]);\n    if (endValuePosition !== -1) {\n      value = css.substring(0, endValuePosition);\n      const fakeMatch = [value] as unknown as RegExpExecArray;\n      processMatch(fakeMatch);\n\n      value = trim(value).replace(commentre, '');\n    }\n\n    const ret = pos<CssDeclarationAST>({\n      type: CssTypes.declaration,\n      property: propValue.replace(commentre, ''),\n      value: value,\n    });\n\n    // ;\n    const endMatch = /^[;\\s]*/.exec(css);\n    if (endMatch) {\n      processMatch(endMatch);\n    }\n\n    return ret;\n  }\n\n  /**\n   * Parse declarations.\n   */\n  function declarations() {\n    const decls: Array<CssDeclarationAST | CssCommentAST> = [];\n\n    if (!open()) {\n      return error(\"missing '{'\");\n    }\n    comments(decls);\n\n    // declarations\n    let decl;\n    while ((decl = declaration())) {\n      if (decl) {\n        decls.push(decl);\n        comments(decls);\n      }\n    }\n\n    if (!close()) {\n      return error(\"missing '}'\");\n    }\n    return decls;\n  }\n\n  /**\n   * Parse keyframe.\n   */\n  function keyframe() {\n    let m;\n    const vals = [];\n    const pos = position();\n\n    while ((m = /^((\\d+\\.\\d+|\\.\\d+|\\d+)%?|[a-z]+)\\s*/.exec(css))) {\n      const res = processMatch(m);\n      vals.push(res[1]);\n      const spacesMatch = /^,\\s*/.exec(css);\n      if (spacesMatch) {\n        processMatch(spacesMatch);\n      }\n    }\n\n    if (!vals.length) {\n      return;\n    }\n\n    return pos<CssKeyframeAST>({\n      type: CssTypes.keyframe,\n      values: vals,\n      declarations: declarations() || [],\n    });\n  }\n\n  /**\n   * Parse keyframes.\n   */\n  function atkeyframes(): CssKeyframesAST | void {\n    const pos = position();\n    const m1 = /^@([-\\w]+)?keyframes\\s*/.exec(css);\n\n    if (!m1) {\n      return;\n    }\n    const vendor = processMatch(m1)[1];\n\n    // identifier\n    const m2 = /^([-\\w]+)\\s*/.exec(css);\n    if (!m2) {\n      return error('@keyframes missing name');\n    }\n    const name = processMatch(m2)[1];\n\n    if (!open()) {\n      return error(\"@keyframes missing '{'\");\n    }\n\n    let frame;\n    let frames: Array<CssKeyframeAST | CssCommentAST> = comments();\n    while ((frame = keyframe())) {\n      frames.push(frame);\n      frames = frames.concat(comments());\n    }\n\n    if (!close()) {\n      return error(\"@keyframes missing '}'\");\n    }\n\n    return pos<CssKeyframesAST>({\n      type: CssTypes.keyframes,\n      name: name,\n      vendor: vendor,\n      keyframes: frames,\n    });\n  }\n\n  /**\n   * Parse supports.\n   */\n  function atsupports(): CssSupportsAST | void {\n    const pos = position();\n    const m = /^@supports *([^{]+)/.exec(css);\n\n    if (!m) {\n      return;\n    }\n    const supports = trim(processMatch(m)[1]);\n\n    if (!open()) {\n      return error(\"@supports missing '{'\");\n    }\n\n    const style = comments<CssAtRuleAST>().concat(rules());\n\n    if (!close()) {\n      return error(\"@supports missing '}'\");\n    }\n\n    return pos<CssSupportsAST>({\n      type: CssTypes.supports,\n      supports: supports,\n      rules: style,\n    });\n  }\n\n  /**\n   * Parse host.\n   */\n  function athost() {\n    const pos = position();\n    const m = /^@host\\s*/.exec(css);\n\n    if (!m) {\n      return;\n    }\n    processMatch(m);\n\n    if (!open()) {\n      return error(\"@host missing '{'\");\n    }\n\n    const style = comments<CssAtRuleAST>().concat(rules());\n\n    if (!close()) {\n      return error(\"@host missing '}'\");\n    }\n\n    return pos<CssHostAST>({\n      type: CssTypes.host,\n      rules: style,\n    });\n  }\n\n  /**\n   * Parse container.\n   */\n  function atcontainer(): CssContainerAST | void {\n    const pos = position();\n    const m = /^@container *([^{]+)/.exec(css);\n\n    if (!m) {\n      return;\n    }\n    const container = trim(processMatch(m)[1]);\n\n    if (!open()) {\n      return error(\"@container missing '{'\");\n    }\n\n    const style = comments<CssAtRuleAST>().concat(rules());\n\n    if (!close()) {\n      return error(\"@container missing '}'\");\n    }\n\n    return pos<CssContainerAST>({\n      type: CssTypes.container,\n      container: container,\n      rules: style,\n    });\n  }\n\n  /**\n   * Parse container.\n   */\n  function atlayer(): CssLayerAST | void {\n    const pos = position();\n    const m = /^@layer *([^{;@]+)/.exec(css);\n\n    if (!m) {\n      return;\n    }\n    const layer = trim(processMatch(m)[1]);\n\n    if (!open()) {\n      const m2 = /^[;\\s]*/.exec(css);\n      if (m2) {\n        processMatch(m2);\n      }\n      return pos<CssLayerAST>({\n        type: CssTypes.layer,\n        layer: layer,\n      });\n    }\n\n    const style = comments<CssAtRuleAST>().concat(rules());\n\n    if (!close()) {\n      return error(\"@layer missing '}'\");\n    }\n\n    return pos<CssLayerAST>({\n      type: CssTypes.layer,\n      layer: layer,\n      rules: style,\n    });\n  }\n\n  /**\n   * Parse media.\n   */\n  function atmedia(): CssMediaAST | void {\n    const pos = position();\n    const m = /^@media *([^{]+)/.exec(css);\n\n    if (!m) {\n      return;\n    }\n    const media = trim(processMatch(m)[1]);\n\n    if (!open()) {\n      return error(\"@media missing '{'\");\n    }\n\n    const style = comments<CssAtRuleAST>().concat(rules());\n\n    if (!close()) {\n      return error(\"@media missing '}'\");\n    }\n\n    return pos<CssMediaAST>({\n      type: CssTypes.media,\n      media: media,\n      rules: style,\n    });\n  }\n\n  /**\n   * Parse custom-media.\n   */\n  function atcustommedia(): CssCustomMediaAST | void {\n    const pos = position();\n    const m = /^@custom-media\\s+(--\\S+)\\s+([^{;\\s][^{;]*);/.exec(css);\n    if (!m) {\n      return;\n    }\n    const res = processMatch(m);\n\n    return pos<CssCustomMediaAST>({\n      type: CssTypes.customMedia,\n      name: trim(res[1]),\n      media: trim(res[2]),\n    });\n  }\n\n  /**\n   * Parse paged media.\n   */\n  function atpage(): CssPageAST | void {\n    const pos = position();\n    const m = /^@page */.exec(css);\n    if (!m) {\n      return;\n    }\n    processMatch(m);\n\n    const sel = selector() || [];\n\n    if (!open()) {\n      return error(\"@page missing '{'\");\n    }\n    let decls = comments<CssDeclarationAST>();\n\n    // declarations\n    let decl;\n    while ((decl = declaration())) {\n      decls.push(decl);\n      decls = decls.concat(comments());\n    }\n\n    if (!close()) {\n      return error(\"@page missing '}'\");\n    }\n\n    return pos<CssPageAST>({\n      type: CssTypes.page,\n      selectors: sel,\n      declarations: decls,\n    });\n  }\n\n  /**\n   * Parse document.\n   */\n  function atdocument(): CssDocumentAST | void {\n    const pos = position();\n    const m = /^@([-\\w]+)?document *([^{]+)/.exec(css);\n    if (!m) {\n      return;\n    }\n    const res = processMatch(m);\n\n    const vendor = trim(res[1]);\n    const doc = trim(res[2]);\n\n    if (!open()) {\n      return error(\"@document missing '{'\");\n    }\n\n    const style = comments<CssAtRuleAST>().concat(rules());\n\n    if (!close()) {\n      return error(\"@document missing '}'\");\n    }\n\n    return pos<CssDocumentAST>({\n      type: CssTypes.document,\n      document: doc,\n      vendor: vendor,\n      rules: style,\n    });\n  }\n\n  /**\n   * Parse font-face.\n   */\n  function atfontface(): CssFontFaceAST | void {\n    const pos = position();\n    const m = /^@font-face\\s*/.exec(css);\n    if (!m) {\n      return;\n    }\n    processMatch(m);\n\n    if (!open()) {\n      return error(\"@font-face missing '{'\");\n    }\n    let decls = comments<CssDeclarationAST>();\n\n    // declarations\n    let decl;\n    while ((decl = declaration())) {\n      decls.push(decl);\n      decls = decls.concat(comments());\n    }\n\n    if (!close()) {\n      return error(\"@font-face missing '}'\");\n    }\n\n    return pos<CssFontFaceAST>({\n      type: CssTypes.fontFace,\n      declarations: decls,\n    });\n  }\n\n  /**\n   * Parse starting style.\n   */\n  function atstartingstyle(): CssStartingStyleAST | void {\n    const pos = position();\n    const m = /^@starting-style\\s*/.exec(css);\n    if (!m) {\n      return;\n    }\n    processMatch(m);\n\n    if (!open()) {\n      return error(\"@starting-style missing '{'\");\n    }\n    const style = comments<CssAtRuleAST>().concat(rules());\n\n    if (!close()) {\n      return error(\"@starting-style missing '}'\");\n    }\n\n    return pos<CssStartingStyleAST>({\n      type: CssTypes.startingStyle,\n      rules: style,\n    });\n  }\n\n  /**\n   * Parse import\n   */\n  const atimport = _compileAtrule<CssImportAST>('import');\n\n  /**\n   * Parse charset\n   */\n  const atcharset = _compileAtrule<CssCharsetAST>('charset');\n\n  /**\n   * Parse namespace\n   */\n  const atnamespace = _compileAtrule<CssNamespaceAST>('namespace');\n\n  /**\n   * Parse non-block at-rules\n   */\n  function _compileAtrule<T1 extends CssCommonPositionAST>(\n    name: string,\n  ): () => T1 | void {\n    const re = new RegExp(\n      '^@' +\n        name +\n        '\\\\s*((?::?[^;\\'\"]|\"(?:\\\\\\\\\"|[^\"])*?\"|\\'(?:\\\\\\\\\\'|[^\\'])*?\\')+)(?:;|$)',\n    );\n\n    // ^@import\\s*([^;\"']|(\"|')(?:\\\\\\2|.)*?\\2)+(;|$)\n\n    return function (): T1 | void {\n      const pos = position();\n      const m = re.exec(css);\n      if (!m) {\n        return;\n      }\n      const res = processMatch(m);\n      const ret: Record<string, string> = {type: name};\n      ret[name] = res[1].trim();\n      return pos<T1>(ret as unknown as T1) as T1;\n    };\n  }\n\n  /**\n   * Parse at rule.\n   */\n  function atrule(): CssAtRuleAST | void {\n    if (css[0] !== '@') {\n      return;\n    }\n\n    return (\n      atkeyframes() ||\n      atmedia() ||\n      atcustommedia() ||\n      atsupports() ||\n      atimport() ||\n      atcharset() ||\n      atnamespace() ||\n      atdocument() ||\n      atpage() ||\n      athost() ||\n      atfontface() ||\n      atcontainer() ||\n      atstartingstyle() ||\n      atlayer()\n    );\n  }\n\n  /**\n   * Parse rule.\n   */\n  function rule() {\n    const pos = position();\n    const sel = selector();\n\n    if (!sel) {\n      return error('selector missing');\n    }\n    comments();\n\n    return pos<CssRuleAST>({\n      type: CssTypes.rule,\n      selectors: sel,\n      declarations: declarations() || [],\n    });\n  }\n\n  return addParent(stylesheet());\n};\n\n/**\n * Trim `str`.\n */\nfunction trim(str: string) {\n  return str ? str.trim() : '';\n}\n\n/**\n * Adds non-enumerable parent node reference to each node.\n */\nfunction addParent<T1 extends {type?: string}>(obj: T1, parent?: unknown): T1 {\n  const isNode = obj && typeof obj.type === 'string';\n  const childParent = isNode ? obj : parent;\n\n  for (const k in obj) {\n    const value = obj[k];\n    if (Array.isArray(value)) {\n      value.forEach(v => {\n        addParent(v, childParent);\n      });\n    } else if (value && typeof value === 'object') {\n      addParent(value, childParent);\n    }\n  }\n\n  if (isNode) {\n    Object.defineProperty(obj, 'parent', {\n      configurable: true,\n      writable: true,\n      enumerable: false,\n      value: parent || null,\n    });\n  }\n\n  return obj;\n}\n\nexport default parse;\n", "export default class CssParseError extends Error {\n  readonly reason: string;\n  readonly filename?: string;\n  readonly line: number;\n  readonly column: number;\n  readonly source: string;\n\n  constructor(\n    filename: string,\n    msg: string,\n    lineno: number,\n    column: number,\n    css: string,\n  ) {\n    super(filename + ':' + lineno + ':' + column + ': ' + msg);\n    this.reason = msg;\n    this.filename = filename;\n    this.line = lineno;\n    this.column = column;\n    this.source = css;\n  }\n}\n", "/**\n * Store position information for a node\n */\nexport default class Position {\n  start: {line: number; column: number};\n  end: {line: number; column: number};\n  source?: string;\n\n  constructor(\n    start: {line: number; column: number},\n    end: {line: number; column: number},\n    source: string,\n  ) {\n    this.start = start;\n    this.end = end;\n    this.source = source;\n  }\n}\n", "import CssParseError from './CssParseError';\nimport Position from './CssPosition';\n\nexport enum CssTypes {\n  stylesheet = 'stylesheet',\n  rule = 'rule',\n  declaration = 'declaration',\n  comment = 'comment',\n  container = 'container',\n  charset = 'charset',\n  document = 'document',\n  customMedia = 'custom-media',\n  fontFace = 'font-face',\n  host = 'host',\n  import = 'import',\n  keyframes = 'keyframes',\n  keyframe = 'keyframe',\n  layer = 'layer',\n  media = 'media',\n  namespace = 'namespace',\n  page = 'page',\n  startingStyle = 'starting-style',\n  supports = 'supports',\n}\n\nexport type CssCommonAST = {\n  type: CssTypes;\n};\n\nexport type CssCommonPositionAST = CssCommonAST & {\n  position?: Position;\n  parent?: unknown;\n};\n\nexport type CssStylesheetAST = CssCommonAST & {\n  type: CssTypes.stylesheet;\n  stylesheet: {\n    source?: string;\n    rules: Array<CssAtRuleAST>;\n    parsingErrors?: Array<CssParseError>;\n  };\n};\n\nexport type CssRuleAST = CssCommonPositionAST & {\n  type: CssTypes.rule;\n  selectors: Array<string>;\n  declarations: Array<CssDeclarationAST | CssCommentAST>;\n};\n\nexport type CssDeclarationAST = CssCommonPositionAST & {\n  type: CssTypes.declaration;\n  property: string;\n  value: string;\n};\n\nexport type CssCommentAST = CssCommonPositionAST & {\n  type: CssTypes.comment;\n  comment: string;\n};\nexport type CssContainerAST = CssCommonPositionAST & {\n  type: CssTypes.container;\n  container: string;\n  rules: Array<CssAtRuleAST>;\n};\n\nexport type CssCharsetAST = CssCommonPositionAST & {\n  type: CssTypes.charset;\n  charset: string;\n};\nexport type CssCustomMediaAST = CssCommonPositionAST & {\n  type: CssTypes.customMedia;\n  name: string;\n  media: string;\n};\nexport type CssDocumentAST = CssCommonPositionAST & {\n  type: CssTypes.document;\n  document: string;\n  vendor?: string;\n  rules: Array<CssAtRuleAST>;\n};\nexport type CssFontFaceAST = CssCommonPositionAST & {\n  type: CssTypes.fontFace;\n  declarations: Array<CssDeclarationAST | CssCommentAST>;\n};\nexport type CssHostAST = CssCommonPositionAST & {\n  type: CssTypes.host;\n  rules: Array<CssAtRuleAST>;\n};\nexport type CssImportAST = CssCommonPositionAST & {\n  type: CssTypes.import;\n  import: string;\n};\nexport type CssKeyframesAST = CssCommonPositionAST & {\n  type: CssTypes.keyframes;\n  name: string;\n  vendor?: string;\n  keyframes: Array<CssKeyframeAST | CssCommentAST>;\n};\nexport type CssKeyframeAST = CssCommonPositionAST & {\n  type: CssTypes.keyframe;\n  values: Array<string>;\n  declarations: Array<CssDeclarationAST | CssCommentAST>;\n};\nexport type CssLayerAST = CssCommonPositionAST & {\n  type: CssTypes.layer;\n  layer: string;\n  rules?: Array<CssAtRuleAST>;\n};\nexport type CssMediaAST = CssCommonPositionAST & {\n  type: CssTypes.media;\n  media: string;\n  rules: Array<CssAtRuleAST>;\n};\nexport type CssNamespaceAST = CssCommonPositionAST & {\n  type: CssTypes.namespace;\n  namespace: string;\n};\nexport type CssPageAST = CssCommonPositionAST & {\n  type: CssTypes.page;\n  selectors: Array<string>;\n  declarations: Array<CssDeclarationAST | CssCommentAST>;\n};\nexport type CssSupportsAST = CssCommonPositionAST & {\n  type: CssTypes.supports;\n  supports: string;\n  rules: Array<CssAtRuleAST>;\n};\n\nexport type CssStartingStyleAST = CssCommonPositionAST & {\n  type: CssTypes.startingStyle;\n  rules: Array<CssAtRuleAST>;\n};\n\nexport type CssAtRuleAST =\n  | CssRuleAST\n  | CssCommentAST\n  | CssContainerAST\n  | CssCharsetAST\n  | CssCustomMediaAST\n  | CssDocumentAST\n  | CssFontFaceAST\n  | CssHostAST\n  | CssImportAST\n  | CssKeyframesAST\n  | CssLayerAST\n  | CssMediaAST\n  | CssNamespaceAST\n  | CssPageAST\n  | CssSupportsAST\n  | CssStartingStyleAST;\n\nexport type CssAllNodesAST =\n  | CssAtRuleAST\n  | CssStylesheetAST\n  | CssDeclarationAST\n  | CssKeyframeAST;\n", "export const MAX_LOOP = 10000;\n\n/**\n * Find the first occurrence of any search string in the input string, ignoring escaped characters\n * @param string - The input string to search in\n * @param search - Array of strings to search for\n * @param position - Optional starting position for the search\n * @returns The index of the first match, or -1 if not found\n * @throws {Error} If too many escape sequences are encountered (> MAX_LOOP)\n * @example\n * ```ts\n * // Basic search\n * indexOfArrayNonEscaped('a,b,c', [',']) // 1\n *\n * // Handles escaped characters\n * indexOfArrayNonEscaped('a\\\\,b,c', [',']) // 4, the first comma is escaped\n * ```\n */\nexport const indexOfArrayNonEscaped = (\n  string: string,\n  search: Array<string>,\n  position?: number,\n): number => {\n  let currentPosition = position;\n  let maxLoop = MAX_LOOP;\n  do {\n    const all = search.map(v => string.indexOf(v, currentPosition));\n    all.push(string.indexOf('\\\\', currentPosition));\n    const foundAll = all.filter(v => v !== -1);\n    if (foundAll.length === 0) {\n      return -1;\n    }\n\n    const found = Math.min(...foundAll);\n    if (string[found] === '\\\\') {\n      currentPosition = found + 2;\n      maxLoop--;\n    } else {\n      return found;\n    }\n  } while (maxLoop > 0);\n\n  throw new Error('Too many escaping');\n};\n\n/**\n * Find the first occurrence of any search string in the input string, respecting brackets and quotes\n * @param string - The input string to search in\n * @param search - Array of strings to search for\n * @param position - Optional starting position for the search\n * @returns The index of the first match, or -1 if not found\n * @throws {Error} If too many escape sequences are encountered (> MAX_LOOP)\n * @example\n * ```ts\n * // Basic search\n * indexOfArrayWithBracketAndQuoteSupport('a,b,c', [',']) // 1\n *\n * // Respects brackets - won't match inside ()\n * indexOfArrayWithBracketAndQuoteSupport('(a,b),c', [',']) // 4, ignores the comma inside ()\n *\n * // Respects quotes - won't match inside quotes\n * indexOfArrayWithBracketAndQuoteSupport('\"a,b\",c', [',']) // 4, ignores the comma inside quotes\n * indexOfArrayWithBracketAndQuoteSupport(\"'a,b',c\", [',']) // 4, ignores the comma inside quotes\n *\n * // Handles escaped characters\n * indexOfArrayWithBracketAndQuoteSupport('a\\\\,b,c', [',']) // 4, the first comma is escaped\n * ```\n */\nexport const indexOfArrayWithBracketAndQuoteSupport = (\n  string: string,\n  search: Array<string>,\n  position?: number,\n): number => {\n  let currentSearchPosition = position;\n  let maxLoop = MAX_LOOP;\n\n  do {\n    const all = search.map(v => string.indexOf(v, currentSearchPosition));\n\n    all.push(string.indexOf('(', currentSearchPosition));\n    all.push(string.indexOf('\"', currentSearchPosition));\n    all.push(string.indexOf(\"'\", currentSearchPosition));\n    all.push(string.indexOf('\\\\', currentSearchPosition));\n\n    const foundAll = all.filter(v => v !== -1);\n    if (foundAll.length === 0) {\n      return -1;\n    }\n\n    const firstMatchPos = Math.min(...foundAll);\n    const char = string[firstMatchPos];\n    switch (char) {\n      case '\\\\':\n        currentSearchPosition = firstMatchPos + 2;\n        break;\n      case '(':\n        {\n          const endPosition = indexOfArrayWithBracketAndQuoteSupport(\n            string,\n            [')'],\n            firstMatchPos + 1,\n          );\n          if (endPosition === -1) {\n            return -1;\n          }\n          currentSearchPosition = endPosition + 1;\n        }\n        break;\n      case '\"':\n        {\n          const endQuotePosition = indexOfArrayNonEscaped(\n            string,\n            ['\"'],\n            firstMatchPos + 1,\n          );\n          if (endQuotePosition === -1) {\n            return -1;\n          }\n          currentSearchPosition = endQuotePosition + 1;\n        }\n        break;\n      case \"'\":\n        {\n          const endQuotePosition = indexOfArrayNonEscaped(\n            string,\n            [\"'\"],\n            firstMatchPos + 1,\n          );\n          if (endQuotePosition === -1) {\n            return -1;\n          }\n          currentSearchPosition = endQuotePosition + 1;\n        }\n        break;\n      default:\n        return firstMatchPos;\n    }\n    maxLoop--;\n  } while (maxLoop > 0);\n\n  throw new Error('Too many escaping');\n};\n\n/**\n * Split a string by search tokens, respecting brackets and quotes\n * @example\n * ```ts\n * splitWithBracketAndQuoteSupport('a,b', [',']) // ['a', 'b']\n * splitWithBracketAndQuoteSupport('a,(b,c)', [',']) // ['a', '(b,c)']\n * splitWithBracketAndQuoteSupport('a,\"b,c\"', [',']) // ['a', '\"b,c\"']\n * splitWithBracketAndQuoteSupport(\"a,'b,c'\", [',']) // ['a', \"'b,c'\"]\n * ```\n */\nexport const splitWithBracketAndQuoteSupport = (\n  string: string,\n  search: Array<string>,\n): Array<string> => {\n  const result: Array<string> = [];\n  let currentPosition = 0;\n  while (currentPosition < string.length) {\n    const index = indexOfArrayWithBracketAndQuoteSupport(\n      string,\n      search,\n      currentPosition,\n    );\n    if (index === -1) {\n      result.push(string.substring(currentPosition));\n      return result;\n    }\n    result.push(string.substring(currentPosition, index));\n    currentPosition = index + 1;\n  }\n  return result;\n};\n", "import {CssStylesheetAST} from '../type';\nimport Compiler, {CompilerOptions} from './compiler';\n\nexport default (node: CssStylesheetAST, options?: CompilerOptions) => {\n  const compiler = new Compiler(options || {});\n  return compiler.compile(node);\n};\n", "import {\n  CssAllNodesAST,\n  CssCharsetAST,\n  CssCommentAST,\n  CssCommonPositionAST,\n  CssContainerAST,\n  CssCustomMediaAST,\n  CssDeclarationAST,\n  CssDocumentAST,\n  CssFontFaceAST,\n  CssHostAST,\n  CssImportAST,\n  CssKeyframeAST,\n  CssKeyframesAST,\n  CssLayerAST,\n  CssMediaAST,\n  CssNamespaceAST,\n  CssPageAST,\n  CssRuleAST,\n  CssStartingStyleAST,\n  CssStylesheetAST,\n  CssSupportsAST,\n  CssTypes,\n} from '../type';\n\nexport type CompilerOptions = {\n  indent?: string;\n  compress?: boolean;\n};\n\nclass Compiler {\n  level = 0;\n  indentation = '  ';\n  compress = false;\n\n  constructor(options?: CompilerOptions) {\n    if (typeof options?.indent === 'string') {\n      this.indentation = options?.indent;\n    }\n    if (options?.compress) {\n      this.compress = true;\n    }\n  }\n\n  // We disable no-unused-vars for _position. We keep position for potential reintroduction of source-map\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  emit(str: string, _position?: CssCommonPositionAST['position']) {\n    return str;\n  }\n\n  /**\n   * Increase, decrease or return current indentation.\n   */\n  indent(level?: number) {\n    this.level = this.level || 1;\n\n    if (level) {\n      this.level += level;\n      return '';\n    }\n\n    return Array(this.level).join(this.indentation);\n  }\n\n  visit(node: CssAllNodesAST): string {\n    switch (node.type) {\n      case CssTypes.stylesheet:\n        return this.stylesheet(node);\n      case CssTypes.rule:\n        return this.rule(node);\n      case CssTypes.declaration:\n        return this.declaration(node);\n      case CssTypes.comment:\n        return this.comment(node);\n      case CssTypes.container:\n        return this.container(node);\n      case CssTypes.charset:\n        return this.charset(node);\n      case CssTypes.document:\n        return this.document(node);\n      case CssTypes.customMedia:\n        return this.customMedia(node);\n      case CssTypes.fontFace:\n        return this.fontFace(node);\n      case CssTypes.host:\n        return this.host(node);\n      case CssTypes.import:\n        return this.import(node);\n      case CssTypes.keyframes:\n        return this.keyframes(node);\n      case CssTypes.keyframe:\n        return this.keyframe(node);\n      case CssTypes.layer:\n        return this.layer(node);\n      case CssTypes.media:\n        return this.media(node);\n      case CssTypes.namespace:\n        return this.namespace(node);\n      case CssTypes.page:\n        return this.page(node);\n      case CssTypes.startingStyle:\n        return this.startingStyle(node);\n      case CssTypes.supports:\n        return this.supports(node);\n    }\n  }\n\n  mapVisit(nodes: Array<CssAllNodesAST>, delim?: string) {\n    let buf = '';\n    delim = delim || '';\n\n    for (let i = 0, length = nodes.length; i < length; i++) {\n      buf += this.visit(nodes[i]);\n      if (delim && i < length - 1) {\n        buf += this.emit(delim);\n      }\n    }\n\n    return buf;\n  }\n\n  compile(node: CssStylesheetAST) {\n    if (this.compress) {\n      return node.stylesheet.rules.map(this.visit, this).join('');\n    }\n\n    return this.stylesheet(node);\n  }\n\n  /**\n   * Visit stylesheet node.\n   */\n  stylesheet(node: CssStylesheetAST) {\n    return this.mapVisit(node.stylesheet.rules, '\\n\\n');\n  }\n\n  /**\n   * Visit comment node.\n   */\n  comment(node: CssCommentAST) {\n    if (this.compress) {\n      return this.emit('', node.position);\n    }\n    return this.emit(this.indent() + '/*' + node.comment + '*/', node.position);\n  }\n\n  /**\n   * Visit container node.\n   */\n  container(node: CssContainerAST) {\n    if (this.compress) {\n      return (\n        this.emit('@container ' + node.container, node.position) +\n        this.emit('{') +\n        this.mapVisit(node.rules) +\n        this.emit('}')\n      );\n    }\n    return (\n      this.emit(this.indent() + '@container ' + node.container, node.position) +\n      this.emit(' {\\n' + this.indent(1)) +\n      this.mapVisit(node.rules, '\\n\\n') +\n      this.emit('\\n' + this.indent(-1) + this.indent() + '}')\n    );\n  }\n\n  /**\n   * Visit container node.\n   */\n  layer(node: CssLayerAST) {\n    if (this.compress) {\n      return (\n        this.emit('@layer ' + node.layer, node.position) +\n        (node.rules\n          ? this.emit('{') +\n            this.mapVisit(<CssAllNodesAST[]>node.rules) +\n            this.emit('}')\n          : ';')\n      );\n    }\n    return (\n      this.emit(this.indent() + '@layer ' + node.layer, node.position) +\n      (node.rules\n        ? this.emit(' {\\n' + this.indent(1)) +\n          this.mapVisit(<CssAllNodesAST[]>node.rules, '\\n\\n') +\n          this.emit('\\n' + this.indent(-1) + this.indent() + '}')\n        : ';')\n    );\n  }\n\n  /**\n   * Visit import node.\n   */\n  import(node: CssImportAST) {\n    return this.emit('@import ' + node.import + ';', node.position);\n  }\n\n  /**\n   * Visit media node.\n   */\n  media(node: CssMediaAST) {\n    if (this.compress) {\n      return (\n        this.emit('@media ' + node.media, node.position) +\n        this.emit('{') +\n        this.mapVisit(node.rules) +\n        this.emit('}')\n      );\n    }\n    return (\n      this.emit(this.indent() + '@media ' + node.media, node.position) +\n      this.emit(' {\\n' + this.indent(1)) +\n      this.mapVisit(node.rules, '\\n\\n') +\n      this.emit('\\n' + this.indent(-1) + this.indent() + '}')\n    );\n  }\n\n  /**\n   * Visit document node.\n   */\n  document(node: CssDocumentAST) {\n    const doc = '@' + (node.vendor || '') + 'document ' + node.document;\n    if (this.compress) {\n      return (\n        this.emit(doc, node.position) +\n        this.emit('{') +\n        this.mapVisit(node.rules) +\n        this.emit('}')\n      );\n    }\n    return (\n      this.emit(doc, node.position) +\n      this.emit(' ' + ' {\\n' + this.indent(1)) +\n      this.mapVisit(node.rules, '\\n\\n') +\n      this.emit(this.indent(-1) + '\\n}')\n    );\n  }\n\n  /**\n   * Visit charset node.\n   */\n  charset(node: CssCharsetAST) {\n    return this.emit('@charset ' + node.charset + ';', node.position);\n  }\n\n  /**\n   * Visit namespace node.\n   */\n  namespace(node: CssNamespaceAST) {\n    return this.emit('@namespace ' + node.namespace + ';', node.position);\n  }\n\n  /**\n   * Visit container node.\n   */\n  startingStyle(node: CssStartingStyleAST) {\n    if (this.compress) {\n      return (\n        this.emit('@starting-style', node.position) +\n        this.emit('{') +\n        this.mapVisit(node.rules) +\n        this.emit('}')\n      );\n    }\n    return (\n      this.emit(this.indent() + '@starting-style', node.position) +\n      this.emit(' {\\n' + this.indent(1)) +\n      this.mapVisit(node.rules, '\\n\\n') +\n      this.emit('\\n' + this.indent(-1) + this.indent() + '}')\n    );\n  }\n\n  /**\n   * Visit supports node.\n   */\n  supports(node: CssSupportsAST) {\n    if (this.compress) {\n      return (\n        this.emit('@supports ' + node.supports, node.position) +\n        this.emit('{') +\n        this.mapVisit(node.rules) +\n        this.emit('}')\n      );\n    }\n    return (\n      this.emit(this.indent() + '@supports ' + node.supports, node.position) +\n      this.emit(' {\\n' + this.indent(1)) +\n      this.mapVisit(node.rules, '\\n\\n') +\n      this.emit('\\n' + this.indent(-1) + this.indent() + '}')\n    );\n  }\n\n  /**\n   * Visit keyframes node.\n   */\n  keyframes(node: CssKeyframesAST) {\n    if (this.compress) {\n      return (\n        this.emit(\n          '@' + (node.vendor || '') + 'keyframes ' + node.name,\n          node.position,\n        ) +\n        this.emit('{') +\n        this.mapVisit(node.keyframes) +\n        this.emit('}')\n      );\n    }\n    return (\n      this.emit(\n        '@' + (node.vendor || '') + 'keyframes ' + node.name,\n        node.position,\n      ) +\n      this.emit(' {\\n' + this.indent(1)) +\n      this.mapVisit(node.keyframes, '\\n') +\n      this.emit(this.indent(-1) + '}')\n    );\n  }\n\n  /**\n   * Visit keyframe node.\n   */\n  keyframe(node: CssKeyframeAST) {\n    const decls = node.declarations;\n    if (this.compress) {\n      return (\n        this.emit(node.values.join(','), node.position) +\n        this.emit('{') +\n        this.mapVisit(decls) +\n        this.emit('}')\n      );\n    }\n\n    return (\n      this.emit(this.indent()) +\n      this.emit(node.values.join(', '), node.position) +\n      this.emit(' {\\n' + this.indent(1)) +\n      this.mapVisit(decls, '\\n') +\n      this.emit(this.indent(-1) + '\\n' + this.indent() + '}\\n')\n    );\n  }\n\n  /**\n   * Visit page node.\n   */\n  page(node: CssPageAST) {\n    if (this.compress) {\n      const sel = node.selectors.length ? node.selectors.join(', ') : '';\n\n      return (\n        this.emit('@page ' + sel, node.position) +\n        this.emit('{') +\n        this.mapVisit(node.declarations) +\n        this.emit('}')\n      );\n    }\n    const sel = node.selectors.length ? node.selectors.join(', ') + ' ' : '';\n\n    return (\n      this.emit('@page ' + sel, node.position) +\n      this.emit('{\\n') +\n      this.emit(this.indent(1)) +\n      this.mapVisit(node.declarations, '\\n') +\n      this.emit(this.indent(-1)) +\n      this.emit('\\n}')\n    );\n  }\n\n  /**\n   * Visit font-face node.\n   */\n  fontFace(node: CssFontFaceAST) {\n    if (this.compress) {\n      return (\n        this.emit('@font-face', node.position) +\n        this.emit('{') +\n        this.mapVisit(node.declarations) +\n        this.emit('}')\n      );\n    }\n    return (\n      this.emit('@font-face ', node.position) +\n      this.emit('{\\n') +\n      this.emit(this.indent(1)) +\n      this.mapVisit(node.declarations, '\\n') +\n      this.emit(this.indent(-1)) +\n      this.emit('\\n}')\n    );\n  }\n\n  /**\n   * Visit host node.\n   */\n  host(node: CssHostAST) {\n    if (this.compress) {\n      return (\n        this.emit('@host', node.position) +\n        this.emit('{') +\n        this.mapVisit(node.rules) +\n        this.emit('}')\n      );\n    }\n    return (\n      this.emit('@host', node.position) +\n      this.emit(' {\\n' + this.indent(1)) +\n      this.mapVisit(node.rules, '\\n\\n') +\n      this.emit(this.indent(-1) + '\\n}')\n    );\n  }\n\n  /**\n   * Visit custom-media node.\n   */\n  customMedia(node: CssCustomMediaAST) {\n    return this.emit(\n      '@custom-media ' + node.name + ' ' + node.media + ';',\n      node.position,\n    );\n  }\n\n  /**\n   * Visit rule node.\n   */\n  rule(node: CssRuleAST) {\n    const decls = node.declarations;\n    if (!decls.length) {\n      return '';\n    }\n\n    if (this.compress) {\n      return (\n        this.emit(node.selectors.join(','), node.position) +\n        this.emit('{') +\n        this.mapVisit(decls) +\n        this.emit('}')\n      );\n    }\n    const indent = this.indent();\n\n    return (\n      this.emit(\n        node.selectors\n          .map(s => {\n            return indent + s;\n          })\n          .join(',\\n'),\n        node.position,\n      ) +\n      this.emit(' {\\n') +\n      this.emit(this.indent(1)) +\n      this.mapVisit(decls, '\\n') +\n      this.emit(this.indent(-1)) +\n      this.emit('\\n' + this.indent() + '}')\n    );\n  }\n\n  /**\n   * Visit declaration node.\n   */\n  declaration(node: CssDeclarationAST) {\n    if (this.compress) {\n      return (\n        this.emit(node.property + ':' + node.value, node.position) +\n        this.emit(';')\n      );\n    }\n    if (node.property === 'grid-template-areas')\n      return (\n        this.emit(this.indent()) +\n        this.emit(\n          node.property +\n            ': ' +\n            node.value.split('\\n').join('\\n'.padEnd(22) + this.indent()),\n          node.position,\n        ) +\n        this.emit(';')\n      );\n    return (\n      this.emit(this.indent()) +\n      this.emit(node.property + ': ' + node.value, node.position) +\n      this.emit(';')\n    );\n  }\n}\n\nexport default Compiler;\n"], "names": [], "version": 3, "file": "index.cjs.map"}