{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M13 16a3 3 0 0 1 2.24 5\",\n  key: \"1epib5\"\n}], [\"path\", {\n  d: \"M18 12h.01\",\n  key: \"yjnet6\"\n}], [\"path\", {\n  d: \"M18 21h-8a4 4 0 0 1-4-4 7 7 0 0 1 7-7h.2L9.6 6.4a1 1 0 1 1 2.8-2.8L15.8 7h.2c3.3 0 6 2.7 6 6v1a2 2 0 0 1-2 2h-1a3 3 0 0 0-3 3\",\n  key: \"ue9ozu\"\n}], [\"path\", {\n  d: \"M20 8.54V4a2 2 0 1 0-4 0v3\",\n  key: \"49iql8\"\n}], [\"path\", {\n  d: \"M7.612 12.524a3 3 0 1 0-1.6 4.3\",\n  key: \"1e33i0\"\n}]];\nconst Rabbit = createLucideIcon(\"rabbit\", __iconNode);\nexport { __iconNode, Rabbit as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "Rabbit", "createLucideIcon"], "sources": ["D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\node_modules\\lucide-react\\src\\icons\\rabbit.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M13 16a3 3 0 0 1 2.24 5', key: '1epib5' }],\n  ['path', { d: 'M18 12h.01', key: 'yjnet6' }],\n  [\n    'path',\n    {\n      d: 'M18 21h-8a4 4 0 0 1-4-4 7 7 0 0 1 7-7h.2L9.6 6.4a1 1 0 1 1 2.8-2.8L15.8 7h.2c3.3 0 6 2.7 6 6v1a2 2 0 0 1-2 2h-1a3 3 0 0 0-3 3',\n      key: 'ue9ozu',\n    },\n  ],\n  ['path', { d: 'M20 8.54V4a2 2 0 1 0-4 0v3', key: '49iql8' }],\n  ['path', { d: 'M7.612 12.524a3 3 0 1 0-1.6 4.3', key: '1e33i0' }],\n];\n\n/**\n * @component @name Rabbit\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTMgMTZhMyAzIDAgMCAxIDIuMjQgNSIgLz4KICA8cGF0aCBkPSJNMTggMTJoLjAxIiAvPgogIDxwYXRoIGQ9Ik0xOCAyMWgtOGE0IDQgMCAwIDEtNC00IDcgNyAwIDAgMSA3LTdoLjJMOS42IDYuNGExIDEgMCAxIDEgMi44LTIuOEwxNS44IDdoLjJjMy4zIDAgNiAyLjcgNiA2djFhMiAyIDAgMCAxLTIgMmgtMWEzIDMgMCAwIDAtMyAzIiAvPgogIDxwYXRoIGQ9Ik0yMCA4LjU0VjRhMiAyIDAgMSAwLTQgMHYzIiAvPgogIDxwYXRoIGQ9Ik03LjYxMiAxMi41MjRhMyAzIDAgMSAwLTEuNiA0LjMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/rabbit\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Rabbit = createLucideIcon('rabbit', __iconNode);\n\nexport default Rabbit;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,yBAA2B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CACE,QACA;EACED,CAAG;EACHC,GAAK;AAAA,EAET,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,4BAA8B;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3D,CAAC,MAAQ;EAAED,CAAA,EAAG,iCAAmC;EAAAC,GAAA,EAAK;AAAU,GAClE;AAaM,MAAAC,MAAA,GAASC,gBAAiB,WAAUJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}