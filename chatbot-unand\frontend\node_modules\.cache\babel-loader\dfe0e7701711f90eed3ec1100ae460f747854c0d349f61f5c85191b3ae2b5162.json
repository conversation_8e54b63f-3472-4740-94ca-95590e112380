{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 10.01h.01\",\n  key: \"1e9xi7\"\n}], [\"path\", {\n  d: \"M10 14.01h.01\",\n  key: \"ac23bv\"\n}], [\"path\", {\n  d: \"M14 10.01h.01\",\n  key: \"2wfrvf\"\n}], [\"path\", {\n  d: \"M14 14.01h.01\",\n  key: \"8tw8yn\"\n}], [\"path\", {\n  d: \"M18 6v11.5\",\n  key: \"dkbidh\"\n}], [\"path\", {\n  d: \"M6 6v12\",\n  key: \"vkc79e\"\n}], [\"rect\", {\n  x: \"2\",\n  y: \"6\",\n  width: \"20\",\n  height: \"12\",\n  rx: \"2\",\n  key: \"1wpnh2\"\n}]];\nconst Bandage = createLucideIcon(\"bandage\", __iconNode);\nexport { __iconNode, Bandage as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "x", "y", "width", "height", "rx", "Bandage", "createLucideIcon"], "sources": ["D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\node_modules\\lucide-react\\src\\icons\\bandage.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M10 10.01h.01', key: '1e9xi7' }],\n  ['path', { d: 'M10 14.01h.01', key: 'ac23bv' }],\n  ['path', { d: 'M14 10.01h.01', key: '2wfrvf' }],\n  ['path', { d: 'M14 14.01h.01', key: '8tw8yn' }],\n  ['path', { d: 'M18 6v11.5', key: 'dkbidh' }],\n  ['path', { d: 'M6 6v12', key: 'vkc79e' }],\n  ['rect', { x: '2', y: '6', width: '20', height: '12', rx: '2', key: '1wpnh2' }],\n];\n\n/**\n * @component @name Bandage\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAgMTAuMDFoLjAxIiAvPgogIDxwYXRoIGQ9Ik0xMCAxNC4wMWguMDEiIC8+CiAgPHBhdGggZD0iTTE0IDEwLjAxaC4wMSIgLz4KICA8cGF0aCBkPSJNMTQgMTQuMDFoLjAxIiAvPgogIDxwYXRoIGQ9Ik0xOCA2djExLjUiIC8+CiAgPHBhdGggZD0iTTYgNnYxMiIgLz4KICA8cmVjdCB4PSIyIiB5PSI2IiB3aWR0aD0iMjAiIGhlaWdodD0iMTIiIHJ4PSIyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/bandage\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Bandage = createLucideIcon('bandage', __iconNode);\n\nexport default Bandage;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,eAAiB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9C,CAAC,MAAQ;EAAED,CAAA,EAAG,eAAiB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9C,CAAC,MAAQ;EAAED,CAAA,EAAG,eAAiB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9C,CAAC,MAAQ;EAAED,CAAA,EAAG,eAAiB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9C,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAKC,CAAG;EAAKC,KAAO;EAAMC,MAAA,EAAQ,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAL,GAAA,EAAK;AAAU,GAChF;AAaM,MAAAM,OAAA,GAAUC,gBAAiB,YAAWT,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}