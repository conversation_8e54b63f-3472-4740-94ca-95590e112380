{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m11 7-3 5h4l-3 5\",\n  key: \"b4a64w\"\n}], [\"path\", {\n  d: \"M14.856 6H16a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2h-2.935\",\n  key: \"lre1cr\"\n}], [\"path\", {\n  d: \"M22 14v-4\",\n  key: \"14q9d5\"\n}], [\"path\", {\n  d: \"M5.14 18H4a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h2.936\",\n  key: \"13q5k0\"\n}]];\nconst BatteryCharging = createLucideIcon(\"battery-charging\", __iconNode);\nexport { __iconNode, BatteryCharging as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "BatteryCharging", "createLucideIcon"], "sources": ["D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\node_modules\\lucide-react\\src\\icons\\battery-charging.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm11 7-3 5h4l-3 5', key: 'b4a64w' }],\n  ['path', { d: 'M14.856 6H16a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2h-2.935', key: 'lre1cr' }],\n  ['path', { d: 'M22 14v-4', key: '14q9d5' }],\n  ['path', { d: 'M5.14 18H4a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h2.936', key: '13q5k0' }],\n];\n\n/**\n * @component @name BatteryCharging\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTEgNy0zIDVoNGwtMyA1IiAvPgogIDxwYXRoIGQ9Ik0xNC44NTYgNkgxNmEyIDIgMCAwIDEgMiAydjhhMiAyIDAgMCAxLTIgMmgtMi45MzUiIC8+CiAgPHBhdGggZD0iTTIyIDE0di00IiAvPgogIDxwYXRoIGQ9Ik01LjE0IDE4SDRhMiAyIDAgMCAxLTItMlY4YTIgMiAwIDAgMSAyLTJoMi45MzYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/battery-charging\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst BatteryCharging = createLucideIcon('battery-charging', __iconNode);\n\nexport default BatteryCharging;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,kBAAoB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACjD,CAAC,MAAQ;EAAED,CAAA,EAAG,mDAAqD;EAAAC,GAAA,EAAK;AAAA,CAAU,GAClF,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,gDAAkD;EAAAC,GAAA,EAAK;AAAU,GACjF;AAaM,MAAAC,eAAA,GAAkBC,gBAAiB,qBAAoBJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}