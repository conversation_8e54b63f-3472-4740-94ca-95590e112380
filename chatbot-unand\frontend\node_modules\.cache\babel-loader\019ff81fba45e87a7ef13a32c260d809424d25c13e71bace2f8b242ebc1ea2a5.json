{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M19.929 9.629A1 1 0 0 1 19 11H9a1 1 0 0 1-.928-1.371l2-5A1 1 0 0 1 11 4h6a1 1 0 0 1 .928.629z\",\n  key: \"1uvrbf\"\n}], [\"path\", {\n  d: \"M6 15a2 2 0 0 1 2 2v2a2 2 0 0 1-2 2H5a1 1 0 0 1-1-1v-4a1 1 0 0 1 1-1z\",\n  key: \"154r2a\"\n}], [\"path\", {\n  d: \"M8 18h4a2 2 0 0 0 2-2v-5\",\n  key: \"z9mbu0\"\n}]];\nconst LampWallUp = createLucideIcon(\"lamp-wall-up\", __iconNode);\nexport { __iconNode, LampWallUp as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "LampWallUp", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\node_modules\\lucide-react\\src\\icons\\lamp-wall-up.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M19.929 9.629A1 1 0 0 1 19 11H9a1 1 0 0 1-.928-1.371l2-5A1 1 0 0 1 11 4h6a1 1 0 0 1 .928.629z',\n      key: '1uvrbf',\n    },\n  ],\n  [\n    'path',\n    { d: 'M6 15a2 2 0 0 1 2 2v2a2 2 0 0 1-2 2H5a1 1 0 0 1-1-1v-4a1 1 0 0 1 1-1z', key: '154r2a' },\n  ],\n  ['path', { d: 'M8 18h4a2 2 0 0 0 2-2v-5', key: 'z9mbu0' }],\n];\n\n/**\n * @component @name LampWallUp\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTkuOTI5IDkuNjI5QTEgMSAwIDAgMSAxOSAxMUg5YTEgMSAwIDAgMS0uOTI4LTEuMzcxbDItNUExIDEgMCAwIDEgMTEgNGg2YTEgMSAwIDAgMSAuOTI4LjYyOXoiIC8+CiAgPHBhdGggZD0iTTYgMTVhMiAyIDAgMCAxIDIgMnYyYTIgMiAwIDAgMS0yIDJINWExIDEgMCAwIDEtMS0xdi00YTEgMSAwIDAgMSAxLTF6IiAvPgogIDxwYXRoIGQ9Ik04IDE4aDRhMiAyIDAgMCAwIDItMnYtNSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/lamp-wall-up\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst LampWallUp = createLucideIcon('lamp-wall-up', __iconNode);\n\nexport default LampWallUp;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AAAA,EAET,EACA,CACE,QACA;EAAED,CAAA,EAAG,uEAAyE;EAAAC,GAAA,EAAK;AAAS,EAC9F,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,0BAA4B;EAAAC,GAAA,EAAK;AAAU,GAC3D;AAaM,MAAAC,UAAA,GAAaC,gBAAiB,iBAAgBJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}