{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website kp\\\\chatbot-unand\\\\frontend\\\\src\\\\contexts\\\\ThemeContext.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ThemeContext = /*#__PURE__*/createContext();\nexport const useTheme = () => {\n  _s();\n  const context = useContext(ThemeContext);\n  if (!context) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n};\n_s(useTheme, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const ThemeProvider = ({\n  children\n}) => {\n  _s2();\n  const [theme, setTheme] = useState(() => {\n    // Check localStorage first, then system preference\n    const savedTheme = localStorage.getItem('theme');\n    if (savedTheme) {\n      return savedTheme;\n    }\n\n    // Check system preference\n    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {\n      return 'dark';\n    }\n    return 'light';\n  });\n  useEffect(() => {\n    const root = window.document.documentElement;\n\n    // Remove previous theme classes\n    root.classList.remove('light', 'dark');\n\n    // Add current theme class\n    root.classList.add(theme);\n\n    // Save to localStorage\n    localStorage.setItem('theme', theme);\n  }, [theme]);\n  const toggleTheme = () => {\n    setTheme(prevTheme => prevTheme === 'light' ? 'dark' : 'light');\n  };\n  const value = {\n    theme,\n    setTheme,\n    toggleTheme\n  };\n  return /*#__PURE__*/_jsxDEV(ThemeContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 53,\n    columnNumber: 5\n  }, this);\n};\n_s2(ThemeProvider, \"YYSo4DTJsOqHSb1pJPOmWx5UIcU=\");\n_c = ThemeProvider;\nvar _c;\n$RefreshReg$(_c, \"ThemeProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useEffect", "useState", "jsxDEV", "_jsxDEV", "ThemeContext", "useTheme", "_s", "context", "Error", "ThemeProvider", "children", "_s2", "theme", "setTheme", "savedTheme", "localStorage", "getItem", "window", "matchMedia", "matches", "root", "document", "documentElement", "classList", "remove", "add", "setItem", "toggleTheme", "prevTheme", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/website kp/chatbot-unand/frontend/src/contexts/ThemeContext.jsx"], "sourcesContent": ["import React, { createContext, useContext, useEffect, useState } from 'react';\n\nconst ThemeContext = createContext();\n\nexport const useTheme = () => {\n  const context = useContext(ThemeContext);\n  if (!context) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n};\n\nexport const ThemeProvider = ({ children }) => {\n  const [theme, setTheme] = useState(() => {\n    // Check localStorage first, then system preference\n    const savedTheme = localStorage.getItem('theme');\n    if (savedTheme) {\n      return savedTheme;\n    }\n    \n    // Check system preference\n    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {\n      return 'dark';\n    }\n    \n    return 'light';\n  });\n\n  useEffect(() => {\n    const root = window.document.documentElement;\n    \n    // Remove previous theme classes\n    root.classList.remove('light', 'dark');\n    \n    // Add current theme class\n    root.classList.add(theme);\n    \n    // Save to localStorage\n    localStorage.setItem('theme', theme);\n  }, [theme]);\n\n  const toggleTheme = () => {\n    setTheme(prevTheme => prevTheme === 'light' ? 'dark' : 'light');\n  };\n\n  const value = {\n    theme,\n    setTheme,\n    toggleTheme,\n  };\n\n  return (\n    <ThemeContext.Provider value={value}>\n      {children}\n    </ThemeContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9E,MAAMC,YAAY,gBAAGN,aAAa,CAAC,CAAC;AAEpC,OAAO,MAAMO,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAMC,OAAO,GAAGR,UAAU,CAACK,YAAY,CAAC;EACxC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,8CAA8C,CAAC;EACjE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,QAAQ;AAQrB,OAAO,MAAMI,aAAa,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC7C,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGZ,QAAQ,CAAC,MAAM;IACvC;IACA,MAAMa,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAChD,IAAIF,UAAU,EAAE;MACd,OAAOA,UAAU;IACnB;;IAEA;IACA,IAAIG,MAAM,CAACC,UAAU,IAAID,MAAM,CAACC,UAAU,CAAC,8BAA8B,CAAC,CAACC,OAAO,EAAE;MAClF,OAAO,MAAM;IACf;IAEA,OAAO,OAAO;EAChB,CAAC,CAAC;EAEFnB,SAAS,CAAC,MAAM;IACd,MAAMoB,IAAI,GAAGH,MAAM,CAACI,QAAQ,CAACC,eAAe;;IAE5C;IACAF,IAAI,CAACG,SAAS,CAACC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC;;IAEtC;IACAJ,IAAI,CAACG,SAAS,CAACE,GAAG,CAACb,KAAK,CAAC;;IAEzB;IACAG,YAAY,CAACW,OAAO,CAAC,OAAO,EAAEd,KAAK,CAAC;EACtC,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;EAEX,MAAMe,WAAW,GAAGA,CAAA,KAAM;IACxBd,QAAQ,CAACe,SAAS,IAAIA,SAAS,KAAK,OAAO,GAAG,MAAM,GAAG,OAAO,CAAC;EACjE,CAAC;EAED,MAAMC,KAAK,GAAG;IACZjB,KAAK;IACLC,QAAQ;IACRc;EACF,CAAC;EAED,oBACExB,OAAA,CAACC,YAAY,CAAC0B,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAnB,QAAA,EACjCA;EAAQ;IAAAqB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACY,CAAC;AAE5B,CAAC;AAACvB,GAAA,CA5CWF,aAAa;AAAA0B,EAAA,GAAb1B,aAAa;AAAA,IAAA0B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}