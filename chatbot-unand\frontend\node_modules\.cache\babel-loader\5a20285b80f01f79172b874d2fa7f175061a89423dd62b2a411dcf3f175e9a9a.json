{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m17 3-5 5-5-5h10\",\n  key: \"1ftt6x\"\n}], [\"path\", {\n  d: \"m17 21-5-5-5 5h10\",\n  key: \"1m0wmu\"\n}], [\"path\", {\n  d: \"M4 12H2\",\n  key: \"rhcxmi\"\n}], [\"path\", {\n  d: \"M10 12H8\",\n  key: \"s88cx1\"\n}], [\"path\", {\n  d: \"M16 12h-2\",\n  key: \"10asgb\"\n}], [\"path\", {\n  d: \"M22 12h-2\",\n  key: \"14jgyd\"\n}]];\nconst FlipVertical2 = createLucideIcon(\"flip-vertical-2\", __iconNode);\nexport { __iconNode, FlipVertical2 as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "FlipVertical2", "createLucideIcon"], "sources": ["D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\node_modules\\lucide-react\\src\\icons\\flip-vertical-2.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm17 3-5 5-5-5h10', key: '1ftt6x' }],\n  ['path', { d: 'm17 21-5-5-5 5h10', key: '1m0wmu' }],\n  ['path', { d: 'M4 12H2', key: 'rhcxmi' }],\n  ['path', { d: 'M10 12H8', key: 's88cx1' }],\n  ['path', { d: 'M16 12h-2', key: '10asgb' }],\n  ['path', { d: 'M22 12h-2', key: '14jgyd' }],\n];\n\n/**\n * @component @name FlipVertical2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTcgMy01IDUtNS01aDEwIiAvPgogIDxwYXRoIGQ9Im0xNyAyMS01LTUtNSA1aDEwIiAvPgogIDxwYXRoIGQ9Ik00IDEySDIiIC8+CiAgPHBhdGggZD0iTTEwIDEySDgiIC8+CiAgPHBhdGggZD0iTTE2IDEyaC0yIiAvPgogIDxwYXRoIGQ9Ik0yMiAxMmgtMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/flip-vertical-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst FlipVertical2 = createLucideIcon('flip-vertical-2', __iconNode);\n\nexport default FlipVertical2;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,kBAAoB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACjD,CAAC,MAAQ;EAAED,CAAA,EAAG,mBAAqB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAClD,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAU,GAC5C;AAaM,MAAAC,aAAA,GAAgBC,gBAAiB,oBAAmBJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}