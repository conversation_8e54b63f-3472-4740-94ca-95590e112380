import e from"postcss-value-parser";function n(e){const n=e[0];let t=e[1],r=e[2];if(t/=100,r/=100,t+r>=1){const e=t/(t+r);return[e,e,e].map((e=>Math.round(255*e)))}const u=function(e){let n=e[0],t=e[1],r=e[2];n%=360,n<0&&(n+=360);function u(e){const u=(e+n/30)%12,o=t*Math.min(r,1-r);return r-o*Math.max(-1,Math.min(u-3,9-u,1))}return t/=100,r/=100,[u(0),u(8),u(4)]}([n,100,50]);for(let e=0;e<3;e++)u[e]*=1-t-r,u[e]+=t;return u.map((e=>Math.round(255*e)))}function t(t){const i=t.nodes.slice().filter((e=>"comment"!==e.type&&"space"!==e.type)),s=function(n){if(!function(n){if(!n||"word"!==n.type)return!1;if(!a(n))return!1;const t=e.unit(n.value);if(!t)return!1;const r=t.unit.toLowerCase();return!!t.number&&("deg"===r||"grad"===r||"rad"===r||"turn"===r||""===r)}(n[0]))return null;if(!r(n[1]))return null;if(!r(n[2]))return null;const t={h:e.unit(n[0].value),hNode:n[0],w:e.unit(n[1].value),wNode:n[1],b:e.unit(n[2].value),bNode:n[2]};if(function(e){switch(e.unit.toLowerCase()){case"deg":return void(e.unit="");case"rad":return e.unit="",void(e.number=(180*parseFloat(e.number)/Math.PI).toString());case"grad":return e.unit="",void(e.number=(.9*parseFloat(e.number)).toString());case"turn":e.unit="",e.number=(360*parseFloat(e.number)).toString()}}(t.h),""!==t.h.unit)return null;o(t.w),o(t.b),function(e){return e&&"div"===e.type&&"/"===e.value}(n[3])&&(t.slash=n[3]);(r(n[4])||function(e){return e&&"function"===e.type&&"calc"===e.value.toLowerCase()}(n[4])||function(e){return e&&"function"===e.type&&"var"===e.value.toLowerCase()}(n[4]))&&(t.alpha=n[4]);return t}(i);if(!s)return;if(i.length>3&&(!s.slash||!s.alpha))return;t.value="rgb",function(n,t,r){if(!t||!r)return;if(n.value="rgba",t.value=",",t.before="",!function(n){if(!n||"word"!==n.type)return!1;if(!a(n))return!1;const t=e.unit(n.value);if(!t)return!1;return!!t.number}(r))return;const u=e.unit(r.value);if(!u)return;"%"===u.unit&&(u.number=String(parseFloat(u.number)/100),r.value=String(u.number))}(t,s.slash,s.alpha);const[l,c,f]=[(p=s).hNode,p.wNode,p.bNode];var p;const[d,v,b]=function(e){return[e.h,e.w,e.b]}(s),m=n([d.number,v.number,b.number].map((e=>parseFloat(e))));t.nodes.splice(t.nodes.indexOf(l)+1,0,{sourceIndex:0,sourceEndIndex:1,value:",",type:"div",before:"",after:""}),t.nodes.splice(t.nodes.indexOf(c)+1,0,{sourceIndex:0,sourceEndIndex:1,value:",",type:"div",before:"",after:""}),u(t.nodes,l,{...l,value:String(m[0])}),u(t.nodes,c,{...c,value:String(m[1])}),u(t.nodes,f,{...f,value:String(m[2])})}function r(n){if(!n||"word"!==n.type)return!1;if(!a(n))return!1;const t=e.unit(n.value);return!!t&&("%"===t.unit||""===t.unit)}function u(e,n,t){const r=e.indexOf(n);e[r]=t}function o(e){if("%"!==e.unit)return e.unit="%",void(e.number=(100*parseFloat(e.number)).toString())}function a(n){if(!n||!n.value)return!1;try{return!1!==e.unit(n.value)}catch(e){return!1}}const i=n=>{const r="preserve"in Object(n)&&Boolean(n.preserve);return{postcssPlugin:"postcss-hwb-function",Declaration:(n,{result:u,postcss:o})=>{if(r&&function(e){let n=e.parent;for(;n;)if("atrule"===n.type){if("supports"===n.name.toLowerCase()&&-1!==n.params.toLowerCase().indexOf("(color: hwb(0% 0 0))"))return!0;n=n.parent}else n=n.parent;return!1}(n))return;const a=n.value;if(!a.toLowerCase().includes("hwb"))return;const i=function(n,r,u){let o;try{o=e(n)}catch(e){r.warn(u,`Failed to parse value '${n}' as a hwb function. Leaving the original value intact.`)}if(void 0===o)return;o.walk((e=>{e.type&&"function"===e.type&&"hwb"===e.value.toLowerCase()&&t(e)}));const a=String(o);if(a===n)return;return a}(a,n,u);if(void 0!==i)if(n.variable&&r){const e=n.parent,t=o.atRule({name:"supports",params:"(color: hwb(0% 0 0))",source:n.source}),r=e.clone();r.removeAll(),r.append(n.clone()),t.append(r),function(e,n,t){let r=n,u=n.next();for(;r&&u&&"atrule"===u.type&&"supports"===u.name.toLowerCase()&&u.params===t;)r=u,u=u.next();r.after(e)}(t,e,"(color: hwb(0% 0 0))"),n.replaceWith(n.clone({value:i}))}else r?n.cloneBefore({value:i}):n.replaceWith(n.clone({value:i}))}}};i.postcss=!0;export{i as default};
