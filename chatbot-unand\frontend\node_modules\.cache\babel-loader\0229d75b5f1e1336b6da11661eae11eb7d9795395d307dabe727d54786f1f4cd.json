{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website kp\\\\chatbot-unand\\\\frontend\\\\src\\\\App.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { BrowserRouter as Router, Routes, Route, Navigate } from \"react-router-dom\";\nimport ChatWindow from \"./ChatWindow\";\nimport ChatSidebar from \"./ChatSidebar\";\nimport TelegramButton from \"./TelegramButton\";\nimport ThemeToggle from \"./components/ThemeToggle\";\nimport Login from \"./components/Login\";\nimport AdminLogin from \"./components/AdminLogin\";\nimport AdminDashboard from \"./components/AdminDashboard\";\nimport AdminUpload from \"./components/AdminUpload\";\nimport SessionGuard from \"./components/SessionGuard\";\nimport { ThemeProvider } from \"./contexts/ThemeContext\";\nimport { AuthProvider, useAuth } from \"./contexts/AuthContext\";\nimport { getSessionMessages } from \"./api\";\nimport \"./index.css\"; // Pastikan Tailwind CSS diimpor\n\n// Protected Admin Route Component\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProtectedAdminRoute = ({\n  children\n}) => {\n  _s();\n  const {\n    isAdminAuthenticated,\n    loading\n  } = useAuth();\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-r from-green-50 to-yellow-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Memuat...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this);\n  }\n  return isAdminAuthenticated ? children : /*#__PURE__*/_jsxDEV(Navigate, {\n    to: \"/admin/login\",\n    replace: true\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 40,\n    columnNumber: 5\n  }, this);\n};\n\n// Main App Component with Authentication\n_s(ProtectedAdminRoute, \"0TGAiQWd8p/icbdoQ5ni3oUjePk=\", false, function () {\n  return [useAuth];\n});\n_c = ProtectedAdminRoute;\nconst MainApp = () => {\n  _s2();\n  const {\n    user,\n    loading\n  } = useAuth();\n  const [currentSessionId, setCurrentSessionId] = useState(null);\n  const [isSidebarOpen, setIsSidebarOpen] = useState(false);\n  const [messages, setMessages] = useState([{\n    id: 1,\n    text: \"Halo! Saya adalah Chatbot UNAND. Saya siap membantu Anda dengan pertanyaan seputar peraturan kampus dan pemerintah. Silakan ajukan pertanyaan Anda!\",\n    isBot: true,\n    timestamp: new Date(),\n    is_greeting: true\n  }]);\n\n  // Reset state when user changes (login/logout)\n  useEffect(() => {\n    console.log(\"User changed:\", (user === null || user === void 0 ? void 0 : user.email) || \"logged out\");\n\n    // Reset to initial state when user changes\n    setCurrentSessionId(null);\n    setMessages([{\n      id: 1,\n      text: \"Halo! Saya adalah Chatbot UNAND. Saya siap membantu Anda dengan pertanyaan seputar peraturan kampus dan pemerintah. Silakan ajukan pertanyaan Anda!\",\n      isBot: true,\n      timestamp: new Date(),\n      is_greeting: true\n    }]);\n\n    // Close sidebar on mobile\n    if (window.innerWidth < 1024) {\n      setIsSidebarOpen(false);\n    }\n  }, [user]);\n  const handleSessionSelect = async sessionId => {\n    try {\n      setCurrentSessionId(sessionId);\n      const sessionMessages = await getSessionMessages(sessionId);\n\n      // Convert API messages to frontend format\n      const convertedMessages = sessionMessages.map(msg => ({\n        id: msg.id,\n        text: msg.content,\n        isBot: msg.message_type === \"bot\",\n        timestamp: new Date(msg.timestamp),\n        sources: msg.sources,\n        sources_count: msg.sources ? msg.sources.length : 0,\n        summary: msg.summary,\n        suggestions: msg.suggestions\n      }));\n      setMessages(convertedMessages);\n      // Close sidebar on mobile after selecting session\n      if (window.innerWidth < 1024) {\n        setIsSidebarOpen(false);\n      }\n    } catch (error) {\n      console.error(\"Error loading session messages:\", error);\n      alert(\"Gagal memuat riwayat percakapan\");\n    }\n  };\n  const handleNewChat = () => {\n    setCurrentSessionId(null);\n    setMessages([{\n      id: 1,\n      text: \"Halo! Saya adalah Chatbot UNAND. Saya siap membantu Anda dengan pertanyaan seputar peraturan kampus dan pemerintah. Silakan ajukan pertanyaan Anda!\",\n      isBot: true,\n      timestamp: new Date(),\n      is_greeting: true\n    }]);\n    // Close sidebar on mobile after new chat\n    if (window.innerWidth < 1024) {\n      setIsSidebarOpen(false);\n    }\n  };\n  const toggleSidebar = () => {\n    setIsSidebarOpen(!isSidebarOpen);\n  };\n\n  // Handle loading state\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-r from-green-50 to-yellow-50 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 dark:text-gray-400\",\n          children: \"Memuat...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Handle unauthenticated state\n  if (!user) {\n    return /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(SessionGuard, {\n    requiredSessionType: \"user\",\n    children: /*#__PURE__*/_jsxDEV(ThemeProvider, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"h-screen bg-gradient-to-r from-green-50 to-yellow-50 dark:from-gray-900 dark:to-gray-800 flex overflow-hidden relative\",\n        children: [isSidebarOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden\",\n          onClick: () => setIsSidebarOpen(false)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `fixed lg:static lg:translate-x-0 transition-transform duration-300 ease-in-out z-50 w-80 ${isSidebarOpen ? \"translate-x-0\" : \"-translate-x-full\"}`,\n          children: /*#__PURE__*/_jsxDEV(ChatSidebar, {\n            currentSessionId: currentSessionId,\n            onSessionSelect: handleSessionSelect,\n            onNewChat: handleNewChat,\n            onClose: () => setIsSidebarOpen(false)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 flex flex-col min-w-0\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"border-b-2 border-green-600 dark:border-green-400 p-3 lg:p-6 shadow-lg flex-shrink-0 bg-transparent\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"lg:hidden grid grid-cols-3 items-center mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-start\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: toggleSidebar,\n                  className: \"p-2 rounded-lg bg-green-600 dark:bg-green-500 text-white hover:bg-green-700 dark:hover:bg-green-600 transition-colors\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-6 h-6\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M4 6h16M4 12h16M4 18h16\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 191,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 185,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 181,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-center gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"/lambang-unand.jpg\",\n                  alt: \"Logo UNAND\",\n                  className: \"w-8 h-8 object-contain rounded border border-green-600 dark:border-green-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 203,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-lg font-bold text-green-700 dark:text-green-300\",\n                  children: \"TANYO UNAND\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 208,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-end items-center\",\n                children: /*#__PURE__*/_jsxDEV(ThemeToggle, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 215,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"hidden lg:grid lg:grid-cols-3 items-center h-19 w-full\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-shrink-0\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: \"/lambang-unand.jpg\",\n                    alt: \"Logo Universitas Andalas\",\n                    className: \"w-14 h-14 object-contain rounded-lg shadow-md border-2 border-green-600 dark:border-green-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 224,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 223,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex flex-col justify-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                    className: \"text-xl font-bold text-green-700 dark:text-green-300 leading-tight\",\n                    children: \"TANYO UNAND\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 231,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-green-600 dark:text-green-400 text-sm font-medium leading-tight\",\n                    children: \"Tampaik batanyo Seputar Universitas Andalas\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 234,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-yellow-700 dark:text-yellow-400 text-xs font-bold tracking-wider uppercase mt-1\",\n                    children: \"\\\"UNTUK KEDJAJAAN BANGSA\\\"\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 237,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 230,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-center\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-end\",\n                children: /*#__PURE__*/_jsxDEV(ThemeToggle, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 min-h-0\",\n            children: /*#__PURE__*/_jsxDEV(ChatWindow, {\n              messages: messages,\n              setMessages: setMessages,\n              currentSessionId: currentSessionId,\n              setCurrentSessionId: setCurrentSessionId\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TelegramButton, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 148,\n    columnNumber: 5\n  }, this);\n};\n\n// App wrapper with providers and routing\n_s2(MainApp, \"PJeBIQ96xxhOIiSnvXU2kV52F0A=\", false, function () {\n  return [useAuth];\n});\n_c2 = MainApp;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(AuthProvider, {\n    children: /*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(MainApp, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 36\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/admin/login\",\n          element: /*#__PURE__*/_jsxDEV(SessionGuard, {\n            requiredSessionType: \"admin\",\n            children: /*#__PURE__*/_jsxDEV(AdminLogin, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/admin/dashboard\",\n          element: /*#__PURE__*/_jsxDEV(SessionGuard, {\n            requiredSessionType: \"admin\",\n            children: /*#__PURE__*/_jsxDEV(ProtectedAdminRoute, {\n              children: /*#__PURE__*/_jsxDEV(AdminDashboard, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/admin/upload\",\n          element: /*#__PURE__*/_jsxDEV(SessionGuard, {\n            requiredSessionType: \"admin\",\n            children: /*#__PURE__*/_jsxDEV(ProtectedAdminRoute, {\n              children: /*#__PURE__*/_jsxDEV(AdminUpload, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/admin\",\n          element: /*#__PURE__*/_jsxDEV(Navigate, {\n            to: \"/admin/dashboard\",\n            replace: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 314,\n            columnNumber: 22\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 312,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 277,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 276,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 275,\n    columnNumber: 5\n  }, this);\n}\n_c3 = App;\nexport default App;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"ProtectedAdminRoute\");\n$RefreshReg$(_c2, \"MainApp\");\n$RefreshReg$(_c3, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "ChatWindow", "ChatSidebar", "TelegramButton", "ThemeToggle", "<PERSON><PERSON>", "AdminLogin", "AdminDashboard", "AdminUpload", "<PERSON><PERSON><PERSON>", "ThemeProvider", "<PERSON>th<PERSON><PERSON><PERSON>", "useAuth", "getSessionMessages", "jsxDEV", "_jsxDEV", "ProtectedAdminRoute", "children", "_s", "isAdminAuthenticated", "loading", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "replace", "_c", "MainApp", "_s2", "user", "currentSessionId", "setCurrentSessionId", "isSidebarOpen", "setIsSidebarOpen", "messages", "setMessages", "id", "text", "isBot", "timestamp", "Date", "is_greeting", "console", "log", "email", "window", "innerWidth", "handleSessionSelect", "sessionId", "sessionMessages", "convertedMessages", "map", "msg", "content", "message_type", "sources", "sources_count", "length", "summary", "suggestions", "error", "alert", "handleNewChat", "toggleSidebar", "requiredSessionType", "onClick", "onSessionSelect", "onNewChat", "onClose", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "src", "alt", "_c2", "App", "path", "element", "_c3", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/website kp/chatbot-unand/frontend/src/App.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\nimport {\n  BrowserRouter as Router,\n  Routes,\n  Route,\n  Navigate,\n} from \"react-router-dom\";\nimport ChatWindow from \"./ChatWindow\";\nimport ChatSidebar from \"./ChatSidebar\";\nimport TelegramButton from \"./TelegramButton\";\nimport ThemeToggle from \"./components/ThemeToggle\";\nimport Login from \"./components/Login\";\nimport AdminLogin from \"./components/AdminLogin\";\nimport AdminDashboard from \"./components/AdminDashboard\";\nimport AdminUpload from \"./components/AdminUpload\";\nimport SessionGuard from \"./components/SessionGuard\";\nimport { ThemeProvider } from \"./contexts/ThemeContext\";\nimport { AuthProvider, useAuth } from \"./contexts/AuthContext\";\nimport { getSessionMessages } from \"./api\";\nimport \"./index.css\"; // Pastikan Tailwind CSS diimpor\n\n// Protected Admin Route Component\nconst ProtectedAdminRoute = ({ children }) => {\n  const { isAdminAuthenticated, loading } = useAuth();\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-r from-green-50 to-yellow-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600\">Memuat...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return isAdminAuthenticated ? (\n    children\n  ) : (\n    <Navigate to=\"/admin/login\" replace />\n  );\n};\n\n// Main App Component with Authentication\nconst MainApp = () => {\n  const { user, loading } = useAuth();\n  const [currentSessionId, setCurrentSessionId] = useState(null);\n  const [isSidebarOpen, setIsSidebarOpen] = useState(false);\n  const [messages, setMessages] = useState([\n    {\n      id: 1,\n      text: \"Halo! Saya adalah Chatbot UNAND. Saya siap membantu Anda dengan pertanyaan seputar peraturan kampus dan pemerintah. Silakan ajukan pertanyaan Anda!\",\n      isBot: true,\n      timestamp: new Date(),\n      is_greeting: true,\n    },\n  ]);\n\n  // Reset state when user changes (login/logout)\n  useEffect(() => {\n    console.log(\"User changed:\", user?.email || \"logged out\");\n\n    // Reset to initial state when user changes\n    setCurrentSessionId(null);\n    setMessages([\n      {\n        id: 1,\n        text: \"Halo! Saya adalah Chatbot UNAND. Saya siap membantu Anda dengan pertanyaan seputar peraturan kampus dan pemerintah. Silakan ajukan pertanyaan Anda!\",\n        isBot: true,\n        timestamp: new Date(),\n        is_greeting: true,\n      },\n    ]);\n\n    // Close sidebar on mobile\n    if (window.innerWidth < 1024) {\n      setIsSidebarOpen(false);\n    }\n  }, [user]);\n\n  const handleSessionSelect = async (sessionId) => {\n    try {\n      setCurrentSessionId(sessionId);\n      const sessionMessages = await getSessionMessages(sessionId);\n\n      // Convert API messages to frontend format\n      const convertedMessages = sessionMessages.map((msg) => ({\n        id: msg.id,\n        text: msg.content,\n        isBot: msg.message_type === \"bot\",\n        timestamp: new Date(msg.timestamp),\n        sources: msg.sources,\n        sources_count: msg.sources ? msg.sources.length : 0,\n        summary: msg.summary,\n        suggestions: msg.suggestions,\n      }));\n\n      setMessages(convertedMessages);\n      // Close sidebar on mobile after selecting session\n      if (window.innerWidth < 1024) {\n        setIsSidebarOpen(false);\n      }\n    } catch (error) {\n      console.error(\"Error loading session messages:\", error);\n      alert(\"Gagal memuat riwayat percakapan\");\n    }\n  };\n\n  const handleNewChat = () => {\n    setCurrentSessionId(null);\n    setMessages([\n      {\n        id: 1,\n        text: \"Halo! Saya adalah Chatbot UNAND. Saya siap membantu Anda dengan pertanyaan seputar peraturan kampus dan pemerintah. Silakan ajukan pertanyaan Anda!\",\n        isBot: true,\n        timestamp: new Date(),\n        is_greeting: true,\n      },\n    ]);\n    // Close sidebar on mobile after new chat\n    if (window.innerWidth < 1024) {\n      setIsSidebarOpen(false);\n    }\n  };\n\n  const toggleSidebar = () => {\n    setIsSidebarOpen(!isSidebarOpen);\n  };\n\n  // Handle loading state\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-r from-green-50 to-yellow-50 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600 dark:text-gray-400\">Memuat...</p>\n        </div>\n      </div>\n    );\n  }\n\n  // Handle unauthenticated state\n  if (!user) {\n    return <Login />;\n  }\n\n  return (\n    <SessionGuard requiredSessionType=\"user\">\n      <ThemeProvider>\n        <div className=\"h-screen bg-gradient-to-r from-green-50 to-yellow-50 dark:from-gray-900 dark:to-gray-800 flex overflow-hidden relative\">\n          {/* Mobile Overlay */}\n          {isSidebarOpen && (\n            <div\n              className=\"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden\"\n              onClick={() => setIsSidebarOpen(false)}\n            />\n          )}\n\n          {/* Sidebar */}\n          <div\n            className={`fixed lg:static lg:translate-x-0 transition-transform duration-300 ease-in-out z-50 w-80 ${\n              isSidebarOpen ? \"translate-x-0\" : \"-translate-x-full\"\n            }`}\n          >\n            <ChatSidebar\n              currentSessionId={currentSessionId}\n              onSessionSelect={handleSessionSelect}\n              onNewChat={handleNewChat}\n              onClose={() => setIsSidebarOpen(false)}\n            />\n          </div>\n\n          {/* Main Content Area */}\n          <div className=\"flex-1 flex flex-col min-w-0\">\n            {/* Fixed Header */}\n            <div className=\"border-b-2 border-green-600 dark:border-green-400 p-3 lg:p-6 shadow-lg flex-shrink-0 bg-transparent\">\n              {/* Mobile Header */}\n              <div className=\"lg:hidden grid grid-cols-3 items-center mb-2\">\n                {/* Left: Menu Button */}\n                <div className=\"flex justify-start\">\n                  <button\n                    onClick={toggleSidebar}\n                    className=\"p-2 rounded-lg bg-green-600 dark:bg-green-500 text-white hover:bg-green-700 dark:hover:bg-green-600 transition-colors\"\n                  >\n                    <svg\n                      className=\"w-6 h-6\"\n                      fill=\"none\"\n                      stroke=\"currentColor\"\n                      viewBox=\"0 0 24 24\"\n                    >\n                      <path\n                        strokeLinecap=\"round\"\n                        strokeLinejoin=\"round\"\n                        strokeWidth={2}\n                        d=\"M4 6h16M4 12h16M4 18h16\"\n                      />\n                    </svg>\n                  </button>\n                </div>\n\n                {/* Center: Logo and Title */}\n                <div className=\"flex items-center justify-center gap-2\">\n                  <img\n                    src=\"/lambang-unand.jpg\"\n                    alt=\"Logo UNAND\"\n                    className=\"w-8 h-8 object-contain rounded border border-green-600 dark:border-green-400\"\n                  />\n                  <h1 className=\"text-lg font-bold text-green-700 dark:text-green-300\">\n                    TANYO UNAND\n                  </h1>\n                </div>\n\n                {/* Right: Theme Toggle */}\n                <div className=\"flex justify-end items-center\">\n                  <ThemeToggle />\n                </div>\n              </div>\n\n              {/* Desktop Header */}\n              <div className=\"hidden lg:grid lg:grid-cols-3 items-center h-19 w-full\">\n                {/* Left Section: Logo and Title */}\n                <div className=\"flex items-center gap-4\">\n                  <div className=\"flex-shrink-0\">\n                    <img\n                      src=\"/lambang-unand.jpg\"\n                      alt=\"Logo Universitas Andalas\"\n                      className=\"w-14 h-14 object-contain rounded-lg shadow-md border-2 border-green-600 dark:border-green-400\"\n                    />\n                  </div>\n                  <div className=\"flex flex-col justify-center\">\n                    <h1 className=\"text-xl font-bold text-green-700 dark:text-green-300 leading-tight\">\n                      TANYO UNAND\n                    </h1>\n                    <p className=\"text-green-600 dark:text-green-400 text-sm font-medium leading-tight\">\n                      Tampaik batanyo Seputar Universitas Andalas\n                    </p>\n                    <p className=\"text-yellow-700 dark:text-yellow-400 text-xs font-bold tracking-wider uppercase mt-1\">\n                      \"UNTUK KEDJAJAAN BANGSA\"\n                    </p>\n                  </div>\n                </div>\n\n                {/* Center Section: Empty for balance */}\n                <div className=\"flex items-center justify-center\"></div>\n\n                {/* Right Section: Theme Toggle */}\n                <div className=\"flex items-center justify-end\">\n                  <ThemeToggle />\n                </div>\n              </div>\n            </div>\n\n            {/* Chat Window */}\n            <div className=\"flex-1 min-h-0\">\n              <ChatWindow\n                messages={messages}\n                setMessages={setMessages}\n                currentSessionId={currentSessionId}\n                setCurrentSessionId={setCurrentSessionId}\n              />\n            </div>\n          </div>\n\n          {/* Telegram Button */}\n          <TelegramButton />\n        </div>\n      </ThemeProvider>\n    </SessionGuard>\n  );\n};\n\n// App wrapper with providers and routing\nfunction App() {\n  return (\n    <AuthProvider>\n      <Router>\n        <Routes>\n          {/* Main chatbot routes */}\n          <Route path=\"/\" element={<MainApp />} />\n\n          {/* Admin routes */}\n          <Route\n            path=\"/admin/login\"\n            element={\n              <SessionGuard requiredSessionType=\"admin\">\n                <AdminLogin />\n              </SessionGuard>\n            }\n          />\n          <Route\n            path=\"/admin/dashboard\"\n            element={\n              <SessionGuard requiredSessionType=\"admin\">\n                <ProtectedAdminRoute>\n                  <AdminDashboard />\n                </ProtectedAdminRoute>\n              </SessionGuard>\n            }\n          />\n          <Route\n            path=\"/admin/upload\"\n            element={\n              <SessionGuard requiredSessionType=\"admin\">\n                <ProtectedAdminRoute>\n                  <AdminUpload />\n                </ProtectedAdminRoute>\n              </SessionGuard>\n            }\n          />\n\n          {/* Redirect /admin to /admin/dashboard */}\n          <Route\n            path=\"/admin\"\n            element={<Navigate to=\"/admin/dashboard\" replace />}\n          />\n        </Routes>\n      </Router>\n    </AuthProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,aAAa,IAAIC,MAAM,EACvBC,MAAM,EACNC,KAAK,EACLC,QAAQ,QACH,kBAAkB;AACzB,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,KAAK,MAAM,oBAAoB;AACtC,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,YAAY,MAAM,2BAA2B;AACpD,SAASC,aAAa,QAAQ,yBAAyB;AACvD,SAASC,YAAY,EAAEC,OAAO,QAAQ,wBAAwB;AAC9D,SAASC,kBAAkB,QAAQ,OAAO;AAC1C,OAAO,aAAa,CAAC,CAAC;;AAEtB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,mBAAmB,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC5C,MAAM;IAAEC,oBAAoB;IAAEC;EAAQ,CAAC,GAAGR,OAAO,CAAC,CAAC;EAEnD,IAAIQ,OAAO,EAAE;IACX,oBACEL,OAAA;MAAKM,SAAS,EAAC,2FAA2F;MAAAJ,QAAA,eACxGF,OAAA;QAAKM,SAAS,EAAC,aAAa;QAAAJ,QAAA,gBAC1BF,OAAA;UAAKM,SAAS,EAAC;QAA8E;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACpGV,OAAA;UAAGM,SAAS,EAAC,eAAe;UAAAJ,QAAA,EAAC;QAAS;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,OAAON,oBAAoB,GACzBF,QAAQ,gBAERF,OAAA,CAACf,QAAQ;IAAC0B,EAAE,EAAC,cAAc;IAACC,OAAO;EAAA;IAAAL,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CACtC;AACH,CAAC;;AAED;AAAAP,EAAA,CArBMF,mBAAmB;EAAA,QACmBJ,OAAO;AAAA;AAAAgB,EAAA,GAD7CZ,mBAAmB;AAsBzB,MAAMa,OAAO,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACpB,MAAM;IAAEC,IAAI;IAAEX;EAAQ,CAAC,GAAGR,OAAO,CAAC,CAAC;EACnC,MAAM,CAACoB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACwC,aAAa,EAAEC,gBAAgB,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC0C,QAAQ,EAAEC,WAAW,CAAC,GAAG3C,QAAQ,CAAC,CACvC;IACE4C,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,qJAAqJ;IAC3JC,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;IACrBC,WAAW,EAAE;EACf,CAAC,CACF,CAAC;;EAEF;EACAhD,SAAS,CAAC,MAAM;IACdiD,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,CAAAd,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEe,KAAK,KAAI,YAAY,CAAC;;IAEzD;IACAb,mBAAmB,CAAC,IAAI,CAAC;IACzBI,WAAW,CAAC,CACV;MACEC,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,qJAAqJ;MAC3JC,KAAK,EAAE,IAAI;MACXC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;MACrBC,WAAW,EAAE;IACf,CAAC,CACF,CAAC;;IAEF;IACA,IAAII,MAAM,CAACC,UAAU,GAAG,IAAI,EAAE;MAC5Bb,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC,EAAE,CAACJ,IAAI,CAAC,CAAC;EAEV,MAAMkB,mBAAmB,GAAG,MAAOC,SAAS,IAAK;IAC/C,IAAI;MACFjB,mBAAmB,CAACiB,SAAS,CAAC;MAC9B,MAAMC,eAAe,GAAG,MAAMtC,kBAAkB,CAACqC,SAAS,CAAC;;MAE3D;MACA,MAAME,iBAAiB,GAAGD,eAAe,CAACE,GAAG,CAAEC,GAAG,KAAM;QACtDhB,EAAE,EAAEgB,GAAG,CAAChB,EAAE;QACVC,IAAI,EAAEe,GAAG,CAACC,OAAO;QACjBf,KAAK,EAAEc,GAAG,CAACE,YAAY,KAAK,KAAK;QACjCf,SAAS,EAAE,IAAIC,IAAI,CAACY,GAAG,CAACb,SAAS,CAAC;QAClCgB,OAAO,EAAEH,GAAG,CAACG,OAAO;QACpBC,aAAa,EAAEJ,GAAG,CAACG,OAAO,GAAGH,GAAG,CAACG,OAAO,CAACE,MAAM,GAAG,CAAC;QACnDC,OAAO,EAAEN,GAAG,CAACM,OAAO;QACpBC,WAAW,EAAEP,GAAG,CAACO;MACnB,CAAC,CAAC,CAAC;MAEHxB,WAAW,CAACe,iBAAiB,CAAC;MAC9B;MACA,IAAIL,MAAM,CAACC,UAAU,GAAG,IAAI,EAAE;QAC5Bb,gBAAgB,CAAC,KAAK,CAAC;MACzB;IACF,CAAC,CAAC,OAAO2B,KAAK,EAAE;MACdlB,OAAO,CAACkB,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvDC,KAAK,CAAC,iCAAiC,CAAC;IAC1C;EACF,CAAC;EAED,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1B/B,mBAAmB,CAAC,IAAI,CAAC;IACzBI,WAAW,CAAC,CACV;MACEC,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,qJAAqJ;MAC3JC,KAAK,EAAE,IAAI;MACXC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;MACrBC,WAAW,EAAE;IACf,CAAC,CACF,CAAC;IACF;IACA,IAAII,MAAM,CAACC,UAAU,GAAG,IAAI,EAAE;MAC5Bb,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;EAED,MAAM8B,aAAa,GAAGA,CAAA,KAAM;IAC1B9B,gBAAgB,CAAC,CAACD,aAAa,CAAC;EAClC,CAAC;;EAED;EACA,IAAId,OAAO,EAAE;IACX,oBACEL,OAAA;MAAKM,SAAS,EAAC,+HAA+H;MAAAJ,QAAA,eAC5IF,OAAA;QAAKM,SAAS,EAAC,aAAa;QAAAJ,QAAA,gBAC1BF,OAAA;UAAKM,SAAS,EAAC;QAA8E;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACpGV,OAAA;UAAGM,SAAS,EAAC,kCAAkC;UAAAJ,QAAA,EAAC;QAAS;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAI,CAACM,IAAI,EAAE;IACT,oBAAOhB,OAAA,CAACV,KAAK;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAClB;EAEA,oBACEV,OAAA,CAACN,YAAY;IAACyD,mBAAmB,EAAC,MAAM;IAAAjD,QAAA,eACtCF,OAAA,CAACL,aAAa;MAAAO,QAAA,eACZF,OAAA;QAAKM,SAAS,EAAC,wHAAwH;QAAAJ,QAAA,GAEpIiB,aAAa,iBACZnB,OAAA;UACEM,SAAS,EAAC,qDAAqD;UAC/D8C,OAAO,EAAEA,CAAA,KAAMhC,gBAAgB,CAAC,KAAK;QAAE;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CACF,eAGDV,OAAA;UACEM,SAAS,EAAE,4FACTa,aAAa,GAAG,eAAe,GAAG,mBAAmB,EACpD;UAAAjB,QAAA,eAEHF,OAAA,CAACb,WAAW;YACV8B,gBAAgB,EAAEA,gBAAiB;YACnCoC,eAAe,EAAEnB,mBAAoB;YACrCoB,SAAS,EAAEL,aAAc;YACzBM,OAAO,EAAEA,CAAA,KAAMnC,gBAAgB,CAAC,KAAK;UAAE;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNV,OAAA;UAAKM,SAAS,EAAC,8BAA8B;UAAAJ,QAAA,gBAE3CF,OAAA;YAAKM,SAAS,EAAC,qGAAqG;YAAAJ,QAAA,gBAElHF,OAAA;cAAKM,SAAS,EAAC,8CAA8C;cAAAJ,QAAA,gBAE3DF,OAAA;gBAAKM,SAAS,EAAC,oBAAoB;gBAAAJ,QAAA,eACjCF,OAAA;kBACEoD,OAAO,EAAEF,aAAc;kBACvB5C,SAAS,EAAC,uHAAuH;kBAAAJ,QAAA,eAEjIF,OAAA;oBACEM,SAAS,EAAC,SAAS;oBACnBkD,IAAI,EAAC,MAAM;oBACXC,MAAM,EAAC,cAAc;oBACrBC,OAAO,EAAC,WAAW;oBAAAxD,QAAA,eAEnBF,OAAA;sBACE2D,aAAa,EAAC,OAAO;sBACrBC,cAAc,EAAC,OAAO;sBACtBC,WAAW,EAAE,CAAE;sBACfC,CAAC,EAAC;oBAAyB;sBAAAvD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAGNV,OAAA;gBAAKM,SAAS,EAAC,wCAAwC;gBAAAJ,QAAA,gBACrDF,OAAA;kBACE+D,GAAG,EAAC,oBAAoB;kBACxBC,GAAG,EAAC,YAAY;kBAChB1D,SAAS,EAAC;gBAA8E;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzF,CAAC,eACFV,OAAA;kBAAIM,SAAS,EAAC,sDAAsD;kBAAAJ,QAAA,EAAC;gBAErE;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eAGNV,OAAA;gBAAKM,SAAS,EAAC,+BAA+B;gBAAAJ,QAAA,eAC5CF,OAAA,CAACX,WAAW;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNV,OAAA;cAAKM,SAAS,EAAC,wDAAwD;cAAAJ,QAAA,gBAErEF,OAAA;gBAAKM,SAAS,EAAC,yBAAyB;gBAAAJ,QAAA,gBACtCF,OAAA;kBAAKM,SAAS,EAAC,eAAe;kBAAAJ,QAAA,eAC5BF,OAAA;oBACE+D,GAAG,EAAC,oBAAoB;oBACxBC,GAAG,EAAC,0BAA0B;oBAC9B1D,SAAS,EAAC;kBAA+F;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1G;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNV,OAAA;kBAAKM,SAAS,EAAC,8BAA8B;kBAAAJ,QAAA,gBAC3CF,OAAA;oBAAIM,SAAS,EAAC,oEAAoE;oBAAAJ,QAAA,EAAC;kBAEnF;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACLV,OAAA;oBAAGM,SAAS,EAAC,sEAAsE;oBAAAJ,QAAA,EAAC;kBAEpF;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACJV,OAAA;oBAAGM,SAAS,EAAC,sFAAsF;oBAAAJ,QAAA,EAAC;kBAEpG;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNV,OAAA;gBAAKM,SAAS,EAAC;cAAkC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAGxDV,OAAA;gBAAKM,SAAS,EAAC,+BAA+B;gBAAAJ,QAAA,eAC5CF,OAAA,CAACX,WAAW;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNV,OAAA;YAAKM,SAAS,EAAC,gBAAgB;YAAAJ,QAAA,eAC7BF,OAAA,CAACd,UAAU;cACTmC,QAAQ,EAAEA,QAAS;cACnBC,WAAW,EAAEA,WAAY;cACzBL,gBAAgB,EAAEA,gBAAiB;cACnCC,mBAAmB,EAAEA;YAAoB;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNV,OAAA,CAACZ,cAAc;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEnB,CAAC;;AAED;AAAAK,GAAA,CAnOMD,OAAO;EAAA,QACejB,OAAO;AAAA;AAAAoE,GAAA,GAD7BnD,OAAO;AAoOb,SAASoD,GAAGA,CAAA,EAAG;EACb,oBACElE,OAAA,CAACJ,YAAY;IAAAM,QAAA,eACXF,OAAA,CAAClB,MAAM;MAAAoB,QAAA,eACLF,OAAA,CAACjB,MAAM;QAAAmB,QAAA,gBAELF,OAAA,CAAChB,KAAK;UAACmF,IAAI,EAAC,GAAG;UAACC,OAAO,eAAEpE,OAAA,CAACc,OAAO;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAGxCV,OAAA,CAAChB,KAAK;UACJmF,IAAI,EAAC,cAAc;UACnBC,OAAO,eACLpE,OAAA,CAACN,YAAY;YAACyD,mBAAmB,EAAC,OAAO;YAAAjD,QAAA,eACvCF,OAAA,CAACT,UAAU;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QACf;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACFV,OAAA,CAAChB,KAAK;UACJmF,IAAI,EAAC,kBAAkB;UACvBC,OAAO,eACLpE,OAAA,CAACN,YAAY;YAACyD,mBAAmB,EAAC,OAAO;YAAAjD,QAAA,eACvCF,OAAA,CAACC,mBAAmB;cAAAC,QAAA,eAClBF,OAAA,CAACR,cAAc;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QACf;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACFV,OAAA,CAAChB,KAAK;UACJmF,IAAI,EAAC,eAAe;UACpBC,OAAO,eACLpE,OAAA,CAACN,YAAY;YAACyD,mBAAmB,EAAC,OAAO;YAAAjD,QAAA,eACvCF,OAAA,CAACC,mBAAmB;cAAAC,QAAA,eAClBF,OAAA,CAACP,WAAW;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QACf;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGFV,OAAA,CAAChB,KAAK;UACJmF,IAAI,EAAC,QAAQ;UACbC,OAAO,eAAEpE,OAAA,CAACf,QAAQ;YAAC0B,EAAE,EAAC,kBAAkB;YAACC,OAAO;UAAA;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEnB;AAAC2D,GAAA,GA/CQH,GAAG;AAiDZ,eAAeA,GAAG;AAAC,IAAArD,EAAA,EAAAoD,GAAA,EAAAI,GAAA;AAAAC,YAAA,CAAAzD,EAAA;AAAAyD,YAAA,CAAAL,GAAA;AAAAK,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}