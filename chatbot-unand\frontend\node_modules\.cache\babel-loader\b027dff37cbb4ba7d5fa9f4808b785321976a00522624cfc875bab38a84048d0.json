{"ast": null, "code": "// packages/react/use-layout-effect/src/use-layout-effect.tsx\nimport * as React from \"react\";\nvar useLayoutEffect2 = globalThis?.document ? React.useLayoutEffect : () => {};\nexport { useLayoutEffect2 as useLayoutEffect };", "map": {"version": 3, "names": ["React", "useLayoutEffect2", "globalThis", "document", "useLayoutEffect"], "sources": ["D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\node_modules\\@radix-ui\\react-use-layout-effect\\src\\use-layout-effect.tsx"], "sourcesContent": ["import * as React from 'react';\n\n/**\n * On the server, <PERSON>act emits a warning when calling `useLayoutEffect`.\n * This is because neither `useLayoutEffect` nor `useEffect` run on the server.\n * We use this safe version which suppresses the warning by replacing it with a noop on the server.\n *\n * See: https://reactjs.org/docs/hooks-reference.html#uselayouteffect\n */\nconst useLayoutEffect = globalThis?.document ? React.useLayoutEffect : () => {};\n\nexport { useLayoutEffect };\n"], "mappings": ";AAAA,YAAYA,KAAA,MAAW;AASvB,IAAMC,gBAAA,GAAkBC,UAAA,EAAYC,QAAA,GAAiBH,KAAA,CAAAI,eAAA,GAAkB,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}