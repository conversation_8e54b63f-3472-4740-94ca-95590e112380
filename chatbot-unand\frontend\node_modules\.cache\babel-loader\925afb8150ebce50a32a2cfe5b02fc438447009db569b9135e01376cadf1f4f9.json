{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website kp\\\\chatbot-unand\\\\frontend\\\\src\\\\ChatWindow.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from \"react\";\nimport Message from \"./Message\";\nimport ChatInput from \"./ChatInput\";\nimport { sendMessageToChatbot, uploadDocument } from \"./api\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ChatWindow = () => {\n  _s();\n  const [messages, setMessages] = useState([{\n    text: \"Halo! Saya chatbot informasi peraturan Unand. Ada pertanyaan tentang peraturan kampus atau pemerintah?\",\n    sender: \"bot\"\n  }]);\n  const [isLoading, setIsLoading] = useState(false);\n  const messagesEndRef = useRef(null);\n  const fileInputRef = useRef(null); // Ref for file input\n\n  const scrollToBottom = () => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: \"smooth\"\n    });\n  };\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n  const handleSendMessage = async text => {\n    const userMessage = {\n      text,\n      sender: \"user\"\n    };\n    setMessages(prevMessages => [...prevMessages, userMessage]);\n    setIsLoading(true);\n    try {\n      const botResponse = await sendMessageToChatbot(text);\n      setMessages(prevMessages => [...prevMessages, {\n        text: botResponse,\n        sender: \"bot\"\n      }]);\n    } catch (error) {\n      setMessages(prevMessages => [...prevMessages, {\n        text: `Terjadi kesalahan: ${error.message}`,\n        sender: \"bot\"\n      }]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleFileUpload = async event => {\n    const file = event.target.files[0];\n    if (!file) return;\n    if (!file.name.endsWith(\".docx\")) {\n      setMessages(prev => [...prev, {\n        text: \"Hanya file Word (.docx) yang diizinkan.\",\n        sender: \"bot\"\n      }]);\n      return;\n    }\n    setMessages(prevMessages => [...prevMessages, {\n      text: `Mengunggah file \"${file.name}\"...`,\n      sender: \"user\"\n    }]);\n    setIsLoading(true);\n    try {\n      const uploadMessage = await uploadDocument(file);\n      setMessages(prevMessages => [...prevMessages, {\n        text: uploadMessage,\n        sender: \"bot\"\n      }]);\n    } catch (error) {\n      setMessages(prevMessages => [...prevMessages, {\n        text: `Gagal mengunggah file: ${error.message}`,\n        sender: \"bot\"\n      }]);\n    } finally {\n      setIsLoading(false);\n      // Reset file input value so the same file can be uploaded again if needed\n      if (fileInputRef.current) {\n        fileInputRef.current.value = \"\";\n      }\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col h-screen bg-gray-50 font-sans\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"bg-gradient-to-r from-blue-600 to-blue-800 text-white p-4 shadow-md flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-xl font-bold\",\n        children: \"Chatbot Peraturan Unand\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"file-upload\",\n          className: \"bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-4 rounded-full text-sm cursor-pointer disabled:opacity-50\",\n          children: isLoading ? \"Mengunggah...\" : \"Unggah Dokumen Baru\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          id: \"file-upload\",\n          type: \"file\",\n          ref: fileInputRef,\n          className: \"hidden\",\n          onChange: handleFileUpload,\n          disabled: isLoading,\n          accept: \".docx\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 86,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 overflow-y-auto p-4 space-y-2\",\n      children: [messages.map((msg, index) => /*#__PURE__*/_jsxDEV(Message, {\n        text: msg.text,\n        sender: msg.sender\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 11\n      }, this)), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-start mb-2\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gray-200 text-gray-800 rounded-lg py-2 px-4 max-w-[70%] shadow animate-pulse\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Mengetik...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: messagesEndRef\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ChatInput, {\n      onSendMessage: handleSendMessage,\n      isLoading: isLoading\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 85,\n    columnNumber: 5\n  }, this);\n};\n_s(ChatWindow, \"jWR1ONvBjajF17FaedvwZ06ZAfk=\");\n_c = ChatWindow;\nexport default ChatWindow;\nvar _c;\n$RefreshReg$(_c, \"ChatWindow\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Message", "ChatInput", "sendMessageToChatbot", "uploadDocument", "jsxDEV", "_jsxDEV", "ChatWindow", "_s", "messages", "setMessages", "text", "sender", "isLoading", "setIsLoading", "messagesEndRef", "fileInputRef", "scrollToBottom", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "handleSendMessage", "userMessage", "prevMessages", "botResponse", "error", "message", "handleFileUpload", "event", "file", "target", "files", "name", "endsWith", "prev", "uploadMessage", "value", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "htmlFor", "id", "type", "ref", "onChange", "disabled", "accept", "map", "msg", "index", "onSendMessage", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/website kp/chatbot-unand/frontend/src/ChatWindow.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from \"react\";\nimport Message from \"./Message\";\nimport ChatInput from \"./ChatInput\";\nimport { sendMessageToChatbot, uploadDocument } from \"./api\";\n\nconst ChatWindow = () => {\n  const [messages, setMessages] = useState([\n    {\n      text: \"Halo! Saya chatbot informasi peraturan Unand. Ada pertanyaan tentang peraturan kampus atau pemerintah?\",\n      sender: \"bot\",\n    },\n  ]);\n  const [isLoading, setIsLoading] = useState(false);\n  const messagesEndRef = useRef(null);\n  const fileInputRef = useRef(null); // Ref for file input\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: \"smooth\" });\n  };\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  const handleSendMessage = async (text) => {\n    const userMessage = { text, sender: \"user\" };\n    setMessages((prevMessages) => [...prevMessages, userMessage]);\n    setIsLoading(true);\n\n    try {\n      const botResponse = await sendMessageToChatbot(text);\n      setMessages((prevMessages) => [\n        ...prevMessages,\n        { text: botResponse, sender: \"bot\" },\n      ]);\n    } catch (error) {\n      setMessages((prevMessages) => [\n        ...prevMessages,\n        { text: `Terjadi kesalahan: ${error.message}`, sender: \"bot\" },\n      ]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleFileUpload = async (event) => {\n    const file = event.target.files[0];\n    if (!file) return;\n\n    if (!file.name.endsWith(\".docx\")) {\n      setMessages((prev) => [\n        ...prev,\n        { text: \"Hanya file Word (.docx) yang diizinkan.\", sender: \"bot\" },\n      ]);\n      return;\n    }\n\n    setMessages((prevMessages) => [\n      ...prevMessages,\n      { text: `Mengunggah file \"${file.name}\"...`, sender: \"user\" },\n    ]);\n    setIsLoading(true);\n\n    try {\n      const uploadMessage = await uploadDocument(file);\n      setMessages((prevMessages) => [\n        ...prevMessages,\n        { text: uploadMessage, sender: \"bot\" },\n      ]);\n    } catch (error) {\n      setMessages((prevMessages) => [\n        ...prevMessages,\n        { text: `Gagal mengunggah file: ${error.message}`, sender: \"bot\" },\n      ]);\n    } finally {\n      setIsLoading(false);\n      // Reset file input value so the same file can be uploaded again if needed\n      if (fileInputRef.current) {\n        fileInputRef.current.value = \"\";\n      }\n    }\n  };\n\n  return (\n    <div className=\"flex flex-col h-screen bg-gray-50 font-sans\">\n      <header className=\"bg-gradient-to-r from-blue-600 to-blue-800 text-white p-4 shadow-md flex justify-between items-center\">\n        <h1 className=\"text-xl font-bold\">Chatbot Peraturan Unand</h1>\n        <div className=\"flex items-center space-x-2\">\n          {/* Tombol untuk mengunggah file */}\n          <label\n            htmlFor=\"file-upload\"\n            className=\"bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-4 rounded-full text-sm cursor-pointer disabled:opacity-50\"\n          >\n            {isLoading ? \"Mengunggah...\" : \"Unggah Dokumen Baru\"}\n          </label>\n          <input\n            id=\"file-upload\"\n            type=\"file\"\n            ref={fileInputRef}\n            className=\"hidden\"\n            onChange={handleFileUpload}\n            disabled={isLoading}\n            accept=\".docx\"\n          />\n        </div>\n      </header>\n\n      <div className=\"flex-1 overflow-y-auto p-4 space-y-2\">\n        {messages.map((msg, index) => (\n          <Message key={index} text={msg.text} sender={msg.sender} />\n        ))}\n        {isLoading && (\n          <div className=\"flex justify-start mb-2\">\n            <div className=\"bg-gray-200 text-gray-800 rounded-lg py-2 px-4 max-w-[70%] shadow animate-pulse\">\n              <p>Mengetik...</p>\n            </div>\n          </div>\n        )}\n        <div ref={messagesEndRef} />\n      </div>\n\n      <ChatInput onSendMessage={handleSendMessage} isLoading={isLoading} />\n    </div>\n  );\n};\n\nexport default ChatWindow;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,SAAS,MAAM,aAAa;AACnC,SAASC,oBAAoB,EAAEC,cAAc,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7D,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGZ,QAAQ,CAAC,CACvC;IACEa,IAAI,EAAE,wGAAwG;IAC9GC,MAAM,EAAE;EACV,CAAC,CACF,CAAC;EACF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAMiB,cAAc,GAAGf,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMgB,YAAY,GAAGhB,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;;EAEnC,MAAMiB,cAAc,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAC3B,CAAAA,qBAAA,GAAAH,cAAc,CAACI,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC;EAEDtB,SAAS,CAAC,MAAM;IACdkB,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACR,QAAQ,CAAC,CAAC;EAEd,MAAMa,iBAAiB,GAAG,MAAOX,IAAI,IAAK;IACxC,MAAMY,WAAW,GAAG;MAAEZ,IAAI;MAAEC,MAAM,EAAE;IAAO,CAAC;IAC5CF,WAAW,CAAEc,YAAY,IAAK,CAAC,GAAGA,YAAY,EAAED,WAAW,CAAC,CAAC;IAC7DT,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACF,MAAMW,WAAW,GAAG,MAAMtB,oBAAoB,CAACQ,IAAI,CAAC;MACpDD,WAAW,CAAEc,YAAY,IAAK,CAC5B,GAAGA,YAAY,EACf;QAAEb,IAAI,EAAEc,WAAW;QAAEb,MAAM,EAAE;MAAM,CAAC,CACrC,CAAC;IACJ,CAAC,CAAC,OAAOc,KAAK,EAAE;MACdhB,WAAW,CAAEc,YAAY,IAAK,CAC5B,GAAGA,YAAY,EACf;QAAEb,IAAI,EAAE,sBAAsBe,KAAK,CAACC,OAAO,EAAE;QAAEf,MAAM,EAAE;MAAM,CAAC,CAC/D,CAAC;IACJ,CAAC,SAAS;MACRE,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMc,gBAAgB,GAAG,MAAOC,KAAK,IAAK;IACxC,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAI,CAACF,IAAI,EAAE;IAEX,IAAI,CAACA,IAAI,CAACG,IAAI,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;MAChCxB,WAAW,CAAEyB,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;QAAExB,IAAI,EAAE,yCAAyC;QAAEC,MAAM,EAAE;MAAM,CAAC,CACnE,CAAC;MACF;IACF;IAEAF,WAAW,CAAEc,YAAY,IAAK,CAC5B,GAAGA,YAAY,EACf;MAAEb,IAAI,EAAE,oBAAoBmB,IAAI,CAACG,IAAI,MAAM;MAAErB,MAAM,EAAE;IAAO,CAAC,CAC9D,CAAC;IACFE,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACF,MAAMsB,aAAa,GAAG,MAAMhC,cAAc,CAAC0B,IAAI,CAAC;MAChDpB,WAAW,CAAEc,YAAY,IAAK,CAC5B,GAAGA,YAAY,EACf;QAAEb,IAAI,EAAEyB,aAAa;QAAExB,MAAM,EAAE;MAAM,CAAC,CACvC,CAAC;IACJ,CAAC,CAAC,OAAOc,KAAK,EAAE;MACdhB,WAAW,CAAEc,YAAY,IAAK,CAC5B,GAAGA,YAAY,EACf;QAAEb,IAAI,EAAE,0BAA0Be,KAAK,CAACC,OAAO,EAAE;QAAEf,MAAM,EAAE;MAAM,CAAC,CACnE,CAAC;IACJ,CAAC,SAAS;MACRE,YAAY,CAAC,KAAK,CAAC;MACnB;MACA,IAAIE,YAAY,CAACG,OAAO,EAAE;QACxBH,YAAY,CAACG,OAAO,CAACkB,KAAK,GAAG,EAAE;MACjC;IACF;EACF,CAAC;EAED,oBACE/B,OAAA;IAAKgC,SAAS,EAAC,6CAA6C;IAAAC,QAAA,gBAC1DjC,OAAA;MAAQgC,SAAS,EAAC,uGAAuG;MAAAC,QAAA,gBACvHjC,OAAA;QAAIgC,SAAS,EAAC,mBAAmB;QAAAC,QAAA,EAAC;MAAuB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC9DrC,OAAA;QAAKgC,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAE1CjC,OAAA;UACEsC,OAAO,EAAC,aAAa;UACrBN,SAAS,EAAC,4HAA4H;UAAAC,QAAA,EAErI1B,SAAS,GAAG,eAAe,GAAG;QAAqB;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC,eACRrC,OAAA;UACEuC,EAAE,EAAC,aAAa;UAChBC,IAAI,EAAC,MAAM;UACXC,GAAG,EAAE/B,YAAa;UAClBsB,SAAS,EAAC,QAAQ;UAClBU,QAAQ,EAAEpB,gBAAiB;UAC3BqB,QAAQ,EAAEpC,SAAU;UACpBqC,MAAM,EAAC;QAAO;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAETrC,OAAA;MAAKgC,SAAS,EAAC,sCAAsC;MAAAC,QAAA,GAClD9B,QAAQ,CAAC0C,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,kBACvB/C,OAAA,CAACL,OAAO;QAAaU,IAAI,EAAEyC,GAAG,CAACzC,IAAK;QAACC,MAAM,EAAEwC,GAAG,CAACxC;MAAO,GAA1CyC,KAAK;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAuC,CAC3D,CAAC,EACD9B,SAAS,iBACRP,OAAA;QAAKgC,SAAS,EAAC,yBAAyB;QAAAC,QAAA,eACtCjC,OAAA;UAAKgC,SAAS,EAAC,iFAAiF;UAAAC,QAAA,eAC9FjC,OAAA;YAAAiC,QAAA,EAAG;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eACDrC,OAAA;QAAKyC,GAAG,EAAEhC;MAAe;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,eAENrC,OAAA,CAACJ,SAAS;MAACoD,aAAa,EAAEhC,iBAAkB;MAACT,SAAS,EAAEA;IAAU;MAAA2B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAClE,CAAC;AAEV,CAAC;AAACnC,EAAA,CAvHID,UAAU;AAAAgD,EAAA,GAAVhD,UAAU;AAyHhB,eAAeA,UAAU;AAAC,IAAAgD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}