{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m15 18-.722-3.25\",\n  key: \"1j64jw\"\n}], [\"path\", {\n  d: \"M2 8a10.645 10.645 0 0 0 20 0\",\n  key: \"1e7gxb\"\n}], [\"path\", {\n  d: \"m20 15-1.726-2.05\",\n  key: \"1cnuld\"\n}], [\"path\", {\n  d: \"m4 15 1.726-2.05\",\n  key: \"1dsqqd\"\n}], [\"path\", {\n  d: \"m9 18 .722-3.25\",\n  key: \"ypw2yx\"\n}]];\nconst EyeClosed = createLucideIcon(\"eye-closed\", __iconNode);\nexport { __iconNode, EyeClosed as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "EyeClosed", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\node_modules\\lucide-react\\src\\icons\\eye-closed.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm15 18-.722-3.25', key: '1j64jw' }],\n  ['path', { d: 'M2 8a10.645 10.645 0 0 0 20 0', key: '1e7gxb' }],\n  ['path', { d: 'm20 15-1.726-2.05', key: '1cnuld' }],\n  ['path', { d: 'm4 15 1.726-2.05', key: '1dsqqd' }],\n  ['path', { d: 'm9 18 .722-3.25', key: 'ypw2yx' }],\n];\n\n/**\n * @component @name EyeClosed\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTUgMTgtLjcyMi0zLjI1IiAvPgogIDxwYXRoIGQ9Ik0yIDhhMTAuNjQ1IDEwLjY0NSAwIDAgMCAyMCAwIiAvPgogIDxwYXRoIGQ9Im0yMCAxNS0xLjcyNi0yLjA1IiAvPgogIDxwYXRoIGQ9Im00IDE1IDEuNzI2LTIuMDUiIC8+CiAgPHBhdGggZD0ibTkgMTggLjcyMi0zLjI1IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/eye-closed\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst EyeClosed = createLucideIcon('eye-closed', __iconNode);\n\nexport default EyeClosed;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,kBAAoB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACjD,CAAC,MAAQ;EAAED,CAAA,EAAG,+BAAiC;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9D,CAAC,MAAQ;EAAED,CAAA,EAAG,mBAAqB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAClD,CAAC,MAAQ;EAAED,CAAA,EAAG,kBAAoB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACjD,CAAC,MAAQ;EAAED,CAAA,EAAG,iBAAmB;EAAAC,GAAA,EAAK;AAAU,GAClD;AAaM,MAAAC,SAAA,GAAYC,gBAAiB,eAAcJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}