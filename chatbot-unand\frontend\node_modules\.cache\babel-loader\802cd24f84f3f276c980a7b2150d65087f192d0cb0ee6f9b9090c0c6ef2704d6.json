{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website kp\\\\chatbot-unand\\\\frontend\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport ChatWindow from \"./ChatWindow\";\nimport ChatSidebar from \"./ChatSidebar\";\nimport { getSessionMessages } from \"./api\";\nimport \"./index.css\"; // Pastikan Tailwind CSS diimpor\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [currentSessionId, setCurrentSessionId] = useState(null);\n  const [messages, setMessages] = useState([{\n    id: 1,\n    text: \"Halo! Saya adalah Chatbot UNAND. Saya siap membantu Anda dengan pertanyaan seputar peraturan kampus dan pemerintah. Silakan ajukan pertanyaan Anda!\",\n    isBot: true,\n    timestamp: new Date()\n  }]);\n  const handleSessionSelect = async sessionId => {\n    try {\n      setCurrentSessionId(sessionId);\n      const sessionMessages = await getSessionMessages(sessionId);\n\n      // Convert API messages to frontend format\n      const convertedMessages = sessionMessages.map(msg => ({\n        id: msg.id,\n        text: msg.content,\n        isBot: msg.message_type === \"bot\",\n        timestamp: new Date(msg.timestamp),\n        sources: msg.sources\n      }));\n      setMessages(convertedMessages);\n    } catch (error) {\n      console.error(\"Error loading session messages:\", error);\n      alert(\"Gagal memuat riwayat percakapan\");\n    }\n  };\n  const handleNewChat = () => {\n    setCurrentSessionId(null);\n    setMessages([{\n      id: 1,\n      text: \"Halo! Saya adalah Chatbot UNAND. Saya siap membantu Anda dengan pertanyaan seputar peraturan kampus dan pemerintah. Silakan ajukan pertanyaan Anda!\",\n      isBot: true,\n      timestamp: new Date()\n    }]);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"h-screen bg-gray-100 flex overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed left-0 top-0 h-full z-10\",\n      children: /*#__PURE__*/_jsxDEV(ChatSidebar, {\n        currentSessionId: currentSessionId,\n        onSessionSelect: handleSessionSelect,\n        onNewChat: handleNewChat\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col ml-80\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed top-0 right-0 bg-white border-b border-gray-200 p-4 z-10 shadow-sm\",\n        style: {\n          left: \"320px\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-blue-600\",\n          children: \"Chatbot UNAND\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 text-sm\",\n          children: \"Asisten AI untuk pertanyaan seputar peraturan kampus dan pemerintah\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1\",\n        style: {\n          marginTop: \"96px\",\n          // Height of header (padding + text)\n          height: \"calc(100vh - 96px)\"\n        },\n        children: /*#__PURE__*/_jsxDEV(ChatWindow, {\n          messages: messages,\n          setMessages: setMessages,\n          currentSessionId: currentSessionId,\n          setCurrentSessionId: setCurrentSessionId\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 52,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"R1gwBKiSP0X6FWgCVSgx3kOYYAA=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "ChatWindow", "ChatSidebar", "getSessionMessages", "jsxDEV", "_jsxDEV", "App", "_s", "currentSessionId", "setCurrentSessionId", "messages", "setMessages", "id", "text", "isBot", "timestamp", "Date", "handleSessionSelect", "sessionId", "sessionMessages", "convertedMessages", "map", "msg", "content", "message_type", "sources", "error", "console", "alert", "handleNewChat", "className", "children", "onSessionSelect", "onNewChat", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "left", "marginTop", "height", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/website kp/chatbot-unand/frontend/src/App.js"], "sourcesContent": ["import React, { useState } from \"react\";\nimport ChatWindow from \"./ChatWindow\";\nimport ChatS<PERSON>bar from \"./ChatSidebar\";\nimport { getSessionMessages } from \"./api\";\nimport \"./index.css\"; // Pastikan Tailwind CSS diimpor\n\nfunction App() {\n  const [currentSessionId, setCurrentSessionId] = useState(null);\n  const [messages, setMessages] = useState([\n    {\n      id: 1,\n      text: \"Halo! Saya adalah Chatbot UNAND. Saya siap membantu Anda dengan pertanyaan seputar peraturan kampus dan pemerintah. Silakan ajukan pertanyaan Anda!\",\n      isBot: true,\n      timestamp: new Date(),\n    },\n  ]);\n\n  const handleSessionSelect = async (sessionId) => {\n    try {\n      setCurrentSessionId(sessionId);\n      const sessionMessages = await getSessionMessages(sessionId);\n\n      // Convert API messages to frontend format\n      const convertedMessages = sessionMessages.map((msg) => ({\n        id: msg.id,\n        text: msg.content,\n        isBot: msg.message_type === \"bot\",\n        timestamp: new Date(msg.timestamp),\n        sources: msg.sources,\n      }));\n\n      setMessages(convertedMessages);\n    } catch (error) {\n      console.error(\"Error loading session messages:\", error);\n      alert(\"Gagal memuat riwayat percakapan\");\n    }\n  };\n\n  const handleNewChat = () => {\n    setCurrentSessionId(null);\n    setMessages([\n      {\n        id: 1,\n        text: \"Halo! Saya adalah Chatbot UNAND. Saya siap membantu Anda dengan pertanyaan seputar peraturan kampus dan pemerintah. Silakan ajukan pertanyaan Anda!\",\n        isBot: true,\n        timestamp: new Date(),\n      },\n    ]);\n  };\n\n  return (\n    <div className=\"h-screen bg-gray-100 flex overflow-hidden\">\n      {/* Fixed Sidebar */}\n      <div className=\"fixed left-0 top-0 h-full z-10\">\n        <ChatSidebar\n          currentSessionId={currentSessionId}\n          onSessionSelect={handleSessionSelect}\n          onNewChat={handleNewChat}\n        />\n      </div>\n\n      {/* Main Content Area */}\n      <div className=\"flex-1 flex flex-col ml-80\">\n        {/* Fixed Header */}\n        <div\n          className=\"fixed top-0 right-0 bg-white border-b border-gray-200 p-4 z-10 shadow-sm\"\n          style={{ left: \"320px\" }}\n        >\n          <h1 className=\"text-2xl font-bold text-blue-600\">Chatbot UNAND</h1>\n          <p className=\"text-gray-600 text-sm\">\n            Asisten AI untuk pertanyaan seputar peraturan kampus dan pemerintah\n          </p>\n        </div>\n\n        {/* Chat Window with calculated height to avoid header overlap */}\n        <div\n          className=\"flex-1\"\n          style={{\n            marginTop: \"96px\", // Height of header (padding + text)\n            height: \"calc(100vh - 96px)\",\n          }}\n        >\n          <ChatWindow\n            messages={messages}\n            setMessages={setMessages}\n            currentSessionId={currentSessionId}\n            setCurrentSessionId={setCurrentSessionId}\n          />\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,kBAAkB,QAAQ,OAAO;AAC1C,OAAO,aAAa,CAAC,CAAC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEtB,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGT,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACU,QAAQ,EAAEC,WAAW,CAAC,GAAGX,QAAQ,CAAC,CACvC;IACEY,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,qJAAqJ;IAC3JC,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,IAAIC,IAAI,CAAC;EACtB,CAAC,CACF,CAAC;EAEF,MAAMC,mBAAmB,GAAG,MAAOC,SAAS,IAAK;IAC/C,IAAI;MACFT,mBAAmB,CAACS,SAAS,CAAC;MAC9B,MAAMC,eAAe,GAAG,MAAMhB,kBAAkB,CAACe,SAAS,CAAC;;MAE3D;MACA,MAAME,iBAAiB,GAAGD,eAAe,CAACE,GAAG,CAAEC,GAAG,KAAM;QACtDV,EAAE,EAAEU,GAAG,CAACV,EAAE;QACVC,IAAI,EAAES,GAAG,CAACC,OAAO;QACjBT,KAAK,EAAEQ,GAAG,CAACE,YAAY,KAAK,KAAK;QACjCT,SAAS,EAAE,IAAIC,IAAI,CAACM,GAAG,CAACP,SAAS,CAAC;QAClCU,OAAO,EAAEH,GAAG,CAACG;MACf,CAAC,CAAC,CAAC;MAEHd,WAAW,CAACS,iBAAiB,CAAC;IAChC,CAAC,CAAC,OAAOM,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvDE,KAAK,CAAC,iCAAiC,CAAC;IAC1C;EACF,CAAC;EAED,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1BpB,mBAAmB,CAAC,IAAI,CAAC;IACzBE,WAAW,CAAC,CACV;MACEC,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,qJAAqJ;MAC3JC,KAAK,EAAE,IAAI;MACXC,SAAS,EAAE,IAAIC,IAAI,CAAC;IACtB,CAAC,CACF,CAAC;EACJ,CAAC;EAED,oBACEX,OAAA;IAAKyB,SAAS,EAAC,2CAA2C;IAAAC,QAAA,gBAExD1B,OAAA;MAAKyB,SAAS,EAAC,gCAAgC;MAAAC,QAAA,eAC7C1B,OAAA,CAACH,WAAW;QACVM,gBAAgB,EAAEA,gBAAiB;QACnCwB,eAAe,EAAEf,mBAAoB;QACrCgB,SAAS,EAAEJ;MAAc;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNhC,OAAA;MAAKyB,SAAS,EAAC,4BAA4B;MAAAC,QAAA,gBAEzC1B,OAAA;QACEyB,SAAS,EAAC,0EAA0E;QACpFQ,KAAK,EAAE;UAAEC,IAAI,EAAE;QAAQ,CAAE;QAAAR,QAAA,gBAEzB1B,OAAA;UAAIyB,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAa;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnEhC,OAAA;UAAGyB,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAErC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNhC,OAAA;QACEyB,SAAS,EAAC,QAAQ;QAClBQ,KAAK,EAAE;UACLE,SAAS,EAAE,MAAM;UAAE;UACnBC,MAAM,EAAE;QACV,CAAE;QAAAV,QAAA,eAEF1B,OAAA,CAACJ,UAAU;UACTS,QAAQ,EAAEA,QAAS;UACnBC,WAAW,EAAEA,WAAY;UACzBH,gBAAgB,EAAEA,gBAAiB;UACnCC,mBAAmB,EAAEA;QAAoB;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC9B,EAAA,CAtFQD,GAAG;AAAAoC,EAAA,GAAHpC,GAAG;AAwFZ,eAAeA,GAAG;AAAC,IAAAoC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}