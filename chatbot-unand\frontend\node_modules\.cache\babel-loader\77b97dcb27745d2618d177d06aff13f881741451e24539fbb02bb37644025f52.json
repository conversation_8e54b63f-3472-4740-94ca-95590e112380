{"ast": null, "code": "const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || \"http://localhost:8001\";\n\n// Helper function to get auth headers\nconst getAuthHeaders = () => {\n  const token = localStorage.getItem(\"access_token\");\n  const headers = {\n    \"Content-Type\": \"application/json\"\n  };\n  if (token) {\n    // Check if token is expired before using it\n    try {\n      const payload = JSON.parse(atob(token.split(\".\")[1]));\n      const currentTime = Math.floor(Date.now() / 1000);\n\n      // If token is expired, don't include it\n      if (payload.exp < currentTime) {\n        console.log(\"API: Token expired, not including in headers\");\n        localStorage.removeItem(\"access_token\");\n        localStorage.removeItem(\"user\");\n        return headers;\n      }\n      headers.Authorization = `Bearer ${token}`;\n    } catch (error) {\n      console.error(\"API: Error checking token:\", error);\n      // Remove invalid token\n      localStorage.removeItem(\"access_token\");\n      localStorage.removeItem(\"user\");\n    }\n  }\n  return headers;\n};\nexport const sendMessageToChatbot = async (query, sessionId = null) => {\n  try {\n    const response = await fetch(`${API_BASE_URL}/chat`, {\n      method: \"POST\",\n      headers: getAuthHeaders(),\n      body: JSON.stringify({\n        query,\n        session_id: sessionId\n      })\n    });\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.detail || \"Terjadi kesalahan pada server.\");\n    }\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error(\"Error sending message to chatbot:\", error);\n    throw error;\n  }\n};\nexport const uploadDocument = async file => {\n  try {\n    const formData = new FormData();\n    formData.append(\"file\", file); // 'file' harus cocok dengan nama parameter di FastAPI\n\n    const token = localStorage.getItem(\"access_token\");\n    const headers = {};\n    if (token) {\n      headers.Authorization = `Bearer ${token}`;\n    }\n    const response = await fetch(`${API_BASE_URL}/upload-document`, {\n      method: \"POST\",\n      headers: headers,\n      body: formData // FormData akan mengatur header Content-Type secara otomatis\n    });\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.detail || \"Gagal mengunggah dokumen.\");\n    }\n    const data = await response.json();\n    return data.message;\n  } catch (error) {\n    console.error(\"Error uploading document:\", error);\n    throw error; // Re-throw to be handled by the component\n  }\n};\n\n// Session management functions\nexport const createSession = async (title = null) => {\n  try {\n    const response = await fetch(`${API_BASE_URL}/sessions`, {\n      method: \"POST\",\n      headers: getAuthHeaders(),\n      body: JSON.stringify({\n        title\n      })\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error(\"Error creating session:\", error);\n    throw error;\n  }\n};\nexport const getSessions = async () => {\n  try {\n    const response = await fetch(`${API_BASE_URL}/sessions`, {\n      headers: getAuthHeaders()\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error(\"Error getting sessions:\", error);\n    throw error;\n  }\n};\nexport const getSessionMessages = async sessionId => {\n  try {\n    const response = await fetch(`${API_BASE_URL}/sessions/${sessionId}/messages`, {\n      headers: getAuthHeaders()\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error(\"Error getting session messages:\", error);\n    throw error;\n  }\n};\nexport const updateSession = async (sessionId, title) => {\n  try {\n    const response = await fetch(`${API_BASE_URL}/sessions/${sessionId}`, {\n      method: \"PUT\",\n      headers: getAuthHeaders(),\n      body: JSON.stringify({\n        title\n      })\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error(\"Error updating session:\", error);\n    throw error;\n  }\n};\nexport const deleteSession = async sessionId => {\n  try {\n    const response = await fetch(`${API_BASE_URL}/sessions/${sessionId}`, {\n      method: \"DELETE\",\n      headers: getAuthHeaders()\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    return await response.json();\n  } catch (error) {\n    console.error(\"Error deleting session:\", error);\n    throw error;\n  }\n};", "map": {"version": 3, "names": ["API_BASE_URL", "process", "env", "REACT_APP_API_BASE_URL", "getAuthHeaders", "token", "localStorage", "getItem", "headers", "payload", "JSON", "parse", "atob", "split", "currentTime", "Math", "floor", "Date", "now", "exp", "console", "log", "removeItem", "Authorization", "error", "sendMessageToChatbot", "query", "sessionId", "response", "fetch", "method", "body", "stringify", "session_id", "ok", "errorData", "json", "Error", "detail", "data", "uploadDocument", "file", "formData", "FormData", "append", "message", "createSession", "title", "status", "getSessions", "getSessionMessages", "updateSession", "deleteSession"], "sources": ["C:/Users/<USER>/Downloads/website kp/chatbot-unand/frontend/src/api.js"], "sourcesContent": ["const API_BASE_URL =\n  process.env.REACT_APP_API_BASE_URL || \"http://localhost:8001\";\n\n// Helper function to get auth headers\nconst getAuthHeaders = () => {\n  const token = localStorage.getItem(\"access_token\");\n  const headers = {\n    \"Content-Type\": \"application/json\",\n  };\n\n  if (token) {\n    // Check if token is expired before using it\n    try {\n      const payload = JSON.parse(atob(token.split(\".\")[1]));\n      const currentTime = Math.floor(Date.now() / 1000);\n\n      // If token is expired, don't include it\n      if (payload.exp < currentTime) {\n        console.log(\"API: Token expired, not including in headers\");\n        localStorage.removeItem(\"access_token\");\n        localStorage.removeItem(\"user\");\n        return headers;\n      }\n\n      headers.Authorization = `Bearer ${token}`;\n    } catch (error) {\n      console.error(\"API: Error checking token:\", error);\n      // Remove invalid token\n      localStorage.removeItem(\"access_token\");\n      localStorage.removeItem(\"user\");\n    }\n  }\n\n  return headers;\n};\n\nexport const sendMessageToChatbot = async (query, sessionId = null) => {\n  try {\n    const response = await fetch(`${API_BASE_URL}/chat`, {\n      method: \"POST\",\n      headers: getAuthHeaders(),\n      body: JSON.stringify({\n        query,\n        session_id: sessionId,\n      }),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.detail || \"Terjadi kesalahan pada server.\");\n    }\n\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error(\"Error sending message to chatbot:\", error);\n    throw error;\n  }\n};\n\nexport const uploadDocument = async (file) => {\n  try {\n    const formData = new FormData();\n    formData.append(\"file\", file); // 'file' harus cocok dengan nama parameter di FastAPI\n\n    const token = localStorage.getItem(\"access_token\");\n    const headers = {};\n    if (token) {\n      headers.Authorization = `Bearer ${token}`;\n    }\n\n    const response = await fetch(`${API_BASE_URL}/upload-document`, {\n      method: \"POST\",\n      headers: headers,\n      body: formData, // FormData akan mengatur header Content-Type secara otomatis\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.detail || \"Gagal mengunggah dokumen.\");\n    }\n\n    const data = await response.json();\n    return data.message;\n  } catch (error) {\n    console.error(\"Error uploading document:\", error);\n    throw error; // Re-throw to be handled by the component\n  }\n};\n\n// Session management functions\nexport const createSession = async (title = null) => {\n  try {\n    const response = await fetch(`${API_BASE_URL}/sessions`, {\n      method: \"POST\",\n      headers: getAuthHeaders(),\n      body: JSON.stringify({ title }),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error(\"Error creating session:\", error);\n    throw error;\n  }\n};\n\nexport const getSessions = async () => {\n  try {\n    const response = await fetch(`${API_BASE_URL}/sessions`, {\n      headers: getAuthHeaders(),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error(\"Error getting sessions:\", error);\n    throw error;\n  }\n};\n\nexport const getSessionMessages = async (sessionId) => {\n  try {\n    const response = await fetch(\n      `${API_BASE_URL}/sessions/${sessionId}/messages`,\n      {\n        headers: getAuthHeaders(),\n      }\n    );\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error(\"Error getting session messages:\", error);\n    throw error;\n  }\n};\n\nexport const updateSession = async (sessionId, title) => {\n  try {\n    const response = await fetch(`${API_BASE_URL}/sessions/${sessionId}`, {\n      method: \"PUT\",\n      headers: getAuthHeaders(),\n      body: JSON.stringify({ title }),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error(\"Error updating session:\", error);\n    throw error;\n  }\n};\n\nexport const deleteSession = async (sessionId) => {\n  try {\n    const response = await fetch(`${API_BASE_URL}/sessions/${sessionId}`, {\n      method: \"DELETE\",\n      headers: getAuthHeaders(),\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error(\"Error deleting session:\", error);\n    throw error;\n  }\n};\n"], "mappings": "AAAA,MAAMA,YAAY,GAChBC,OAAO,CAACC,GAAG,CAACC,sBAAsB,IAAI,uBAAuB;;AAE/D;AACA,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAC3B,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;EAClD,MAAMC,OAAO,GAAG;IACd,cAAc,EAAE;EAClB,CAAC;EAED,IAAIH,KAAK,EAAE;IACT;IACA,IAAI;MACF,MAAMI,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACC,IAAI,CAACP,KAAK,CAACQ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrD,MAAMC,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;;MAEjD;MACA,IAAIT,OAAO,CAACU,GAAG,GAAGL,WAAW,EAAE;QAC7BM,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;QAC3Df,YAAY,CAACgB,UAAU,CAAC,cAAc,CAAC;QACvChB,YAAY,CAACgB,UAAU,CAAC,MAAM,CAAC;QAC/B,OAAOd,OAAO;MAChB;MAEAA,OAAO,CAACe,aAAa,GAAG,UAAUlB,KAAK,EAAE;IAC3C,CAAC,CAAC,OAAOmB,KAAK,EAAE;MACdJ,OAAO,CAACI,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD;MACAlB,YAAY,CAACgB,UAAU,CAAC,cAAc,CAAC;MACvChB,YAAY,CAACgB,UAAU,CAAC,MAAM,CAAC;IACjC;EACF;EAEA,OAAOd,OAAO;AAChB,CAAC;AAED,OAAO,MAAMiB,oBAAoB,GAAG,MAAAA,CAAOC,KAAK,EAAEC,SAAS,GAAG,IAAI,KAAK;EACrE,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG7B,YAAY,OAAO,EAAE;MACnD8B,MAAM,EAAE,MAAM;MACdtB,OAAO,EAAEJ,cAAc,CAAC,CAAC;MACzB2B,IAAI,EAAErB,IAAI,CAACsB,SAAS,CAAC;QACnBN,KAAK;QACLO,UAAU,EAAEN;MACd,CAAC;IACH,CAAC,CAAC;IAEF,IAAI,CAACC,QAAQ,CAACM,EAAE,EAAE;MAChB,MAAMC,SAAS,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;MACvC,MAAM,IAAIC,KAAK,CAACF,SAAS,CAACG,MAAM,IAAI,gCAAgC,CAAC;IACvE;IAEA,MAAMC,IAAI,GAAG,MAAMX,QAAQ,CAACQ,IAAI,CAAC,CAAC;IAClC,OAAOG,IAAI;EACb,CAAC,CAAC,OAAOf,KAAK,EAAE;IACdJ,OAAO,CAACI,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;IACzD,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMgB,cAAc,GAAG,MAAOC,IAAI,IAAK;EAC5C,IAAI;IACF,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEH,IAAI,CAAC,CAAC,CAAC;;IAE/B,MAAMpC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;IAClD,MAAMC,OAAO,GAAG,CAAC,CAAC;IAClB,IAAIH,KAAK,EAAE;MACTG,OAAO,CAACe,aAAa,GAAG,UAAUlB,KAAK,EAAE;IAC3C;IAEA,MAAMuB,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG7B,YAAY,kBAAkB,EAAE;MAC9D8B,MAAM,EAAE,MAAM;MACdtB,OAAO,EAAEA,OAAO;MAChBuB,IAAI,EAAEW,QAAQ,CAAE;IAClB,CAAC,CAAC;IAEF,IAAI,CAACd,QAAQ,CAACM,EAAE,EAAE;MAChB,MAAMC,SAAS,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;MACvC,MAAM,IAAIC,KAAK,CAACF,SAAS,CAACG,MAAM,IAAI,2BAA2B,CAAC;IAClE;IAEA,MAAMC,IAAI,GAAG,MAAMX,QAAQ,CAACQ,IAAI,CAAC,CAAC;IAClC,OAAOG,IAAI,CAACM,OAAO;EACrB,CAAC,CAAC,OAAOrB,KAAK,EAAE;IACdJ,OAAO,CAACI,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACjD,MAAMA,KAAK,CAAC,CAAC;EACf;AACF,CAAC;;AAED;AACA,OAAO,MAAMsB,aAAa,GAAG,MAAAA,CAAOC,KAAK,GAAG,IAAI,KAAK;EACnD,IAAI;IACF,MAAMnB,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG7B,YAAY,WAAW,EAAE;MACvD8B,MAAM,EAAE,MAAM;MACdtB,OAAO,EAAEJ,cAAc,CAAC,CAAC;MACzB2B,IAAI,EAAErB,IAAI,CAACsB,SAAS,CAAC;QAAEe;MAAM,CAAC;IAChC,CAAC,CAAC;IAEF,IAAI,CAACnB,QAAQ,CAACM,EAAE,EAAE;MAChB,MAAM,IAAIG,KAAK,CAAC,uBAAuBT,QAAQ,CAACoB,MAAM,EAAE,CAAC;IAC3D;IAEA,OAAO,MAAMpB,QAAQ,CAACQ,IAAI,CAAC,CAAC;EAC9B,CAAC,CAAC,OAAOZ,KAAK,EAAE;IACdJ,OAAO,CAACI,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IAC/C,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMyB,WAAW,GAAG,MAAAA,CAAA,KAAY;EACrC,IAAI;IACF,MAAMrB,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG7B,YAAY,WAAW,EAAE;MACvDQ,OAAO,EAAEJ,cAAc,CAAC;IAC1B,CAAC,CAAC;IAEF,IAAI,CAACwB,QAAQ,CAACM,EAAE,EAAE;MAChB,MAAM,IAAIG,KAAK,CAAC,uBAAuBT,QAAQ,CAACoB,MAAM,EAAE,CAAC;IAC3D;IAEA,OAAO,MAAMpB,QAAQ,CAACQ,IAAI,CAAC,CAAC;EAC9B,CAAC,CAAC,OAAOZ,KAAK,EAAE;IACdJ,OAAO,CAACI,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IAC/C,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAM0B,kBAAkB,GAAG,MAAOvB,SAAS,IAAK;EACrD,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAC1B,GAAG7B,YAAY,aAAa2B,SAAS,WAAW,EAChD;MACEnB,OAAO,EAAEJ,cAAc,CAAC;IAC1B,CACF,CAAC;IAED,IAAI,CAACwB,QAAQ,CAACM,EAAE,EAAE;MAChB,MAAM,IAAIG,KAAK,CAAC,uBAAuBT,QAAQ,CAACoB,MAAM,EAAE,CAAC;IAC3D;IAEA,OAAO,MAAMpB,QAAQ,CAACQ,IAAI,CAAC,CAAC;EAC9B,CAAC,CAAC,OAAOZ,KAAK,EAAE;IACdJ,OAAO,CAACI,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACvD,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAM2B,aAAa,GAAG,MAAAA,CAAOxB,SAAS,EAAEoB,KAAK,KAAK;EACvD,IAAI;IACF,MAAMnB,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG7B,YAAY,aAAa2B,SAAS,EAAE,EAAE;MACpEG,MAAM,EAAE,KAAK;MACbtB,OAAO,EAAEJ,cAAc,CAAC,CAAC;MACzB2B,IAAI,EAAErB,IAAI,CAACsB,SAAS,CAAC;QAAEe;MAAM,CAAC;IAChC,CAAC,CAAC;IAEF,IAAI,CAACnB,QAAQ,CAACM,EAAE,EAAE;MAChB,MAAM,IAAIG,KAAK,CAAC,uBAAuBT,QAAQ,CAACoB,MAAM,EAAE,CAAC;IAC3D;IAEA,OAAO,MAAMpB,QAAQ,CAACQ,IAAI,CAAC,CAAC;EAC9B,CAAC,CAAC,OAAOZ,KAAK,EAAE;IACdJ,OAAO,CAACI,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IAC/C,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAM4B,aAAa,GAAG,MAAOzB,SAAS,IAAK;EAChD,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG7B,YAAY,aAAa2B,SAAS,EAAE,EAAE;MACpEG,MAAM,EAAE,QAAQ;MAChBtB,OAAO,EAAEJ,cAAc,CAAC;IAC1B,CAAC,CAAC;IAEF,IAAI,CAACwB,QAAQ,CAACM,EAAE,EAAE;MAChB,MAAM,IAAIG,KAAK,CAAC,uBAAuBT,QAAQ,CAACoB,MAAM,EAAE,CAAC;IAC3D;IAEA,OAAO,MAAMpB,QAAQ,CAACQ,IAAI,CAAC,CAAC;EAC9B,CAAC,CAAC,OAAOZ,KAAK,EAAE;IACdJ,OAAO,CAACI,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IAC/C,MAAMA,KAAK;EACb;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}