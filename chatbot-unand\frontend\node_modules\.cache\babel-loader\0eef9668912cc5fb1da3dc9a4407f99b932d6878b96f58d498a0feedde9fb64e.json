{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M19 15V9\",\n  key: \"1hci5f\"\n}], [\"path\", {\n  d: \"M15 15h-3v4l-7-7 7-7v4h3v6z\",\n  key: \"16tjna\"\n}]];\nconst ArrowBigLeftDash = createLucideIcon(\"arrow-big-left-dash\", __iconNode);\nexport { __iconNode, ArrowBigLeftDash as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "ArrowBigLeftDash", "createLucideIcon"], "sources": ["D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\node_modules\\lucide-react\\src\\icons\\arrow-big-left-dash.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M19 15V9', key: '1hci5f' }],\n  ['path', { d: 'M15 15h-3v4l-7-7 7-7v4h3v6z', key: '16tjna' }],\n];\n\n/**\n * @component @name ArrowBigLeftDash\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTkgMTVWOSIgLz4KICA8cGF0aCBkPSJNMTUgMTVoLTN2NGwtNy03IDctN3Y0aDN2NnoiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/arrow-big-left-dash\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowBigLeftDash = createLucideIcon('arrow-big-left-dash', __iconNode);\n\nexport default ArrowBigLeftDash;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,6BAA+B;EAAAC,GAAA,EAAK;AAAU,GAC9D;AAaM,MAAAC,gBAAA,GAAmBC,gBAAiB,wBAAuBJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}