{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10.5 2v4\",\n  key: \"1xt6in\"\n}], [\"path\", {\n  d: \"M14 2H7a2 2 0 0 0-2 2\",\n  key: \"e6xig3\"\n}], [\"path\", {\n  d: \"M19.29 14.76A6.67 6.67 0 0 1 17 11a6.6 6.6 0 0 1-2.29 3.76c-1.15.92-1.71 2.04-1.71 3.19 0 2.22 1.8 4.05 4 4.05s4-1.83 4-4.05c0-1.16-.57-2.26-1.71-3.19\",\n  key: \"adq7uc\"\n}], [\"path\", {\n  d: \"M9.607 21H6a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h7V7a1 1 0 0 0-1-1H9a1 1 0 0 0-1 1v3\",\n  key: \"t9hm96\"\n}]];\nconst SoapDispenserDroplet = createLucideIcon(\"soap-dispenser-droplet\", __iconNode);\nexport { __iconNode, SoapDispenserDroplet as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "SoapDispenserDroplet", "createLucideIcon"], "sources": ["D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\node_modules\\lucide-react\\src\\icons\\soap-dispenser-droplet.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M10.5 2v4', key: '1xt6in' }],\n  ['path', { d: 'M14 2H7a2 2 0 0 0-2 2', key: 'e6xig3' }],\n  [\n    'path',\n    {\n      d: 'M19.29 14.76A6.67 6.67 0 0 1 17 11a6.6 6.6 0 0 1-2.29 3.76c-1.15.92-1.71 2.04-1.71 3.19 0 2.22 1.8 4.05 4 4.05s4-1.83 4-4.05c0-1.16-.57-2.26-1.71-3.19',\n      key: 'adq7uc',\n    },\n  ],\n  [\n    'path',\n    {\n      d: 'M9.607 21H6a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h7V7a1 1 0 0 0-1-1H9a1 1 0 0 0-1 1v3',\n      key: 't9hm96',\n    },\n  ],\n];\n\n/**\n * @component @name SoapDispenserDroplet\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAuNSAydjQiIC8+CiAgPHBhdGggZD0iTTE0IDJIN2EyIDIgMCAwIDAtMiAyIiAvPgogIDxwYXRoIGQ9Ik0xOS4yOSAxNC43NkE2LjY3IDYuNjcgMCAwIDEgMTcgMTFhNi42IDYuNiAwIDAgMS0yLjI5IDMuNzZjLTEuMTUuOTItMS43MSAyLjA0LTEuNzEgMy4xOSAwIDIuMjIgMS44IDQuMDUgNCA0LjA1czQtMS44MyA0LTQuMDVjMC0xLjE2LS41Ny0yLjI2LTEuNzEtMy4xOSIgLz4KICA8cGF0aCBkPSJNOS42MDcgMjFINmEyIDIgMCAwIDEtMi0ydi03YTIgMiAwIDAgMSAyLTJoN1Y3YTEgMSAwIDAgMC0xLTFIOWExIDEgMCAwIDAtMSAxdjMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/soap-dispenser-droplet\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst SoapDispenserDroplet = createLucideIcon('soap-dispenser-droplet', __iconNode);\n\nexport default SoapDispenserDroplet;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,uBAAyB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACtD,CACE,QACA;EACED,CAAG;EACHC,GAAK;AAAA,EAET,EACA,CACE,QACA;EACED,CAAG;EACHC,GAAK;AAAA,EACP,CAEJ;AAaM,MAAAC,oBAAA,GAAuBC,gBAAiB,2BAA0BJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}