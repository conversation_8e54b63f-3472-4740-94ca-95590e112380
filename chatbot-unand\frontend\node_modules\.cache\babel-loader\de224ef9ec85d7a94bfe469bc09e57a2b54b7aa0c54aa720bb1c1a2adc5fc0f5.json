{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website kp\\\\chatbot-unand\\\\frontend\\\\src\\\\components\\\\Login.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from \"react\";\nimport { useAuth } from \"../contexts/AuthContext\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Login = () => {\n  _s();\n  const {\n    login\n  } = useAuth();\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const googleButtonRef = useRef(null);\n  const handleCredentialResponse = async response => {\n    console.log(\"Google credential response:\", response);\n    setLoading(true);\n    setError(null);\n    try {\n      console.log(\"Attempting login with token...\");\n      await login(response.credential);\n      console.log(\"Login successful!\");\n    } catch (error) {\n      console.error(\"Login error details:\", error);\n      setError(`Login failed: ${error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Initialize Google Sign-In function\n  const initializeGoogleSignIn = () => {\n    if (window.google && window.google.accounts) {\n      console.log(\"✅ Initializing Google Sign-In...\");\n      console.log(\"🔑 Client ID:\", process.env.REACT_APP_GOOGLE_CLIENT_ID);\n      console.log(\"🌐 Current origin:\", window.location.origin);\n      console.log(\"📍 Button ref current:\", !!googleButtonRef.current);\n\n      // Clear any existing Google session first\n      try {\n        window.google.accounts.id.disableAutoSelect();\n        console.log(\"🧹 Cleared existing Google auto-select\");\n      } catch (e) {\n        console.log(\"ℹ️ No existing Google session to clear\");\n      }\n      try {\n        window.google.accounts.id.initialize({\n          client_id: process.env.REACT_APP_GOOGLE_CLIENT_ID,\n          callback: handleCredentialResponse,\n          auto_select: false,\n          cancel_on_tap_outside: true,\n          use_fedcm_for_prompt: false,\n          // Disable FedCM to avoid conflicts\n          itp_support: true // Enable Intelligent Tracking Prevention support\n        });\n        console.log(\"✅ Google Sign-In initialized successfully\");\n\n        // Render the Google Sign-In button\n        if (googleButtonRef.current) {\n          console.log(\"🎨 Rendering Google Sign-In button...\");\n          window.google.accounts.id.renderButton(googleButtonRef.current, {\n            theme: \"outline\",\n            size: \"large\",\n            width: \"100%\",\n            text: \"signin_with\",\n            locale: \"id\"\n          });\n          console.log(\"✅ Google Sign-In button rendered\");\n        } else {\n          console.error(\"❌ Button ref is null, cannot render button\");\n          setError(\"Tidak dapat menampilkan tombol login Google\");\n        }\n      } catch (error) {\n        console.error(\"❌ Error initializing Google Sign-In:\", error);\n        setError(\"Gagal menginisialisasi Google Sign-In: \" + error.message);\n      }\n    } else {\n      console.error(\"Google Identity Services not loaded\");\n      setError(\"Google Identity Services tidak dapat dimuat\");\n    }\n  };\n\n  // Function to load Google script manually if not loaded\n  const loadGoogleScript = () => {\n    return new Promise((resolve, reject) => {\n      if (window.google && window.google.accounts) {\n        resolve();\n        return;\n      }\n      const script = document.createElement(\"script\");\n      script.src = \"https://accounts.google.com/gsi/client\";\n      script.async = true;\n      script.defer = true;\n      script.onload = () => {\n        console.log(\"✅ Google script loaded manually\");\n        // Wait a bit for the script to initialize\n        setTimeout(() => {\n          if (window.google && window.google.accounts) {\n            resolve();\n          } else {\n            reject(new Error(\"Google script loaded but API not available\"));\n          }\n        }, 500);\n      };\n      script.onerror = () => {\n        reject(new Error(\"Failed to load Google script\"));\n      };\n      document.head.appendChild(script);\n    });\n  };\n  useEffect(() => {\n    // Initialize Google Sign-In when component mounts\n    console.log(\"🚀 Login component mounted, checking Google script...\");\n    console.log(\"🔑 Google Client ID from env:\", process.env.REACT_APP_GOOGLE_CLIENT_ID);\n    console.log(\"🌐 Window.google available:\", !!window.google);\n    console.log(\"🌐 Window.google.accounts available:\", !!(window.google && window.google.accounts));\n    const initializeGoogle = async () => {\n      try {\n        // First check if already loaded\n        if (window.google && window.google.accounts) {\n          console.log(\"✅ Google script already loaded\");\n          initializeGoogleSignIn();\n          return;\n        }\n\n        // Try to load manually\n        console.log(\"📥 Loading Google script manually...\");\n        await loadGoogleScript();\n        initializeGoogleSignIn();\n      } catch (error) {\n        console.error(\"❌ Failed to load Google script:\", error);\n        setError(\"Google Identity Services gagal dimuat. Silakan refresh halaman.\");\n      }\n    };\n    initializeGoogle();\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []); // Remove dependency to avoid infinite loop\n\n  const handleManualLogin = () => {\n    console.log(\"🔄 Manual login triggered\");\n    console.log(\"🌐 Google available:\", !!window.google);\n    console.log(\"🌐 Google accounts available:\", !!(window.google && window.google.accounts));\n    if (window.google && window.google.accounts) {\n      try {\n        console.log(\"🚀 Triggering Google prompt...\");\n        window.google.accounts.id.prompt(notification => {\n          console.log(\"📢 Google prompt notification:\", notification);\n          if (notification.isNotDisplayed() || notification.isSkippedMoment()) {\n            console.log(\"⚠️ Google prompt was not displayed or skipped\");\n            setError(\"Google login popup tidak muncul. Silakan coba lagi atau periksa popup blocker.\");\n          }\n        });\n      } catch (error) {\n        console.error(\"❌ Error triggering Google prompt:\", error);\n        setError(\"Gagal membuka Google login: \" + error.message);\n      }\n    } else {\n      console.error(\"❌ Google Identity Services not available\");\n      setError(\"Google Identity Services tidak tersedia. Silakan refresh halaman.\");\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-r from-green-50 to-yellow-50 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-md w-full mx-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white dark:bg-gray-800 rounded-lg shadow-xl p-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mx-auto w-20 h-20 bg-gradient-to-r from-green-600 to-yellow-500 rounded-full flex items-center justify-center mb-4\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-white text-2xl font-bold\",\n              children: \"U\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-2xl font-bold text-gray-900 dark:text-white mb-2\",\n            children: \"Chatbot UNAND\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 dark:text-gray-400 text-sm\",\n            children: \"UNTUK KEDJAJAAN BANGSA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-500 dark:text-gray-500 text-sm mt-2\",\n            children: \"Masuk untuk mengakses chatbot peraturan kampus\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4 p-3 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-lg\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-red-700 dark:text-red-300 text-sm\",\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4 p-3 bg-blue-50 dark:bg-blue-900 border border-blue-200 dark:border-blue-700 rounded-lg\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-blue-700 dark:text-blue-300 text-sm\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Informasi:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 15\n            }, this), \" Jika tombol Google Sign-In tidak muncul, gunakan tombol \\\"Masuk dengan Google\\\" di bawah ini.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: googleButtonRef,\n            className: \"w-full\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 11\n        }, this), loading && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center py-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-green-600 mr-3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-gray-600 dark:text-gray-400\",\n            children: \"Memproses login...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-6 text-center\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs text-gray-500 dark:text-gray-400\",\n            children: \"Dengan masuk, Anda menyetujui penggunaan data untuk keperluan chatbot\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 180,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"UedV2vSZT0PByJj7YeEKAQAKJcU=\", false, function () {\n  return [useAuth];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useAuth", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "_s", "login", "loading", "setLoading", "error", "setError", "googleButtonRef", "handleCredentialResponse", "response", "console", "log", "credential", "message", "initializeGoogleSignIn", "window", "google", "accounts", "process", "env", "REACT_APP_GOOGLE_CLIENT_ID", "location", "origin", "current", "id", "disableAutoSelect", "e", "initialize", "client_id", "callback", "auto_select", "cancel_on_tap_outside", "use_fedcm_for_prompt", "itp_support", "renderButton", "theme", "size", "width", "text", "locale", "loadGoogleScript", "Promise", "resolve", "reject", "script", "document", "createElement", "src", "async", "defer", "onload", "setTimeout", "Error", "onerror", "head", "append<PERSON><PERSON><PERSON>", "initializeGoogle", "handleManualLogin", "prompt", "notification", "isNotDisplayed", "isSkippedMoment", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ref", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/website kp/chatbot-unand/frontend/src/components/Login.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from \"react\";\nimport { useAuth } from \"../contexts/AuthContext\";\n\nconst Login = () => {\n  const { login } = useAuth();\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const googleButtonRef = useRef(null);\n\n  const handleCredentialResponse = async (response) => {\n    console.log(\"Google credential response:\", response);\n    setLoading(true);\n    setError(null);\n\n    try {\n      console.log(\"Attempting login with token...\");\n      await login(response.credential);\n      console.log(\"Login successful!\");\n    } catch (error) {\n      console.error(\"Login error details:\", error);\n      setError(`Login failed: ${error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Initialize Google Sign-In function\n  const initializeGoogleSignIn = () => {\n    if (window.google && window.google.accounts) {\n      console.log(\"✅ Initializing Google Sign-In...\");\n      console.log(\"🔑 Client ID:\", process.env.REACT_APP_GOOGLE_CLIENT_ID);\n      console.log(\"🌐 Current origin:\", window.location.origin);\n      console.log(\"📍 Button ref current:\", !!googleButtonRef.current);\n\n      // Clear any existing Google session first\n      try {\n        window.google.accounts.id.disableAutoSelect();\n        console.log(\"🧹 Cleared existing Google auto-select\");\n      } catch (e) {\n        console.log(\"ℹ️ No existing Google session to clear\");\n      }\n\n      try {\n        window.google.accounts.id.initialize({\n          client_id: process.env.REACT_APP_GOOGLE_CLIENT_ID,\n          callback: handleCredentialResponse,\n          auto_select: false,\n          cancel_on_tap_outside: true,\n          use_fedcm_for_prompt: false, // Disable FedCM to avoid conflicts\n          itp_support: true, // Enable Intelligent Tracking Prevention support\n        });\n        console.log(\"✅ Google Sign-In initialized successfully\");\n\n        // Render the Google Sign-In button\n        if (googleButtonRef.current) {\n          console.log(\"🎨 Rendering Google Sign-In button...\");\n          window.google.accounts.id.renderButton(googleButtonRef.current, {\n            theme: \"outline\",\n            size: \"large\",\n            width: \"100%\",\n            text: \"signin_with\",\n            locale: \"id\",\n          });\n          console.log(\"✅ Google Sign-In button rendered\");\n        } else {\n          console.error(\"❌ Button ref is null, cannot render button\");\n          setError(\"Tidak dapat menampilkan tombol login Google\");\n        }\n      } catch (error) {\n        console.error(\"❌ Error initializing Google Sign-In:\", error);\n        setError(\"Gagal menginisialisasi Google Sign-In: \" + error.message);\n      }\n    } else {\n      console.error(\"Google Identity Services not loaded\");\n      setError(\"Google Identity Services tidak dapat dimuat\");\n    }\n  };\n\n  // Function to load Google script manually if not loaded\n  const loadGoogleScript = () => {\n    return new Promise((resolve, reject) => {\n      if (window.google && window.google.accounts) {\n        resolve();\n        return;\n      }\n\n      const script = document.createElement(\"script\");\n      script.src = \"https://accounts.google.com/gsi/client\";\n      script.async = true;\n      script.defer = true;\n      script.onload = () => {\n        console.log(\"✅ Google script loaded manually\");\n        // Wait a bit for the script to initialize\n        setTimeout(() => {\n          if (window.google && window.google.accounts) {\n            resolve();\n          } else {\n            reject(new Error(\"Google script loaded but API not available\"));\n          }\n        }, 500);\n      };\n      script.onerror = () => {\n        reject(new Error(\"Failed to load Google script\"));\n      };\n      document.head.appendChild(script);\n    });\n  };\n\n  useEffect(() => {\n    // Initialize Google Sign-In when component mounts\n    console.log(\"🚀 Login component mounted, checking Google script...\");\n    console.log(\n      \"🔑 Google Client ID from env:\",\n      process.env.REACT_APP_GOOGLE_CLIENT_ID\n    );\n    console.log(\"🌐 Window.google available:\", !!window.google);\n    console.log(\n      \"🌐 Window.google.accounts available:\",\n      !!(window.google && window.google.accounts)\n    );\n\n    const initializeGoogle = async () => {\n      try {\n        // First check if already loaded\n        if (window.google && window.google.accounts) {\n          console.log(\"✅ Google script already loaded\");\n          initializeGoogleSignIn();\n          return;\n        }\n\n        // Try to load manually\n        console.log(\"📥 Loading Google script manually...\");\n        await loadGoogleScript();\n        initializeGoogleSignIn();\n      } catch (error) {\n        console.error(\"❌ Failed to load Google script:\", error);\n        setError(\n          \"Google Identity Services gagal dimuat. Silakan refresh halaman.\"\n        );\n      }\n    };\n\n    initializeGoogle();\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []); // Remove dependency to avoid infinite loop\n\n  const handleManualLogin = () => {\n    console.log(\"🔄 Manual login triggered\");\n    console.log(\"🌐 Google available:\", !!window.google);\n    console.log(\n      \"🌐 Google accounts available:\",\n      !!(window.google && window.google.accounts)\n    );\n\n    if (window.google && window.google.accounts) {\n      try {\n        console.log(\"🚀 Triggering Google prompt...\");\n        window.google.accounts.id.prompt((notification) => {\n          console.log(\"📢 Google prompt notification:\", notification);\n          if (notification.isNotDisplayed() || notification.isSkippedMoment()) {\n            console.log(\"⚠️ Google prompt was not displayed or skipped\");\n            setError(\n              \"Google login popup tidak muncul. Silakan coba lagi atau periksa popup blocker.\"\n            );\n          }\n        });\n      } catch (error) {\n        console.error(\"❌ Error triggering Google prompt:\", error);\n        setError(\"Gagal membuka Google login: \" + error.message);\n      }\n    } else {\n      console.error(\"❌ Google Identity Services not available\");\n      setError(\n        \"Google Identity Services tidak tersedia. Silakan refresh halaman.\"\n      );\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-r from-green-50 to-yellow-50 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center\">\n      <div className=\"max-w-md w-full mx-4\">\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-xl p-8\">\n          {/* Logo and Title */}\n          <div className=\"text-center mb-8\">\n            <div className=\"mx-auto w-20 h-20 bg-gradient-to-r from-green-600 to-yellow-500 rounded-full flex items-center justify-center mb-4\">\n              <span className=\"text-white text-2xl font-bold\">U</span>\n            </div>\n            <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-2\">\n              Chatbot UNAND\n            </h1>\n            <p className=\"text-gray-600 dark:text-gray-400 text-sm\">\n              UNTUK KEDJAJAAN BANGSA\n            </p>\n            <p className=\"text-gray-500 dark:text-gray-500 text-sm mt-2\">\n              Masuk untuk mengakses chatbot peraturan kampus\n            </p>\n          </div>\n\n          {/* Error Message */}\n          {error && (\n            <div className=\"mb-4 p-3 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-lg\">\n              <p className=\"text-red-700 dark:text-red-300 text-sm\">{error}</p>\n            </div>\n          )}\n\n          {/* Info Message */}\n          <div className=\"mb-4 p-3 bg-blue-50 dark:bg-blue-900 border border-blue-200 dark:border-blue-700 rounded-lg\">\n            <p className=\"text-blue-700 dark:text-blue-300 text-sm\">\n              <strong>Informasi:</strong> Jika tombol Google Sign-In tidak\n              muncul, gunakan tombol \"Masuk dengan Google\" di bawah ini.\n            </p>\n          </div>\n\n          {/* Google Sign-In Button Container */}\n          <div className=\"mb-4\">\n            <div ref={googleButtonRef} className=\"w-full\"></div>\n          </div>\n\n          {/* Loading State */}\n          {loading && (\n            <div className=\"flex items-center justify-center py-3\">\n              <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-green-600 mr-3\"></div>\n              <span className=\"text-gray-600 dark:text-gray-400\">\n                Memproses login...\n              </span>\n            </div>\n          )}\n\n          {/* Footer */}\n          <div className=\"mt-6 text-center\">\n            <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n              Dengan masuk, Anda menyetujui penggunaan data untuk keperluan\n              chatbot\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Login;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM;IAAEC;EAAM,CAAC,GAAGL,OAAO,CAAC,CAAC;EAC3B,MAAM,CAACM,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACW,KAAK,EAAEC,QAAQ,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAMa,eAAe,GAAGX,MAAM,CAAC,IAAI,CAAC;EAEpC,MAAMY,wBAAwB,GAAG,MAAOC,QAAQ,IAAK;IACnDC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEF,QAAQ,CAAC;IACpDL,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACFI,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;MAC7C,MAAMT,KAAK,CAACO,QAAQ,CAACG,UAAU,CAAC;MAChCF,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;IAClC,CAAC,CAAC,OAAON,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CC,QAAQ,CAAC,iBAAiBD,KAAK,CAACQ,OAAO,EAAE,CAAC;IAC5C,CAAC,SAAS;MACRT,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMU,sBAAsB,GAAGA,CAAA,KAAM;IACnC,IAAIC,MAAM,CAACC,MAAM,IAAID,MAAM,CAACC,MAAM,CAACC,QAAQ,EAAE;MAC3CP,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;MAC/CD,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEO,OAAO,CAACC,GAAG,CAACC,0BAA0B,CAAC;MACpEV,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEI,MAAM,CAACM,QAAQ,CAACC,MAAM,CAAC;MACzDZ,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE,CAAC,CAACJ,eAAe,CAACgB,OAAO,CAAC;;MAEhE;MACA,IAAI;QACFR,MAAM,CAACC,MAAM,CAACC,QAAQ,CAACO,EAAE,CAACC,iBAAiB,CAAC,CAAC;QAC7Cf,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MACvD,CAAC,CAAC,OAAOe,CAAC,EAAE;QACVhB,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MACvD;MAEA,IAAI;QACFI,MAAM,CAACC,MAAM,CAACC,QAAQ,CAACO,EAAE,CAACG,UAAU,CAAC;UACnCC,SAAS,EAAEV,OAAO,CAACC,GAAG,CAACC,0BAA0B;UACjDS,QAAQ,EAAErB,wBAAwB;UAClCsB,WAAW,EAAE,KAAK;UAClBC,qBAAqB,EAAE,IAAI;UAC3BC,oBAAoB,EAAE,KAAK;UAAE;UAC7BC,WAAW,EAAE,IAAI,CAAE;QACrB,CAAC,CAAC;QACFvB,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;;QAExD;QACA,IAAIJ,eAAe,CAACgB,OAAO,EAAE;UAC3Bb,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;UACpDI,MAAM,CAACC,MAAM,CAACC,QAAQ,CAACO,EAAE,CAACU,YAAY,CAAC3B,eAAe,CAACgB,OAAO,EAAE;YAC9DY,KAAK,EAAE,SAAS;YAChBC,IAAI,EAAE,OAAO;YACbC,KAAK,EAAE,MAAM;YACbC,IAAI,EAAE,aAAa;YACnBC,MAAM,EAAE;UACV,CAAC,CAAC;UACF7B,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;QACjD,CAAC,MAAM;UACLD,OAAO,CAACL,KAAK,CAAC,4CAA4C,CAAC;UAC3DC,QAAQ,CAAC,6CAA6C,CAAC;QACzD;MACF,CAAC,CAAC,OAAOD,KAAK,EAAE;QACdK,OAAO,CAACL,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;QAC5DC,QAAQ,CAAC,yCAAyC,GAAGD,KAAK,CAACQ,OAAO,CAAC;MACrE;IACF,CAAC,MAAM;MACLH,OAAO,CAACL,KAAK,CAAC,qCAAqC,CAAC;MACpDC,QAAQ,CAAC,6CAA6C,CAAC;IACzD;EACF,CAAC;;EAED;EACA,MAAMkC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACtC,IAAI5B,MAAM,CAACC,MAAM,IAAID,MAAM,CAACC,MAAM,CAACC,QAAQ,EAAE;QAC3CyB,OAAO,CAAC,CAAC;QACT;MACF;MAEA,MAAME,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;MAC/CF,MAAM,CAACG,GAAG,GAAG,wCAAwC;MACrDH,MAAM,CAACI,KAAK,GAAG,IAAI;MACnBJ,MAAM,CAACK,KAAK,GAAG,IAAI;MACnBL,MAAM,CAACM,MAAM,GAAG,MAAM;QACpBxC,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9C;QACAwC,UAAU,CAAC,MAAM;UACf,IAAIpC,MAAM,CAACC,MAAM,IAAID,MAAM,CAACC,MAAM,CAACC,QAAQ,EAAE;YAC3CyB,OAAO,CAAC,CAAC;UACX,CAAC,MAAM;YACLC,MAAM,CAAC,IAAIS,KAAK,CAAC,4CAA4C,CAAC,CAAC;UACjE;QACF,CAAC,EAAE,GAAG,CAAC;MACT,CAAC;MACDR,MAAM,CAACS,OAAO,GAAG,MAAM;QACrBV,MAAM,CAAC,IAAIS,KAAK,CAAC,8BAA8B,CAAC,CAAC;MACnD,CAAC;MACDP,QAAQ,CAACS,IAAI,CAACC,WAAW,CAACX,MAAM,CAAC;IACnC,CAAC,CAAC;EACJ,CAAC;EAEDjD,SAAS,CAAC,MAAM;IACd;IACAe,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;IACpED,OAAO,CAACC,GAAG,CACT,+BAA+B,EAC/BO,OAAO,CAACC,GAAG,CAACC,0BACd,CAAC;IACDV,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE,CAAC,CAACI,MAAM,CAACC,MAAM,CAAC;IAC3DN,OAAO,CAACC,GAAG,CACT,sCAAsC,EACtC,CAAC,EAAEI,MAAM,CAACC,MAAM,IAAID,MAAM,CAACC,MAAM,CAACC,QAAQ,CAC5C,CAAC;IAED,MAAMuC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;MACnC,IAAI;QACF;QACA,IAAIzC,MAAM,CAACC,MAAM,IAAID,MAAM,CAACC,MAAM,CAACC,QAAQ,EAAE;UAC3CP,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;UAC7CG,sBAAsB,CAAC,CAAC;UACxB;QACF;;QAEA;QACAJ,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;QACnD,MAAM6B,gBAAgB,CAAC,CAAC;QACxB1B,sBAAsB,CAAC,CAAC;MAC1B,CAAC,CAAC,OAAOT,KAAK,EAAE;QACdK,OAAO,CAACL,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QACvDC,QAAQ,CACN,iEACF,CAAC;MACH;IACF,CAAC;IAEDkD,gBAAgB,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B/C,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;IACxCD,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,CAAC,CAACI,MAAM,CAACC,MAAM,CAAC;IACpDN,OAAO,CAACC,GAAG,CACT,+BAA+B,EAC/B,CAAC,EAAEI,MAAM,CAACC,MAAM,IAAID,MAAM,CAACC,MAAM,CAACC,QAAQ,CAC5C,CAAC;IAED,IAAIF,MAAM,CAACC,MAAM,IAAID,MAAM,CAACC,MAAM,CAACC,QAAQ,EAAE;MAC3C,IAAI;QACFP,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;QAC7CI,MAAM,CAACC,MAAM,CAACC,QAAQ,CAACO,EAAE,CAACkC,MAAM,CAAEC,YAAY,IAAK;UACjDjD,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEgD,YAAY,CAAC;UAC3D,IAAIA,YAAY,CAACC,cAAc,CAAC,CAAC,IAAID,YAAY,CAACE,eAAe,CAAC,CAAC,EAAE;YACnEnD,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;YAC5DL,QAAQ,CACN,gFACF,CAAC;UACH;QACF,CAAC,CAAC;MACJ,CAAC,CAAC,OAAOD,KAAK,EAAE;QACdK,OAAO,CAACL,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QACzDC,QAAQ,CAAC,8BAA8B,GAAGD,KAAK,CAACQ,OAAO,CAAC;MAC1D;IACF,CAAC,MAAM;MACLH,OAAO,CAACL,KAAK,CAAC,0CAA0C,CAAC;MACzDC,QAAQ,CACN,mEACF,CAAC;IACH;EACF,CAAC;EAED,oBACEP,OAAA;IAAK+D,SAAS,EAAC,+HAA+H;IAAAC,QAAA,eAC5IhE,OAAA;MAAK+D,SAAS,EAAC,sBAAsB;MAAAC,QAAA,eACnChE,OAAA;QAAK+D,SAAS,EAAC,oDAAoD;QAAAC,QAAA,gBAEjEhE,OAAA;UAAK+D,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BhE,OAAA;YAAK+D,SAAS,EAAC,oHAAoH;YAAAC,QAAA,eACjIhE,OAAA;cAAM+D,SAAS,EAAC,+BAA+B;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC,eACNpE,OAAA;YAAI+D,SAAS,EAAC,uDAAuD;YAAAC,QAAA,EAAC;UAEtE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLpE,OAAA;YAAG+D,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAExD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJpE,OAAA;YAAG+D,SAAS,EAAC,+CAA+C;YAAAC,QAAA,EAAC;UAE7D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,EAGL9D,KAAK,iBACJN,OAAA;UAAK+D,SAAS,EAAC,yFAAyF;UAAAC,QAAA,eACtGhE,OAAA;YAAG+D,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAE1D;UAAK;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CACN,eAGDpE,OAAA;UAAK+D,SAAS,EAAC,6FAA6F;UAAAC,QAAA,eAC1GhE,OAAA;YAAG+D,SAAS,EAAC,0CAA0C;YAAAC,QAAA,gBACrDhE,OAAA;cAAAgE,QAAA,EAAQ;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,kGAE7B;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGNpE,OAAA;UAAK+D,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBhE,OAAA;YAAKqE,GAAG,EAAE7D,eAAgB;YAACuD,SAAS,EAAC;UAAQ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC,EAGLhE,OAAO,iBACNJ,OAAA;UAAK+D,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpDhE,OAAA;YAAK+D,SAAS,EAAC;UAAoE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1FpE,OAAA;YAAM+D,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAEnD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN,eAGDpE,OAAA;UAAK+D,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC/BhE,OAAA;YAAG+D,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAGxD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClE,EAAA,CA5OID,KAAK;EAAA,QACSH,OAAO;AAAA;AAAAwE,EAAA,GADrBrE,KAAK;AA8OX,eAAeA,KAAK;AAAC,IAAAqE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}