{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"20\",\n  height: \"14\",\n  x: \"2\",\n  y: \"3\",\n  rx: \"2\",\n  key: \"48i651\"\n}], [\"line\", {\n  x1: \"8\",\n  x2: \"16\",\n  y1: \"21\",\n  y2: \"21\",\n  key: \"1svkeh\"\n}], [\"line\", {\n  x1: \"12\",\n  x2: \"12\",\n  y1: \"17\",\n  y2: \"21\",\n  key: \"vw1qmm\"\n}]];\nconst Monitor = createLucideIcon(\"monitor\", __iconNode);\nexport { __iconNode, Monitor as default };", "map": {"version": 3, "names": ["__iconNode", "width", "height", "x", "y", "rx", "key", "x1", "x2", "y1", "y2", "Monitor", "createLucideIcon"], "sources": ["D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\node_modules\\lucide-react\\src\\icons\\monitor.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['rect', { width: '20', height: '14', x: '2', y: '3', rx: '2', key: '48i651' }],\n  ['line', { x1: '8', x2: '16', y1: '21', y2: '21', key: '1svkeh' }],\n  ['line', { x1: '12', x2: '12', y1: '17', y2: '21', key: 'vw1qmm' }],\n];\n\n/**\n * @component @name Monitor\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMjAiIGhlaWdodD0iMTQiIHg9IjIiIHk9IjMiIHJ4PSIyIiAvPgogIDxsaW5lIHgxPSI4IiB4Mj0iMTYiIHkxPSIyMSIgeTI9IjIxIiAvPgogIDxsaW5lIHgxPSIxMiIgeDI9IjEyIiB5MT0iMTciIHkyPSIyMSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/monitor\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Monitor = createLucideIcon('monitor', __iconNode);\n\nexport default Monitor;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,QAAQ;EAAEC,KAAA,EAAO;EAAMC,MAAQ;EAAMC,CAAG;EAAKC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9E,CAAC,QAAQ;EAAEC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,GACjE,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAU,GACpE;AAaM,MAAAK,OAAA,GAAUC,gBAAiB,YAAWZ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}