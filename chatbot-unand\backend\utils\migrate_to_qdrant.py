#!/usr/bin/env python3
"""
Migration script to populate Qdrant with documents from the data folder.
"""

import os
import sys
from dotenv import load_dotenv
from docx import Document
import google.generativeai as genai
sys.path.append(os.path.dirname(os.path.dirname(__file__)))
from services.qdrant_service_pure import QdrantService

# Load environment variables from parent directory
env_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), '.env')
print(f"Debug: Loading .env from: {env_path}")
print(f"Debug: .env exists: {os.path.exists(env_path)}")
load_dotenv(env_path)

# Configure Gemini
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
print(f"Debug: GEMINI_API_KEY loaded: {GEMINI_API_KEY[:20] if GEMINI_API_KEY else 'None'}...")
if not GEMINI_API_KEY:
    print("Error: GEMINI_API_KEY not found in environment variables")
    sys.exit(1)

genai.configure(api_key=GEMINI_API_KEY)

def extract_text_from_docx(filepath):
    """Extracts text from a .docx file."""
    try:
        doc = Document(filepath)
        text = ""
        for paragraph in doc.paragraphs:
            text += paragraph.text + "\n"
        return text.strip()
    except Exception as e:
        print(f"Error reading {filepath}: {e}")
        return ""

def chunk_text(text, chunk_size=1000, overlap=200):
    """Split text into overlapping chunks."""
    if len(text) <= chunk_size:
        return [text]
    
    chunks = []
    start = 0
    while start < len(text):
        end = start + chunk_size
        if end >= len(text):
            chunks.append(text[start:])
            break
        else:
            # Find the last space before the end to avoid cutting words
            last_space = text.rfind(' ', start, end)
            if last_space != -1 and last_space > start:
                end = last_space
            chunks.append(text[start:end])
            start = end - overlap
    
    return chunks

def main():
    print("Starting migration to Qdrant...")
    
    # Initialize Qdrant service
    try:
        qdrant_service = QdrantService()
        print("Qdrant service initialized successfully")
    except Exception as e:
        print(f"Error initializing Qdrant service: {e}")
        sys.exit(1)
    
    # Get data directory (one level up from utils)
    data_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "data")
    if not os.path.exists(data_dir):
        print(f"Data directory not found: {data_dir}")
        sys.exit(1)
    
    # Process all .docx files
    all_chunks_with_metadata = []
    
    for filename in os.listdir(data_dir):
        if filename.endswith(".docx") and not filename.startswith("~$"):
            filepath = os.path.join(data_dir, filename)
            print(f"Processing {filename}...")
            
            try:
                text = extract_text_from_docx(filepath)
                if not text:
                    print(f"No text extracted from {filename}")
                    continue
                
                chunks = chunk_text(text)
                print(f"Generated {len(chunks)} chunks from {filename}")
                
                # Add metadata for each chunk
                for i, chunk in enumerate(chunks):
                    all_chunks_with_metadata.append({
                        "text": chunk,
                        "filename": filename,
                        "filepath": filepath,
                        "chunk_index": i
                    })
                    
            except Exception as e:
                print(f"Error processing {filename}: {e}")
                continue
    
    if not all_chunks_with_metadata:
        print("No documents found to process")
        sys.exit(1)
    
    print(f"Total chunks to process: {len(all_chunks_with_metadata)}")
    
    # Clear existing collection and add new documents
    try:
        print("Clearing existing collection...")
        qdrant_service.clear_collection()
        
        print("Adding documents to Qdrant...")
        qdrant_service.add_documents_batch(all_chunks_with_metadata)
        
        # Verify the data was added
        info = qdrant_service.get_collection_info()
        print(f"Migration completed successfully!")
        print(f"Collection info: {info}")
        
    except Exception as e:
        print(f"Error during migration: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
