{"ast": null, "code": "// packages/react/direction/src/direction.tsx\nimport * as React from \"react\";\nimport { jsx } from \"react/jsx-runtime\";\nvar DirectionContext = React.createContext(void 0);\nvar DirectionProvider = props => {\n  const {\n    dir,\n    children\n  } = props;\n  return /* @__PURE__ */jsx(DirectionContext.Provider, {\n    value: dir,\n    children\n  });\n};\nfunction useDirection(localDir) {\n  const globalDir = React.useContext(DirectionContext);\n  return localDir || globalDir || \"ltr\";\n}\nvar Provider = DirectionProvider;\nexport { DirectionProvider, Provider, useDirection };", "map": {"version": 3, "names": ["React", "jsx", "DirectionContext", "createContext", "DirectionProvider", "props", "dir", "children", "Provider", "value", "useDirection", "localDir", "globalDir", "useContext"], "sources": ["D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\node_modules\\@radix-ui\\react-direction\\src\\direction.tsx"], "sourcesContent": ["import * as React from 'react';\n\ntype Direction = 'ltr' | 'rtl';\nconst DirectionContext = React.createContext<Direction | undefined>(undefined);\n\n/* -------------------------------------------------------------------------------------------------\n * Direction\n * -----------------------------------------------------------------------------------------------*/\n\ninterface DirectionProviderProps {\n  children?: React.ReactNode;\n  dir: Direction;\n}\nconst DirectionProvider: React.FC<DirectionProviderProps> = (props) => {\n  const { dir, children } = props;\n  return <DirectionContext.Provider value={dir}>{children}</DirectionContext.Provider>;\n};\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction useDirection(localDir?: Direction) {\n  const globalDir = React.useContext(DirectionContext);\n  return localDir || globalDir || 'ltr';\n}\n\nconst Provider = DirectionProvider;\n\nexport {\n  useDirection,\n  //\n  Provider,\n  //\n  DirectionProvider,\n};\n"], "mappings": ";AAAA,YAAYA,KAAA,MAAW;AAed,SAAAC,GAAA;AAZT,IAAMC,gBAAA,GAAyBF,KAAA,CAAAG,aAAA,CAAqC,MAAS;AAU7E,IAAMC,iBAAA,GAAuDC,KAAA,IAAU;EACrE,MAAM;IAAEC,GAAA;IAAKC;EAAS,IAAIF,KAAA;EAC1B,OAAO,eAAAJ,GAAA,CAACC,gBAAA,CAAiBM,QAAA,EAAjB;IAA0BC,KAAA,EAAOH,GAAA;IAAMC;EAAA,CAAS;AAC1D;AAIA,SAASG,aAAaC,QAAA,EAAsB;EAC1C,MAAMC,SAAA,GAAkBZ,KAAA,CAAAa,UAAA,CAAWX,gBAAgB;EACnD,OAAOS,QAAA,IAAYC,SAAA,IAAa;AAClC;AAEA,IAAMJ,QAAA,GAAWJ,iBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}