{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website kp\\\\chatbot-unand\\\\frontend\\\\src\\\\components\\\\ThemeToggle.jsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Switch } from './ui/switch';\nimport { useTheme } from '../contexts/ThemeContext';\nimport { Sun, Moon } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ThemeToggle = () => {\n  _s();\n  const {\n    theme,\n    toggleTheme\n  } = useTheme();\n  const isDark = theme === 'dark';\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex items-center gap-2\",\n    children: [/*#__PURE__*/_jsxDEV(Sun, {\n      className: `h-4 w-4 transition-colors ${isDark ? 'text-muted-foreground' : 'text-yellow-500'}`\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Switch, {\n      checked: isDark,\n      onCheckedChange: toggleTheme,\n      className: \"data-[state=checked]:bg-slate-700 data-[state=unchecked]:bg-yellow-200\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Moon, {\n      className: `h-4 w-4 transition-colors ${isDark ? 'text-blue-400' : 'text-muted-foreground'}`\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 5\n  }, this);\n};\n_s(ThemeToggle, \"Q4eAjrIZ0CuRuhycs6byifK2KBk=\", false, function () {\n  return [useTheme];\n});\n_c = ThemeToggle;\nexport default ThemeToggle;\nvar _c;\n$RefreshReg$(_c, \"ThemeToggle\");", "map": {"version": 3, "names": ["React", "Switch", "useTheme", "Sun", "Moon", "jsxDEV", "_jsxDEV", "ThemeToggle", "_s", "theme", "toggleTheme", "isDark", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "checked", "onCheckedChange", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/website kp/chatbot-unand/frontend/src/components/ThemeToggle.jsx"], "sourcesContent": ["import React from 'react';\nimport { Switch } from './ui/switch';\nimport { useTheme } from '../contexts/ThemeContext';\nimport { Sun, Moon } from 'lucide-react';\n\nconst ThemeToggle = () => {\n  const { theme, toggleTheme } = useTheme();\n  const isDark = theme === 'dark';\n\n  return (\n    <div className=\"flex items-center gap-2\">\n      <Sun className={`h-4 w-4 transition-colors ${isDark ? 'text-muted-foreground' : 'text-yellow-500'}`} />\n      <Switch\n        checked={isDark}\n        onCheckedChange={toggleTheme}\n        className=\"data-[state=checked]:bg-slate-700 data-[state=unchecked]:bg-yellow-200\"\n      />\n      <Moon className={`h-4 w-4 transition-colors ${isDark ? 'text-blue-400' : 'text-muted-foreground'}`} />\n    </div>\n  );\n};\n\nexport default ThemeToggle;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,GAAG,EAAEC,IAAI,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM;IAAEC,KAAK;IAAEC;EAAY,CAAC,GAAGR,QAAQ,CAAC,CAAC;EACzC,MAAMS,MAAM,GAAGF,KAAK,KAAK,MAAM;EAE/B,oBACEH,OAAA;IAAKM,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBACtCP,OAAA,CAACH,GAAG;MAACS,SAAS,EAAE,6BAA6BD,MAAM,GAAG,uBAAuB,GAAG,iBAAiB;IAAG;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACvGX,OAAA,CAACL,MAAM;MACLiB,OAAO,EAAEP,MAAO;MAChBQ,eAAe,EAAET,WAAY;MAC7BE,SAAS,EAAC;IAAwE;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnF,CAAC,eACFX,OAAA,CAACF,IAAI;MAACQ,SAAS,EAAE,6BAA6BD,MAAM,GAAG,eAAe,GAAG,uBAAuB;IAAG;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACnG,CAAC;AAEV,CAAC;AAACT,EAAA,CAfID,WAAW;EAAA,QACgBL,QAAQ;AAAA;AAAAkB,EAAA,GADnCb,WAAW;AAiBjB,eAAeA,WAAW;AAAC,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}