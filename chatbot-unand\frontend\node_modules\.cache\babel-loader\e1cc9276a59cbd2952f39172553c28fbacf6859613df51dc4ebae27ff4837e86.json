{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website kp\\\\chatbot-unand\\\\frontend\\\\src\\\\components\\\\SessionGuard.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from \"react\";\nimport { useNavigate, useLocation } from \"react-router-dom\";\nimport { useAuth } from \"../contexts/AuthContext\";\n\n// Session Guard Component to prevent cross-session access\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SessionGuard = ({\n  children,\n  requiredSessionType\n}) => {\n  _s();\n  const {\n    sessionType,\n    isSessionConflict,\n    clearAllSessions,\n    loading\n  } = useAuth();\n  const navigate = useNavigate();\n  const location = useLocation();\n  useEffect(() => {\n    // Skip check during loading\n    if (loading) return;\n    const currentPath = location.pathname;\n    const isAdminPath = currentPath.startsWith('/admin');\n    const isUserPath = !isAdminPath;\n    console.log(\"SessionGuard: Checking session access\", {\n      currentPath,\n      sessionType,\n      requiredSessionType,\n      isAdminPath,\n      isUserPath\n    });\n\n    // Check for session conflicts\n    if (isSessionConflict(requiredSessionType)) {\n      console.log(\"SessionGuard: Session conflict detected, clearing all sessions\");\n      clearAllSessions();\n\n      // Redirect to appropriate login\n      if (requiredSessionType === 'admin') {\n        navigate('/admin/login', {\n          replace: true\n        });\n      } else {\n        navigate('/', {\n          replace: true\n        });\n      }\n      return;\n    }\n\n    // Prevent admin users from accessing user interface\n    if (sessionType === 'admin' && isUserPath) {\n      console.log(\"SessionGuard: Admin trying to access user interface, redirecting\");\n      navigate('/admin/dashboard', {\n        replace: true\n      });\n      return;\n    }\n\n    // Prevent regular users from accessing admin interface\n    if (sessionType === 'user' && isAdminPath && currentPath !== '/admin/login') {\n      console.log(\"SessionGuard: User trying to access admin interface, redirecting\");\n      navigate('/', {\n        replace: true\n      });\n      return;\n    }\n  }, [sessionType, requiredSessionType, location.pathname, navigate, isSessionConflict, clearAllSessions, loading]);\n\n  // Show loading during session check\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-r from-green-50 to-yellow-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Memeriksa sesi...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this);\n  }\n  return children;\n};\n_s(SessionGuard, \"QQueJQMO7cM/0EW2kbBvgbExDE4=\", false, function () {\n  return [useAuth, useNavigate, useLocation];\n});\n_c = SessionGuard;\nexport default SessionGuard;\nvar _c;\n$RefreshReg$(_c, \"SessionGuard\");", "map": {"version": 3, "names": ["React", "useEffect", "useNavigate", "useLocation", "useAuth", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON>", "children", "requiredSessionType", "_s", "sessionType", "isSessionConflict", "clearAllSessions", "loading", "navigate", "location", "currentPath", "pathname", "isAdminPath", "startsWith", "isUserPath", "console", "log", "replace", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/website kp/chatbot-unand/frontend/src/components/SessionGuard.jsx"], "sourcesContent": ["import React, { useEffect } from \"react\";\nimport { useNavigate, useLocation } from \"react-router-dom\";\nimport { useAuth } from \"../contexts/AuthContext\";\n\n// Session Guard Component to prevent cross-session access\nconst SessionGuard = ({ children, requiredSessionType }) => {\n  const { sessionType, isSessionConflict, clearAllSessions, loading } = useAuth();\n  const navigate = useNavigate();\n  const location = useLocation();\n\n  useEffect(() => {\n    // Skip check during loading\n    if (loading) return;\n\n    const currentPath = location.pathname;\n    const isAdminPath = currentPath.startsWith('/admin');\n    const isUserPath = !isAdminPath;\n\n    console.log(\"SessionGuard: Checking session access\", {\n      currentPath,\n      sessionType,\n      requiredSessionType,\n      isAdminPath,\n      isUserPath\n    });\n\n    // Check for session conflicts\n    if (isSessionConflict(requiredSessionType)) {\n      console.log(\"SessionGuard: Session conflict detected, clearing all sessions\");\n      clearAllSessions();\n      \n      // Redirect to appropriate login\n      if (requiredSessionType === 'admin') {\n        navigate('/admin/login', { replace: true });\n      } else {\n        navigate('/', { replace: true });\n      }\n      return;\n    }\n\n    // Prevent admin users from accessing user interface\n    if (sessionType === 'admin' && isUserPath) {\n      console.log(\"SessionGuard: Admin trying to access user interface, redirecting\");\n      navigate('/admin/dashboard', { replace: true });\n      return;\n    }\n\n    // Prevent regular users from accessing admin interface\n    if (sessionType === 'user' && isAdminPath && currentPath !== '/admin/login') {\n      console.log(\"SessionGuard: User trying to access admin interface, redirecting\");\n      navigate('/', { replace: true });\n      return;\n    }\n\n  }, [sessionType, requiredSessionType, location.pathname, navigate, isSessionConflict, clearAllSessions, loading]);\n\n  // Show loading during session check\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-r from-green-50 to-yellow-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600\">Memeriksa sesi...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return children;\n};\n\nexport default SessionGuard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,OAAO,QAAQ,yBAAyB;;AAEjD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,YAAY,GAAGA,CAAC;EAAEC,QAAQ;EAAEC;AAAoB,CAAC,KAAK;EAAAC,EAAA;EAC1D,MAAM;IAAEC,WAAW;IAAEC,iBAAiB;IAAEC,gBAAgB;IAAEC;EAAQ,CAAC,GAAGV,OAAO,CAAC,CAAC;EAC/E,MAAMW,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAC9B,MAAMc,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAE9BF,SAAS,CAAC,MAAM;IACd;IACA,IAAIa,OAAO,EAAE;IAEb,MAAMG,WAAW,GAAGD,QAAQ,CAACE,QAAQ;IACrC,MAAMC,WAAW,GAAGF,WAAW,CAACG,UAAU,CAAC,QAAQ,CAAC;IACpD,MAAMC,UAAU,GAAG,CAACF,WAAW;IAE/BG,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE;MACnDN,WAAW;MACXN,WAAW;MACXF,mBAAmB;MACnBU,WAAW;MACXE;IACF,CAAC,CAAC;;IAEF;IACA,IAAIT,iBAAiB,CAACH,mBAAmB,CAAC,EAAE;MAC1Ca,OAAO,CAACC,GAAG,CAAC,gEAAgE,CAAC;MAC7EV,gBAAgB,CAAC,CAAC;;MAElB;MACA,IAAIJ,mBAAmB,KAAK,OAAO,EAAE;QACnCM,QAAQ,CAAC,cAAc,EAAE;UAAES,OAAO,EAAE;QAAK,CAAC,CAAC;MAC7C,CAAC,MAAM;QACLT,QAAQ,CAAC,GAAG,EAAE;UAAES,OAAO,EAAE;QAAK,CAAC,CAAC;MAClC;MACA;IACF;;IAEA;IACA,IAAIb,WAAW,KAAK,OAAO,IAAIU,UAAU,EAAE;MACzCC,OAAO,CAACC,GAAG,CAAC,kEAAkE,CAAC;MAC/ER,QAAQ,CAAC,kBAAkB,EAAE;QAAES,OAAO,EAAE;MAAK,CAAC,CAAC;MAC/C;IACF;;IAEA;IACA,IAAIb,WAAW,KAAK,MAAM,IAAIQ,WAAW,IAAIF,WAAW,KAAK,cAAc,EAAE;MAC3EK,OAAO,CAACC,GAAG,CAAC,kEAAkE,CAAC;MAC/ER,QAAQ,CAAC,GAAG,EAAE;QAAES,OAAO,EAAE;MAAK,CAAC,CAAC;MAChC;IACF;EAEF,CAAC,EAAE,CAACb,WAAW,EAAEF,mBAAmB,EAAEO,QAAQ,CAACE,QAAQ,EAAEH,QAAQ,EAAEH,iBAAiB,EAAEC,gBAAgB,EAAEC,OAAO,CAAC,CAAC;;EAEjH;EACA,IAAIA,OAAO,EAAE;IACX,oBACER,OAAA;MAAKmB,SAAS,EAAC,2FAA2F;MAAAjB,QAAA,eACxGF,OAAA;QAAKmB,SAAS,EAAC,aAAa;QAAAjB,QAAA,gBAC1BF,OAAA;UAAKmB,SAAS,EAAC;QAA8E;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACpGvB,OAAA;UAAGmB,SAAS,EAAC,eAAe;UAAAjB,QAAA,EAAC;QAAiB;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,OAAOrB,QAAQ;AACjB,CAAC;AAACE,EAAA,CAhEIH,YAAY;EAAA,QACsDH,OAAO,EAC5DF,WAAW,EACXC,WAAW;AAAA;AAAA2B,EAAA,GAHxBvB,YAAY;AAkElB,eAAeA,YAAY;AAAC,IAAAuB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}