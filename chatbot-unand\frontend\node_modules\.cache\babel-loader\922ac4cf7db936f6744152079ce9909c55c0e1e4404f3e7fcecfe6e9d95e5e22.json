{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website kp\\\\chatbot-unand\\\\frontend\\\\src\\\\contexts\\\\AuthContext.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from \"react\";\nimport { jwtDecode } from \"jwt-decode\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error(\"useAuth must be used within an AuthProvider\");\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [token, setToken] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Admin state\n  const [adminUser, setAdminUser] = useState(null);\n  const [adminToken, setAdminToken] = useState(null);\n\n  // Session type tracking\n  const [sessionType, setSessionType] = useState(null); // 'user' | 'admin' | null\n\n  // Check for existing token on mount\n  useEffect(() => {\n    const savedToken = localStorage.getItem(\"access_token\");\n    const savedUser = localStorage.getItem(\"user\");\n    const savedAdminToken = localStorage.getItem(\"admin_token\");\n    const savedAdminUser = localStorage.getItem(\"admin_user\");\n    const savedSessionType = localStorage.getItem(\"session_type\");\n\n    // Clear conflicting sessions - only one type allowed per browser\n    if (savedToken && savedAdminToken) {\n      console.log(\"AuthContext: Conflicting sessions detected, clearing all\");\n      localStorage.clear();\n      setLoading(false);\n      return;\n    }\n    if (savedToken && savedUser) {\n      try {\n        // Check if token is still valid\n        const decoded = jwtDecode(savedToken);\n        const currentTime = Date.now() / 1000;\n        if (decoded.exp > currentTime) {\n          setToken(savedToken);\n          setUser(JSON.parse(savedUser));\n          setSessionType(\"user\");\n          localStorage.setItem(\"session_type\", \"user\");\n        } else {\n          // Token expired, clear storage\n          localStorage.removeItem(\"access_token\");\n          localStorage.removeItem(\"user\");\n          localStorage.removeItem(\"session_type\");\n        }\n      } catch (error) {\n        console.error(\"Error decoding token:\", error);\n        localStorage.removeItem(\"access_token\");\n        localStorage.removeItem(\"user\");\n        localStorage.removeItem(\"session_type\");\n      }\n    }\n    if (savedAdminToken && savedAdminUser) {\n      try {\n        // Check if admin token is still valid\n        const decoded = jwtDecode(savedAdminToken);\n        const currentTime = Date.now() / 1000;\n        if (decoded.exp > currentTime) {\n          setAdminToken(savedAdminToken);\n          setAdminUser(JSON.parse(savedAdminUser));\n          setSessionType(\"admin\");\n          localStorage.setItem(\"session_type\", \"admin\");\n        } else {\n          // Admin token expired, clear storage\n          localStorage.removeItem(\"admin_token\");\n          localStorage.removeItem(\"admin_user\");\n          localStorage.removeItem(\"session_type\");\n        }\n      } catch (error) {\n        console.error(\"Error decoding admin token:\", error);\n        localStorage.removeItem(\"admin_token\");\n        localStorage.removeItem(\"admin_user\");\n        localStorage.removeItem(\"session_type\");\n      }\n    }\n    setLoading(false);\n  }, []);\n  const login = async googleToken => {\n    try {\n      console.log(\"AuthContext: Starting login process...\");\n\n      // Clear any existing authentication data first to avoid conflicts\n      forceLogout();\n      const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || \"http://localhost:8000\";\n      console.log(\"AuthContext: Using API URL:\", API_BASE_URL);\n      const response = await fetch(`${API_BASE_URL}/auth/google`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          token: googleToken\n        })\n      });\n      console.log(\"AuthContext: Response status:\", response.status);\n      console.log(\"AuthContext: Response headers:\", response.headers);\n      if (!response.ok) {\n        const errorText = await response.text();\n        console.error(\"AuthContext: Response error:\", errorText);\n        console.error(\"AuthContext: Full response:\", response);\n        console.error(\"AuthContext: Response status:\", response.status);\n        console.error(\"AuthContext: Response statusText:\", response.statusText);\n\n        // Try to parse error as JSON if possible\n        let errorJson = null;\n        try {\n          errorJson = JSON.parse(errorText);\n          console.error(\"AuthContext: Parsed error JSON:\", errorJson);\n        } catch (e) {\n          console.error(\"AuthContext: Error text is not JSON:\", errorText);\n        }\n\n        // Handle specific token timing errors\n        if (errorJson && errorJson.detail && errorJson.detail.includes(\"Token used too early\")) {\n          console.log(\"AuthContext: Token timing issue detected, retrying in 2 seconds...\");\n          await new Promise(resolve => setTimeout(resolve, 2000));\n\n          // Retry the request\n          const retryResponse = await fetch(`${API_BASE_URL}/auth/google`, {\n            method: \"POST\",\n            headers: {\n              \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n              token: googleToken\n            })\n          });\n          if (retryResponse.ok) {\n            const retryData = await retryResponse.json();\n            console.log(\"AuthContext: Retry authentication successful\");\n\n            // Save to localStorage\n            localStorage.setItem(\"access_token\", retryData.access_token);\n            localStorage.setItem(\"user\", JSON.stringify(retryData.user));\n\n            // Update state\n            setToken(retryData.access_token);\n            setUser(retryData.user);\n            console.log(\"AuthContext: Login completed successfully after retry\");\n            return retryData;\n          }\n        }\n        throw new Error(`Authentication failed: ${response.status} - ${errorText}`);\n      }\n      const data = await response.json();\n      console.log(\"AuthContext: Response data:\", data);\n\n      // Save to localStorage\n      localStorage.setItem(\"access_token\", data.access_token);\n      localStorage.setItem(\"user\", JSON.stringify(data.user));\n\n      // Update state\n      setToken(data.access_token);\n      setUser(data.user);\n      console.log(\"AuthContext: Login completed successfully\");\n      return data;\n    } catch (error) {\n      console.error(\"AuthContext: Login error:\", error);\n      // Clear any partial authentication data on error\n      forceLogout();\n      throw error;\n    }\n  };\n  const logout = async () => {\n    console.log(\"AuthContext: Starting logout process...\");\n    try {\n      // Call backend logout endpoint if user is authenticated\n      if (token) {\n        const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || \"http://localhost:8000\";\n        console.log(\"AuthContext: Calling backend logout endpoint...\");\n        const response = await fetch(`${API_BASE_URL}/auth/logout`, {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${token}`\n          }\n        });\n        if (response.ok) {\n          const data = await response.json();\n          console.log(\"AuthContext: Backend logout successful:\", data);\n          console.log(`AuthContext: Deactivated ${data.sessions_deactivated} sessions`);\n        } else {\n          console.warn(\"AuthContext: Backend logout failed, but continuing with frontend logout\");\n        }\n      }\n    } catch (error) {\n      console.error(\"AuthContext: Error calling backend logout:\", error);\n      console.log(\"AuthContext: Continuing with frontend logout despite backend error\");\n    }\n\n    // Clear localStorage\n    localStorage.removeItem(\"access_token\");\n    localStorage.removeItem(\"user\");\n\n    // Clear state\n    setToken(null);\n    setUser(null);\n\n    // Sign out from Google more thoroughly\n    if (window.google && window.google.accounts) {\n      console.log(\"AuthContext: Signing out from Google...\");\n      try {\n        // Disable auto-select\n        window.google.accounts.id.disableAutoSelect();\n\n        // Revoke the current session\n        window.google.accounts.id.revoke(token, done => {\n          console.log(\"AuthContext: Google token revoked:\", done);\n        });\n\n        // Clear any Google session cookies\n        document.cookie = \"g_state=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.google.com;\";\n        document.cookie = \"g_csrf_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.google.com;\";\n      } catch (error) {\n        console.warn(\"AuthContext: Error during Google sign-out:\", error);\n      }\n    }\n    console.log(\"AuthContext: Logout completed\");\n  };\n  const forceLogout = () => {\n    console.log(\"AuthContext: Force logout - clearing all authentication data\");\n\n    // Clear localStorage immediately\n    localStorage.removeItem(\"access_token\");\n    localStorage.removeItem(\"user\");\n\n    // Clear state\n    setToken(null);\n    setUser(null);\n\n    // Clear Google session\n    if (window.google && window.google.accounts) {\n      try {\n        window.google.accounts.id.disableAutoSelect();\n      } catch (error) {\n        console.warn(\"AuthContext: Error during force Google sign-out:\", error);\n      }\n    }\n    console.log(\"AuthContext: Force logout completed\");\n  };\n  const isTokenExpired = token => {\n    if (!token) return true;\n    try {\n      // Decode JWT token to check expiry\n      const payload = JSON.parse(atob(token.split(\".\")[1]));\n      const currentTime = Math.floor(Date.now() / 1000);\n\n      // Check if token is expired (with 30 second buffer)\n      return payload.exp < currentTime + 30;\n    } catch (error) {\n      console.error(\"AuthContext: Error checking token expiry:\", error);\n      return true; // Assume expired if can't decode\n    }\n  };\n  const getAuthHeaders = () => {\n    if (token && !isTokenExpired(token)) {\n      return {\n        Authorization: `Bearer ${token}`,\n        \"Content-Type\": \"application/json\"\n      };\n    } else if (token && isTokenExpired(token)) {\n      // Token is expired, clear it\n      console.log(\"AuthContext: Token expired, clearing authentication\");\n      logout();\n      return {\n        \"Content-Type\": \"application/json\"\n      };\n    }\n    return {\n      \"Content-Type\": \"application/json\"\n    };\n  };\n\n  // Admin functions\n  const loginAdmin = async (email, password) => {\n    try {\n      console.log(\"AuthContext: Starting admin login process...\");\n      const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || \"http://localhost:8000\";\n      const response = await fetch(`${API_BASE_URL}/admin/login`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          email,\n          password\n        })\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || \"Admin login failed\");\n      }\n      const data = await response.json();\n      console.log(\"AuthContext: Admin login successful\");\n\n      // Save to localStorage\n      localStorage.setItem(\"admin_token\", data.access_token);\n      localStorage.setItem(\"admin_user\", JSON.stringify({\n        email: data.admin_email\n      }));\n\n      // Update state\n      setAdminToken(data.access_token);\n      setAdminUser({\n        email: data.admin_email\n      });\n      return data;\n    } catch (error) {\n      console.error(\"AuthContext: Admin login error:\", error);\n      throw error;\n    }\n  };\n  const logoutAdmin = () => {\n    console.log(\"AuthContext: Admin logout\");\n\n    // Clear localStorage\n    localStorage.removeItem(\"admin_token\");\n    localStorage.removeItem(\"admin_user\");\n\n    // Clear state\n    setAdminToken(null);\n    setAdminUser(null);\n  };\n  const getAdminAuthHeaders = () => {\n    if (adminToken && !isTokenExpired(adminToken)) {\n      return {\n        Authorization: `Bearer ${adminToken}`,\n        \"Content-Type\": \"application/json\"\n      };\n    } else if (adminToken && isTokenExpired(adminToken)) {\n      // Admin token is expired, clear it\n      console.log(\"AuthContext: Admin token expired, clearing authentication\");\n      logoutAdmin();\n      return {\n        \"Content-Type\": \"application/json\"\n      };\n    }\n    return {\n      \"Content-Type\": \"application/json\"\n    };\n  };\n  const value = {\n    user,\n    token,\n    loading,\n    login,\n    logout,\n    forceLogout,\n    getAuthHeaders,\n    isAuthenticated: !!user,\n    // Admin functions\n    adminUser,\n    adminToken,\n    loginAdmin,\n    logoutAdmin,\n    getAdminAuthHeaders,\n    isAdminAuthenticated: !!adminUser\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 415,\n    columnNumber: 10\n  }, this);\n};\n_s2(AuthProvider, \"JYAnmyL7JJZ9EhcolWjsFFfqCt4=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "jwtDecode", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "token", "setToken", "loading", "setLoading", "adminUser", "setAdminUser", "adminToken", "setAdminToken", "sessionType", "setSessionType", "savedToken", "localStorage", "getItem", "savedUser", "savedAdminToken", "savedAdminUser", "savedSessionType", "console", "log", "clear", "decoded", "currentTime", "Date", "now", "exp", "JSON", "parse", "setItem", "removeItem", "error", "login", "googleToken", "forceLogout", "API_BASE_URL", "process", "env", "REACT_APP_API_BASE_URL", "response", "fetch", "method", "headers", "body", "stringify", "status", "ok", "errorText", "text", "statusText", "<PERSON><PERSON><PERSON>", "e", "detail", "includes", "Promise", "resolve", "setTimeout", "retryResponse", "retryData", "json", "access_token", "data", "logout", "Authorization", "sessions_deactivated", "warn", "window", "google", "accounts", "id", "disableAutoSelect", "revoke", "done", "document", "cookie", "isTokenExpired", "payload", "atob", "split", "Math", "floor", "getAuthHeaders", "loginAdmin", "email", "password", "errorData", "admin_email", "logoutAdmin", "getAdminAuthHeaders", "value", "isAuthenticated", "isAdminAuthenticated", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/website kp/chatbot-unand/frontend/src/contexts/AuthContext.jsx"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from \"react\";\nimport { jwtDecode } from \"jwt-decode\";\n\nconst AuthContext = createContext();\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error(\"useAuth must be used within an AuthProvider\");\n  }\n  return context;\n};\n\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [token, setToken] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Admin state\n  const [adminUser, setAdminUser] = useState(null);\n  const [adminToken, setAdminToken] = useState(null);\n\n  // Session type tracking\n  const [sessionType, setSessionType] = useState(null); // 'user' | 'admin' | null\n\n  // Check for existing token on mount\n  useEffect(() => {\n    const savedToken = localStorage.getItem(\"access_token\");\n    const savedUser = localStorage.getItem(\"user\");\n    const savedAdminToken = localStorage.getItem(\"admin_token\");\n    const savedAdminUser = localStorage.getItem(\"admin_user\");\n    const savedSessionType = localStorage.getItem(\"session_type\");\n\n    // Clear conflicting sessions - only one type allowed per browser\n    if (savedToken && savedAdminToken) {\n      console.log(\"AuthContext: Conflicting sessions detected, clearing all\");\n      localStorage.clear();\n      setLoading(false);\n      return;\n    }\n\n    if (savedToken && savedUser) {\n      try {\n        // Check if token is still valid\n        const decoded = jwtDecode(savedToken);\n        const currentTime = Date.now() / 1000;\n\n        if (decoded.exp > currentTime) {\n          setToken(savedToken);\n          setUser(JSON.parse(savedUser));\n          setSessionType(\"user\");\n          localStorage.setItem(\"session_type\", \"user\");\n        } else {\n          // Token expired, clear storage\n          localStorage.removeItem(\"access_token\");\n          localStorage.removeItem(\"user\");\n          localStorage.removeItem(\"session_type\");\n        }\n      } catch (error) {\n        console.error(\"Error decoding token:\", error);\n        localStorage.removeItem(\"access_token\");\n        localStorage.removeItem(\"user\");\n        localStorage.removeItem(\"session_type\");\n      }\n    }\n\n    if (savedAdminToken && savedAdminUser) {\n      try {\n        // Check if admin token is still valid\n        const decoded = jwtDecode(savedAdminToken);\n        const currentTime = Date.now() / 1000;\n\n        if (decoded.exp > currentTime) {\n          setAdminToken(savedAdminToken);\n          setAdminUser(JSON.parse(savedAdminUser));\n          setSessionType(\"admin\");\n          localStorage.setItem(\"session_type\", \"admin\");\n        } else {\n          // Admin token expired, clear storage\n          localStorage.removeItem(\"admin_token\");\n          localStorage.removeItem(\"admin_user\");\n          localStorage.removeItem(\"session_type\");\n        }\n      } catch (error) {\n        console.error(\"Error decoding admin token:\", error);\n        localStorage.removeItem(\"admin_token\");\n        localStorage.removeItem(\"admin_user\");\n        localStorage.removeItem(\"session_type\");\n      }\n    }\n\n    setLoading(false);\n  }, []);\n\n  const login = async (googleToken) => {\n    try {\n      console.log(\"AuthContext: Starting login process...\");\n\n      // Clear any existing authentication data first to avoid conflicts\n      forceLogout();\n\n      const API_BASE_URL =\n        process.env.REACT_APP_API_BASE_URL || \"http://localhost:8000\";\n      console.log(\"AuthContext: Using API URL:\", API_BASE_URL);\n      const response = await fetch(`${API_BASE_URL}/auth/google`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify({ token: googleToken }),\n      });\n\n      console.log(\"AuthContext: Response status:\", response.status);\n      console.log(\"AuthContext: Response headers:\", response.headers);\n\n      if (!response.ok) {\n        const errorText = await response.text();\n        console.error(\"AuthContext: Response error:\", errorText);\n        console.error(\"AuthContext: Full response:\", response);\n        console.error(\"AuthContext: Response status:\", response.status);\n        console.error(\"AuthContext: Response statusText:\", response.statusText);\n\n        // Try to parse error as JSON if possible\n        let errorJson = null;\n        try {\n          errorJson = JSON.parse(errorText);\n          console.error(\"AuthContext: Parsed error JSON:\", errorJson);\n        } catch (e) {\n          console.error(\"AuthContext: Error text is not JSON:\", errorText);\n        }\n\n        // Handle specific token timing errors\n        if (\n          errorJson &&\n          errorJson.detail &&\n          errorJson.detail.includes(\"Token used too early\")\n        ) {\n          console.log(\n            \"AuthContext: Token timing issue detected, retrying in 2 seconds...\"\n          );\n          await new Promise((resolve) => setTimeout(resolve, 2000));\n\n          // Retry the request\n          const retryResponse = await fetch(`${API_BASE_URL}/auth/google`, {\n            method: \"POST\",\n            headers: {\n              \"Content-Type\": \"application/json\",\n            },\n            body: JSON.stringify({ token: googleToken }),\n          });\n\n          if (retryResponse.ok) {\n            const retryData = await retryResponse.json();\n            console.log(\"AuthContext: Retry authentication successful\");\n\n            // Save to localStorage\n            localStorage.setItem(\"access_token\", retryData.access_token);\n            localStorage.setItem(\"user\", JSON.stringify(retryData.user));\n\n            // Update state\n            setToken(retryData.access_token);\n            setUser(retryData.user);\n\n            console.log(\n              \"AuthContext: Login completed successfully after retry\"\n            );\n            return retryData;\n          }\n        }\n\n        throw new Error(\n          `Authentication failed: ${response.status} - ${errorText}`\n        );\n      }\n\n      const data = await response.json();\n      console.log(\"AuthContext: Response data:\", data);\n\n      // Save to localStorage\n      localStorage.setItem(\"access_token\", data.access_token);\n      localStorage.setItem(\"user\", JSON.stringify(data.user));\n\n      // Update state\n      setToken(data.access_token);\n      setUser(data.user);\n\n      console.log(\"AuthContext: Login completed successfully\");\n      return data;\n    } catch (error) {\n      console.error(\"AuthContext: Login error:\", error);\n      // Clear any partial authentication data on error\n      forceLogout();\n      throw error;\n    }\n  };\n\n  const logout = async () => {\n    console.log(\"AuthContext: Starting logout process...\");\n\n    try {\n      // Call backend logout endpoint if user is authenticated\n      if (token) {\n        const API_BASE_URL =\n          process.env.REACT_APP_API_BASE_URL || \"http://localhost:8000\";\n        console.log(\"AuthContext: Calling backend logout endpoint...\");\n\n        const response = await fetch(`${API_BASE_URL}/auth/logout`, {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${token}`,\n          },\n        });\n\n        if (response.ok) {\n          const data = await response.json();\n          console.log(\"AuthContext: Backend logout successful:\", data);\n          console.log(\n            `AuthContext: Deactivated ${data.sessions_deactivated} sessions`\n          );\n        } else {\n          console.warn(\n            \"AuthContext: Backend logout failed, but continuing with frontend logout\"\n          );\n        }\n      }\n    } catch (error) {\n      console.error(\"AuthContext: Error calling backend logout:\", error);\n      console.log(\n        \"AuthContext: Continuing with frontend logout despite backend error\"\n      );\n    }\n\n    // Clear localStorage\n    localStorage.removeItem(\"access_token\");\n    localStorage.removeItem(\"user\");\n\n    // Clear state\n    setToken(null);\n    setUser(null);\n\n    // Sign out from Google more thoroughly\n    if (window.google && window.google.accounts) {\n      console.log(\"AuthContext: Signing out from Google...\");\n      try {\n        // Disable auto-select\n        window.google.accounts.id.disableAutoSelect();\n\n        // Revoke the current session\n        window.google.accounts.id.revoke(token, (done) => {\n          console.log(\"AuthContext: Google token revoked:\", done);\n        });\n\n        // Clear any Google session cookies\n        document.cookie =\n          \"g_state=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.google.com;\";\n        document.cookie =\n          \"g_csrf_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.google.com;\";\n      } catch (error) {\n        console.warn(\"AuthContext: Error during Google sign-out:\", error);\n      }\n    }\n\n    console.log(\"AuthContext: Logout completed\");\n  };\n\n  const forceLogout = () => {\n    console.log(\"AuthContext: Force logout - clearing all authentication data\");\n\n    // Clear localStorage immediately\n    localStorage.removeItem(\"access_token\");\n    localStorage.removeItem(\"user\");\n\n    // Clear state\n    setToken(null);\n    setUser(null);\n\n    // Clear Google session\n    if (window.google && window.google.accounts) {\n      try {\n        window.google.accounts.id.disableAutoSelect();\n      } catch (error) {\n        console.warn(\"AuthContext: Error during force Google sign-out:\", error);\n      }\n    }\n\n    console.log(\"AuthContext: Force logout completed\");\n  };\n\n  const isTokenExpired = (token) => {\n    if (!token) return true;\n\n    try {\n      // Decode JWT token to check expiry\n      const payload = JSON.parse(atob(token.split(\".\")[1]));\n      const currentTime = Math.floor(Date.now() / 1000);\n\n      // Check if token is expired (with 30 second buffer)\n      return payload.exp < currentTime + 30;\n    } catch (error) {\n      console.error(\"AuthContext: Error checking token expiry:\", error);\n      return true; // Assume expired if can't decode\n    }\n  };\n\n  const getAuthHeaders = () => {\n    if (token && !isTokenExpired(token)) {\n      return {\n        Authorization: `Bearer ${token}`,\n        \"Content-Type\": \"application/json\",\n      };\n    } else if (token && isTokenExpired(token)) {\n      // Token is expired, clear it\n      console.log(\"AuthContext: Token expired, clearing authentication\");\n      logout();\n      return {\n        \"Content-Type\": \"application/json\",\n      };\n    }\n    return {\n      \"Content-Type\": \"application/json\",\n    };\n  };\n\n  // Admin functions\n  const loginAdmin = async (email, password) => {\n    try {\n      console.log(\"AuthContext: Starting admin login process...\");\n\n      const API_BASE_URL =\n        process.env.REACT_APP_API_BASE_URL || \"http://localhost:8000\";\n      const response = await fetch(`${API_BASE_URL}/admin/login`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify({ email, password }),\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || \"Admin login failed\");\n      }\n\n      const data = await response.json();\n      console.log(\"AuthContext: Admin login successful\");\n\n      // Save to localStorage\n      localStorage.setItem(\"admin_token\", data.access_token);\n      localStorage.setItem(\n        \"admin_user\",\n        JSON.stringify({ email: data.admin_email })\n      );\n\n      // Update state\n      setAdminToken(data.access_token);\n      setAdminUser({ email: data.admin_email });\n\n      return data;\n    } catch (error) {\n      console.error(\"AuthContext: Admin login error:\", error);\n      throw error;\n    }\n  };\n\n  const logoutAdmin = () => {\n    console.log(\"AuthContext: Admin logout\");\n\n    // Clear localStorage\n    localStorage.removeItem(\"admin_token\");\n    localStorage.removeItem(\"admin_user\");\n\n    // Clear state\n    setAdminToken(null);\n    setAdminUser(null);\n  };\n\n  const getAdminAuthHeaders = () => {\n    if (adminToken && !isTokenExpired(adminToken)) {\n      return {\n        Authorization: `Bearer ${adminToken}`,\n        \"Content-Type\": \"application/json\",\n      };\n    } else if (adminToken && isTokenExpired(adminToken)) {\n      // Admin token is expired, clear it\n      console.log(\"AuthContext: Admin token expired, clearing authentication\");\n      logoutAdmin();\n      return {\n        \"Content-Type\": \"application/json\",\n      };\n    }\n    return {\n      \"Content-Type\": \"application/json\",\n    };\n  };\n\n  const value = {\n    user,\n    token,\n    loading,\n    login,\n    logout,\n    forceLogout,\n    getAuthHeaders,\n    isAuthenticated: !!user,\n    // Admin functions\n    adminUser,\n    adminToken,\n    loginAdmin,\n    logoutAdmin,\n    getAdminAuthHeaders,\n    isAdminAuthenticated: !!adminUser,\n  };\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,SAASC,SAAS,QAAQ,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,WAAW,gBAAGP,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMQ,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGT,UAAU,CAACM,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACe,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACA,MAAM,CAACmB,SAAS,EAAEC,YAAY,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACqB,UAAU,EAAEC,aAAa,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACA,MAAM,CAACuB,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;;EAEtD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMwB,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;IACvD,MAAMC,SAAS,GAAGF,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IAC9C,MAAME,eAAe,GAAGH,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IAC3D,MAAMG,cAAc,GAAGJ,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IACzD,MAAMI,gBAAgB,GAAGL,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;;IAE7D;IACA,IAAIF,UAAU,IAAII,eAAe,EAAE;MACjCG,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;MACvEP,YAAY,CAACQ,KAAK,CAAC,CAAC;MACpBhB,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;IAEA,IAAIO,UAAU,IAAIG,SAAS,EAAE;MAC3B,IAAI;QACF;QACA,MAAMO,OAAO,GAAGjC,SAAS,CAACuB,UAAU,CAAC;QACrC,MAAMW,WAAW,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI;QAErC,IAAIH,OAAO,CAACI,GAAG,GAAGH,WAAW,EAAE;UAC7BpB,QAAQ,CAACS,UAAU,CAAC;UACpBX,OAAO,CAAC0B,IAAI,CAACC,KAAK,CAACb,SAAS,CAAC,CAAC;UAC9BJ,cAAc,CAAC,MAAM,CAAC;UACtBE,YAAY,CAACgB,OAAO,CAAC,cAAc,EAAE,MAAM,CAAC;QAC9C,CAAC,MAAM;UACL;UACAhB,YAAY,CAACiB,UAAU,CAAC,cAAc,CAAC;UACvCjB,YAAY,CAACiB,UAAU,CAAC,MAAM,CAAC;UAC/BjB,YAAY,CAACiB,UAAU,CAAC,cAAc,CAAC;QACzC;MACF,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdZ,OAAO,CAACY,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7ClB,YAAY,CAACiB,UAAU,CAAC,cAAc,CAAC;QACvCjB,YAAY,CAACiB,UAAU,CAAC,MAAM,CAAC;QAC/BjB,YAAY,CAACiB,UAAU,CAAC,cAAc,CAAC;MACzC;IACF;IAEA,IAAId,eAAe,IAAIC,cAAc,EAAE;MACrC,IAAI;QACF;QACA,MAAMK,OAAO,GAAGjC,SAAS,CAAC2B,eAAe,CAAC;QAC1C,MAAMO,WAAW,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI;QAErC,IAAIH,OAAO,CAACI,GAAG,GAAGH,WAAW,EAAE;UAC7Bd,aAAa,CAACO,eAAe,CAAC;UAC9BT,YAAY,CAACoB,IAAI,CAACC,KAAK,CAACX,cAAc,CAAC,CAAC;UACxCN,cAAc,CAAC,OAAO,CAAC;UACvBE,YAAY,CAACgB,OAAO,CAAC,cAAc,EAAE,OAAO,CAAC;QAC/C,CAAC,MAAM;UACL;UACAhB,YAAY,CAACiB,UAAU,CAAC,aAAa,CAAC;UACtCjB,YAAY,CAACiB,UAAU,CAAC,YAAY,CAAC;UACrCjB,YAAY,CAACiB,UAAU,CAAC,cAAc,CAAC;QACzC;MACF,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdZ,OAAO,CAACY,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnDlB,YAAY,CAACiB,UAAU,CAAC,aAAa,CAAC;QACtCjB,YAAY,CAACiB,UAAU,CAAC,YAAY,CAAC;QACrCjB,YAAY,CAACiB,UAAU,CAAC,cAAc,CAAC;MACzC;IACF;IAEAzB,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM2B,KAAK,GAAG,MAAOC,WAAW,IAAK;IACnC,IAAI;MACFd,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;MAErD;MACAc,WAAW,CAAC,CAAC;MAEb,MAAMC,YAAY,GAChBC,OAAO,CAACC,GAAG,CAACC,sBAAsB,IAAI,uBAAuB;MAC/DnB,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEe,YAAY,CAAC;MACxD,MAAMI,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGL,YAAY,cAAc,EAAE;QAC1DM,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEhB,IAAI,CAACiB,SAAS,CAAC;UAAE1C,KAAK,EAAE+B;QAAY,CAAC;MAC7C,CAAC,CAAC;MAEFd,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEmB,QAAQ,CAACM,MAAM,CAAC;MAC7D1B,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEmB,QAAQ,CAACG,OAAO,CAAC;MAE/D,IAAI,CAACH,QAAQ,CAACO,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAMR,QAAQ,CAACS,IAAI,CAAC,CAAC;QACvC7B,OAAO,CAACY,KAAK,CAAC,8BAA8B,EAAEgB,SAAS,CAAC;QACxD5B,OAAO,CAACY,KAAK,CAAC,6BAA6B,EAAEQ,QAAQ,CAAC;QACtDpB,OAAO,CAACY,KAAK,CAAC,+BAA+B,EAAEQ,QAAQ,CAACM,MAAM,CAAC;QAC/D1B,OAAO,CAACY,KAAK,CAAC,mCAAmC,EAAEQ,QAAQ,CAACU,UAAU,CAAC;;QAEvE;QACA,IAAIC,SAAS,GAAG,IAAI;QACpB,IAAI;UACFA,SAAS,GAAGvB,IAAI,CAACC,KAAK,CAACmB,SAAS,CAAC;UACjC5B,OAAO,CAACY,KAAK,CAAC,iCAAiC,EAAEmB,SAAS,CAAC;QAC7D,CAAC,CAAC,OAAOC,CAAC,EAAE;UACVhC,OAAO,CAACY,KAAK,CAAC,sCAAsC,EAAEgB,SAAS,CAAC;QAClE;;QAEA;QACA,IACEG,SAAS,IACTA,SAAS,CAACE,MAAM,IAChBF,SAAS,CAACE,MAAM,CAACC,QAAQ,CAAC,sBAAsB,CAAC,EACjD;UACAlC,OAAO,CAACC,GAAG,CACT,oEACF,CAAC;UACD,MAAM,IAAIkC,OAAO,CAAEC,OAAO,IAAKC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;UAEzD;UACA,MAAME,aAAa,GAAG,MAAMjB,KAAK,CAAC,GAAGL,YAAY,cAAc,EAAE;YAC/DM,MAAM,EAAE,MAAM;YACdC,OAAO,EAAE;cACP,cAAc,EAAE;YAClB,CAAC;YACDC,IAAI,EAAEhB,IAAI,CAACiB,SAAS,CAAC;cAAE1C,KAAK,EAAE+B;YAAY,CAAC;UAC7C,CAAC,CAAC;UAEF,IAAIwB,aAAa,CAACX,EAAE,EAAE;YACpB,MAAMY,SAAS,GAAG,MAAMD,aAAa,CAACE,IAAI,CAAC,CAAC;YAC5CxC,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;;YAE3D;YACAP,YAAY,CAACgB,OAAO,CAAC,cAAc,EAAE6B,SAAS,CAACE,YAAY,CAAC;YAC5D/C,YAAY,CAACgB,OAAO,CAAC,MAAM,EAAEF,IAAI,CAACiB,SAAS,CAACc,SAAS,CAAC1D,IAAI,CAAC,CAAC;;YAE5D;YACAG,QAAQ,CAACuD,SAAS,CAACE,YAAY,CAAC;YAChC3D,OAAO,CAACyD,SAAS,CAAC1D,IAAI,CAAC;YAEvBmB,OAAO,CAACC,GAAG,CACT,uDACF,CAAC;YACD,OAAOsC,SAAS;UAClB;QACF;QAEA,MAAM,IAAI9D,KAAK,CACb,0BAA0B2C,QAAQ,CAACM,MAAM,MAAME,SAAS,EAC1D,CAAC;MACH;MAEA,MAAMc,IAAI,GAAG,MAAMtB,QAAQ,CAACoB,IAAI,CAAC,CAAC;MAClCxC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEyC,IAAI,CAAC;;MAEhD;MACAhD,YAAY,CAACgB,OAAO,CAAC,cAAc,EAAEgC,IAAI,CAACD,YAAY,CAAC;MACvD/C,YAAY,CAACgB,OAAO,CAAC,MAAM,EAAEF,IAAI,CAACiB,SAAS,CAACiB,IAAI,CAAC7D,IAAI,CAAC,CAAC;;MAEvD;MACAG,QAAQ,CAAC0D,IAAI,CAACD,YAAY,CAAC;MAC3B3D,OAAO,CAAC4D,IAAI,CAAC7D,IAAI,CAAC;MAElBmB,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;MACxD,OAAOyC,IAAI;IACb,CAAC,CAAC,OAAO9B,KAAK,EAAE;MACdZ,OAAO,CAACY,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD;MACAG,WAAW,CAAC,CAAC;MACb,MAAMH,KAAK;IACb;EACF,CAAC;EAED,MAAM+B,MAAM,GAAG,MAAAA,CAAA,KAAY;IACzB3C,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;IAEtD,IAAI;MACF;MACA,IAAIlB,KAAK,EAAE;QACT,MAAMiC,YAAY,GAChBC,OAAO,CAACC,GAAG,CAACC,sBAAsB,IAAI,uBAAuB;QAC/DnB,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;QAE9D,MAAMmB,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGL,YAAY,cAAc,EAAE;UAC1DM,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClCqB,aAAa,EAAE,UAAU7D,KAAK;UAChC;QACF,CAAC,CAAC;QAEF,IAAIqC,QAAQ,CAACO,EAAE,EAAE;UACf,MAAMe,IAAI,GAAG,MAAMtB,QAAQ,CAACoB,IAAI,CAAC,CAAC;UAClCxC,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEyC,IAAI,CAAC;UAC5D1C,OAAO,CAACC,GAAG,CACT,4BAA4ByC,IAAI,CAACG,oBAAoB,WACvD,CAAC;QACH,CAAC,MAAM;UACL7C,OAAO,CAAC8C,IAAI,CACV,yEACF,CAAC;QACH;MACF;IACF,CAAC,CAAC,OAAOlC,KAAK,EAAE;MACdZ,OAAO,CAACY,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;MAClEZ,OAAO,CAACC,GAAG,CACT,oEACF,CAAC;IACH;;IAEA;IACAP,YAAY,CAACiB,UAAU,CAAC,cAAc,CAAC;IACvCjB,YAAY,CAACiB,UAAU,CAAC,MAAM,CAAC;;IAE/B;IACA3B,QAAQ,CAAC,IAAI,CAAC;IACdF,OAAO,CAAC,IAAI,CAAC;;IAEb;IACA,IAAIiE,MAAM,CAACC,MAAM,IAAID,MAAM,CAACC,MAAM,CAACC,QAAQ,EAAE;MAC3CjD,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;MACtD,IAAI;QACF;QACA8C,MAAM,CAACC,MAAM,CAACC,QAAQ,CAACC,EAAE,CAACC,iBAAiB,CAAC,CAAC;;QAE7C;QACAJ,MAAM,CAACC,MAAM,CAACC,QAAQ,CAACC,EAAE,CAACE,MAAM,CAACrE,KAAK,EAAGsE,IAAI,IAAK;UAChDrD,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEoD,IAAI,CAAC;QACzD,CAAC,CAAC;;QAEF;QACAC,QAAQ,CAACC,MAAM,GACb,8EAA8E;QAChFD,QAAQ,CAACC,MAAM,GACb,mFAAmF;MACvF,CAAC,CAAC,OAAO3C,KAAK,EAAE;QACdZ,OAAO,CAAC8C,IAAI,CAAC,4CAA4C,EAAElC,KAAK,CAAC;MACnE;IACF;IAEAZ,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;EAC9C,CAAC;EAED,MAAMc,WAAW,GAAGA,CAAA,KAAM;IACxBf,OAAO,CAACC,GAAG,CAAC,8DAA8D,CAAC;;IAE3E;IACAP,YAAY,CAACiB,UAAU,CAAC,cAAc,CAAC;IACvCjB,YAAY,CAACiB,UAAU,CAAC,MAAM,CAAC;;IAE/B;IACA3B,QAAQ,CAAC,IAAI,CAAC;IACdF,OAAO,CAAC,IAAI,CAAC;;IAEb;IACA,IAAIiE,MAAM,CAACC,MAAM,IAAID,MAAM,CAACC,MAAM,CAACC,QAAQ,EAAE;MAC3C,IAAI;QACFF,MAAM,CAACC,MAAM,CAACC,QAAQ,CAACC,EAAE,CAACC,iBAAiB,CAAC,CAAC;MAC/C,CAAC,CAAC,OAAOvC,KAAK,EAAE;QACdZ,OAAO,CAAC8C,IAAI,CAAC,kDAAkD,EAAElC,KAAK,CAAC;MACzE;IACF;IAEAZ,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;EACpD,CAAC;EAED,MAAMuD,cAAc,GAAIzE,KAAK,IAAK;IAChC,IAAI,CAACA,KAAK,EAAE,OAAO,IAAI;IAEvB,IAAI;MACF;MACA,MAAM0E,OAAO,GAAGjD,IAAI,CAACC,KAAK,CAACiD,IAAI,CAAC3E,KAAK,CAAC4E,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrD,MAAMvD,WAAW,GAAGwD,IAAI,CAACC,KAAK,CAACxD,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;;MAEjD;MACA,OAAOmD,OAAO,CAAClD,GAAG,GAAGH,WAAW,GAAG,EAAE;IACvC,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdZ,OAAO,CAACY,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACjE,OAAO,IAAI,CAAC,CAAC;IACf;EACF,CAAC;EAED,MAAMkD,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI/E,KAAK,IAAI,CAACyE,cAAc,CAACzE,KAAK,CAAC,EAAE;MACnC,OAAO;QACL6D,aAAa,EAAE,UAAU7D,KAAK,EAAE;QAChC,cAAc,EAAE;MAClB,CAAC;IACH,CAAC,MAAM,IAAIA,KAAK,IAAIyE,cAAc,CAACzE,KAAK,CAAC,EAAE;MACzC;MACAiB,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;MAClE0C,MAAM,CAAC,CAAC;MACR,OAAO;QACL,cAAc,EAAE;MAClB,CAAC;IACH;IACA,OAAO;MACL,cAAc,EAAE;IAClB,CAAC;EACH,CAAC;;EAED;EACA,MAAMoB,UAAU,GAAG,MAAAA,CAAOC,KAAK,EAAEC,QAAQ,KAAK;IAC5C,IAAI;MACFjE,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;MAE3D,MAAMe,YAAY,GAChBC,OAAO,CAACC,GAAG,CAACC,sBAAsB,IAAI,uBAAuB;MAC/D,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGL,YAAY,cAAc,EAAE;QAC1DM,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEhB,IAAI,CAACiB,SAAS,CAAC;UAAEuC,KAAK;UAAEC;QAAS,CAAC;MAC1C,CAAC,CAAC;MAEF,IAAI,CAAC7C,QAAQ,CAACO,EAAE,EAAE;QAChB,MAAMuC,SAAS,GAAG,MAAM9C,QAAQ,CAACoB,IAAI,CAAC,CAAC;QACvC,MAAM,IAAI/D,KAAK,CAACyF,SAAS,CAACjC,MAAM,IAAI,oBAAoB,CAAC;MAC3D;MAEA,MAAMS,IAAI,GAAG,MAAMtB,QAAQ,CAACoB,IAAI,CAAC,CAAC;MAClCxC,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;;MAElD;MACAP,YAAY,CAACgB,OAAO,CAAC,aAAa,EAAEgC,IAAI,CAACD,YAAY,CAAC;MACtD/C,YAAY,CAACgB,OAAO,CAClB,YAAY,EACZF,IAAI,CAACiB,SAAS,CAAC;QAAEuC,KAAK,EAAEtB,IAAI,CAACyB;MAAY,CAAC,CAC5C,CAAC;;MAED;MACA7E,aAAa,CAACoD,IAAI,CAACD,YAAY,CAAC;MAChCrD,YAAY,CAAC;QAAE4E,KAAK,EAAEtB,IAAI,CAACyB;MAAY,CAAC,CAAC;MAEzC,OAAOzB,IAAI;IACb,CAAC,CAAC,OAAO9B,KAAK,EAAE;MACdZ,OAAO,CAACY,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAMwD,WAAW,GAAGA,CAAA,KAAM;IACxBpE,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;;IAExC;IACAP,YAAY,CAACiB,UAAU,CAAC,aAAa,CAAC;IACtCjB,YAAY,CAACiB,UAAU,CAAC,YAAY,CAAC;;IAErC;IACArB,aAAa,CAAC,IAAI,CAAC;IACnBF,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMiF,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAIhF,UAAU,IAAI,CAACmE,cAAc,CAACnE,UAAU,CAAC,EAAE;MAC7C,OAAO;QACLuD,aAAa,EAAE,UAAUvD,UAAU,EAAE;QACrC,cAAc,EAAE;MAClB,CAAC;IACH,CAAC,MAAM,IAAIA,UAAU,IAAImE,cAAc,CAACnE,UAAU,CAAC,EAAE;MACnD;MACAW,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;MACxEmE,WAAW,CAAC,CAAC;MACb,OAAO;QACL,cAAc,EAAE;MAClB,CAAC;IACH;IACA,OAAO;MACL,cAAc,EAAE;IAClB,CAAC;EACH,CAAC;EAED,MAAME,KAAK,GAAG;IACZzF,IAAI;IACJE,KAAK;IACLE,OAAO;IACP4B,KAAK;IACL8B,MAAM;IACN5B,WAAW;IACX+C,cAAc;IACdS,eAAe,EAAE,CAAC,CAAC1F,IAAI;IACvB;IACAM,SAAS;IACTE,UAAU;IACV0E,UAAU;IACVK,WAAW;IACXC,mBAAmB;IACnBG,oBAAoB,EAAE,CAAC,CAACrF;EAC1B,CAAC;EAED,oBAAOf,OAAA,CAACC,WAAW,CAACoG,QAAQ;IAACH,KAAK,EAAEA,KAAM;IAAA3F,QAAA,EAAEA;EAAQ;IAAA+F,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAuB,CAAC;AAC9E,CAAC;AAACjG,GAAA,CAlZWF,YAAY;AAAAoG,EAAA,GAAZpG,YAAY;AAAA,IAAAoG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}