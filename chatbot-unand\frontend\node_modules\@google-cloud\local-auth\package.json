{"name": "@google-cloud/local-auth", "version": "3.0.1", "description": "A standalone library for receiving Google OAuth tokens", "main": "build/src/index.js", "types": "build/src/index.d.ts", "files": ["build/src"], "scripts": {"lint": "gts check", "test": "c8 mocha build/test", "presystem-test": "npm run compile", "system-test": "mocha build/system-test --timeout 60000", "compile": "tsc -p .", "fix": "gts fix", "prepare": "npm run compile", "pretest": "npm run compile", "docs": "compodoc src/", "docs-test": "linkinator docs", "predocs-test": "npm run docs", "samples-test": "cd samples/ && npm link ../ && npm test && cd ../", "prelint": "cd samples; npm link ../; npm install", "clean": "gts clean", "precompile": "gts clean", "api-extractor": "api-extractor run --local", "api-documenter": "api-documenter yaml --input-folder=temp"}, "repository": "googleapis/nodejs-local-auth", "keywords": ["google"], "engines": {"node": ">=14.0.0"}, "author": "Google, LLC", "license": "Apache-2.0", "dependencies": {"arrify": "^2.0.1", "google-auth-library": "^9.0.0", "open": "^7.0.3", "server-destroy": "^1.0.1"}, "devDependencies": {"@compodoc/compodoc": "^1.1.9", "@microsoft/api-documenter": "^7.8.10", "@microsoft/api-extractor": "^7.8.10", "@types/mocha": "^9.0.0", "@types/nock": "^10.0.0", "@types/node": "^20.4.9", "@types/proxyquire": "^1.3.28", "@types/server-destroy": "^1.0.0", "c8": "^8.0.1", "gaxios": "^6.0.4", "gts": "^5.0.0", "linkinator": "^4.0.0", "mocha": "^9.2.2", "nock": "^13.0.0", "proxyquire": "^2.1.3", "typescript": "^5.1.6"}}