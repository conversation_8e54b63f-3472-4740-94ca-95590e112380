{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website kp\\\\chatbot-unand\\\\frontend\\\\src\\\\contexts\\\\AuthContext.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from \"react\";\nimport { jwtDecode } from \"jwt-decode\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error(\"useAuth must be used within an AuthProvider\");\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [token, setToken] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Check for existing token on mount\n  useEffect(() => {\n    const savedToken = localStorage.getItem(\"access_token\");\n    const savedUser = localStorage.getItem(\"user\");\n    if (savedToken && savedUser) {\n      try {\n        // Check if token is still valid\n        const decoded = jwtDecode(savedToken);\n        const currentTime = Date.now() / 1000;\n        if (decoded.exp > currentTime) {\n          setToken(savedToken);\n          setUser(JSON.parse(savedUser));\n        } else {\n          // Token expired, clear storage\n          localStorage.removeItem(\"access_token\");\n          localStorage.removeItem(\"user\");\n        }\n      } catch (error) {\n        console.error(\"Error decoding token:\", error);\n        localStorage.removeItem(\"access_token\");\n        localStorage.removeItem(\"user\");\n      }\n    }\n    setLoading(false);\n  }, []);\n  const login = async googleToken => {\n    try {\n      console.log(\"AuthContext: Starting login process...\");\n      const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || \"http://localhost:8001\";\n      console.log(\"AuthContext: Using API URL:\", API_BASE_URL);\n      const response = await fetch(`${API_BASE_URL}/auth/google`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          token: googleToken\n        })\n      });\n      console.log(\"AuthContext: Response status:\", response.status);\n      console.log(\"AuthContext: Response headers:\", response.headers);\n      if (!response.ok) {\n        const errorText = await response.text();\n        console.error(\"AuthContext: Response error:\", errorText);\n        console.error(\"AuthContext: Full response:\", response);\n        console.error(\"AuthContext: Response status:\", response.status);\n        console.error(\"AuthContext: Response statusText:\", response.statusText);\n\n        // Try to parse error as JSON if possible\n        try {\n          const errorJson = JSON.parse(errorText);\n          console.error(\"AuthContext: Parsed error JSON:\", errorJson);\n        } catch (e) {\n          console.error(\"AuthContext: Error text is not JSON:\", errorText);\n        }\n        throw new Error(`Authentication failed: ${response.status} - ${errorText}`);\n      }\n      const data = await response.json();\n      console.log(\"AuthContext: Response data:\", data);\n\n      // Save to localStorage\n      localStorage.setItem(\"access_token\", data.access_token);\n      localStorage.setItem(\"user\", JSON.stringify(data.user));\n\n      // Update state\n      setToken(data.access_token);\n      setUser(data.user);\n      console.log(\"AuthContext: Login completed successfully\");\n      return data;\n    } catch (error) {\n      console.error(\"AuthContext: Login error:\", error);\n      throw error;\n    }\n  };\n  const logout = async () => {\n    console.log(\"AuthContext: Starting logout process...\");\n    try {\n      // Call backend logout endpoint if user is authenticated\n      if (token) {\n        const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || \"http://localhost:8000\";\n        console.log(\"AuthContext: Calling backend logout endpoint...\");\n        const response = await fetch(`${API_BASE_URL}/auth/logout`, {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${token}`\n          }\n        });\n        if (response.ok) {\n          const data = await response.json();\n          console.log(\"AuthContext: Backend logout successful:\", data);\n          console.log(`AuthContext: Deactivated ${data.sessions_deactivated} sessions`);\n        } else {\n          console.warn(\"AuthContext: Backend logout failed, but continuing with frontend logout\");\n        }\n      }\n    } catch (error) {\n      console.error(\"AuthContext: Error calling backend logout:\", error);\n      console.log(\"AuthContext: Continuing with frontend logout despite backend error\");\n    }\n\n    // Clear localStorage\n    localStorage.removeItem(\"access_token\");\n    localStorage.removeItem(\"user\");\n\n    // Clear state\n    setToken(null);\n    setUser(null);\n\n    // Sign out from Google more thoroughly\n    if (window.google && window.google.accounts) {\n      console.log(\"AuthContext: Signing out from Google...\");\n      try {\n        // Disable auto-select\n        window.google.accounts.id.disableAutoSelect();\n\n        // Revoke the current session\n        window.google.accounts.id.revoke(token, done => {\n          console.log(\"AuthContext: Google token revoked:\", done);\n        });\n\n        // Clear any Google session cookies\n        document.cookie = \"g_state=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.google.com;\";\n        document.cookie = \"g_csrf_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.google.com;\";\n      } catch (error) {\n        console.warn(\"AuthContext: Error during Google sign-out:\", error);\n      }\n    }\n    console.log(\"AuthContext: Logout completed\");\n  };\n  const forceLogout = () => {\n    console.log(\"AuthContext: Force logout - clearing all authentication data\");\n\n    // Clear localStorage immediately\n    localStorage.removeItem(\"access_token\");\n    localStorage.removeItem(\"user\");\n\n    // Clear state\n    setToken(null);\n    setUser(null);\n\n    // Clear Google session\n    if (window.google && window.google.accounts) {\n      try {\n        window.google.accounts.id.disableAutoSelect();\n      } catch (error) {\n        console.warn(\"AuthContext: Error during force Google sign-out:\", error);\n      }\n    }\n    console.log(\"AuthContext: Force logout completed\");\n  };\n  const isTokenExpired = token => {\n    if (!token) return true;\n    try {\n      // Decode JWT token to check expiry\n      const payload = JSON.parse(atob(token.split(\".\")[1]));\n      const currentTime = Math.floor(Date.now() / 1000);\n\n      // Check if token is expired (with 30 second buffer)\n      return payload.exp < currentTime + 30;\n    } catch (error) {\n      console.error(\"AuthContext: Error checking token expiry:\", error);\n      return true; // Assume expired if can't decode\n    }\n  };\n  const getAuthHeaders = () => {\n    if (token && !isTokenExpired(token)) {\n      return {\n        Authorization: `Bearer ${token}`,\n        \"Content-Type\": \"application/json\"\n      };\n    } else if (token && isTokenExpired(token)) {\n      // Token is expired, clear it\n      console.log(\"AuthContext: Token expired, clearing authentication\");\n      logout();\n      return {\n        \"Content-Type\": \"application/json\"\n      };\n    }\n    return {\n      \"Content-Type\": \"application/json\"\n    };\n  };\n  const value = {\n    user,\n    token,\n    loading,\n    login,\n    logout,\n    forceLogout,\n    getAuthHeaders,\n    isAuthenticated: !!user\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 243,\n    columnNumber: 10\n  }, this);\n};\n_s2(AuthProvider, \"uAkFQMmIUxfxJcQTEb8tCM/KFt4=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "jwtDecode", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "token", "setToken", "loading", "setLoading", "savedToken", "localStorage", "getItem", "savedUser", "decoded", "currentTime", "Date", "now", "exp", "JSON", "parse", "removeItem", "error", "console", "login", "googleToken", "log", "API_BASE_URL", "process", "env", "REACT_APP_API_BASE_URL", "response", "fetch", "method", "headers", "body", "stringify", "status", "ok", "errorText", "text", "statusText", "<PERSON><PERSON><PERSON>", "e", "data", "json", "setItem", "access_token", "logout", "Authorization", "sessions_deactivated", "warn", "window", "google", "accounts", "id", "disableAutoSelect", "revoke", "done", "document", "cookie", "forceLogout", "isTokenExpired", "payload", "atob", "split", "Math", "floor", "getAuthHeaders", "value", "isAuthenticated", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/website kp/chatbot-unand/frontend/src/contexts/AuthContext.jsx"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from \"react\";\nimport { jwtDecode } from \"jwt-decode\";\n\nconst AuthContext = createContext();\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error(\"useAuth must be used within an AuthProvider\");\n  }\n  return context;\n};\n\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [token, setToken] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Check for existing token on mount\n  useEffect(() => {\n    const savedToken = localStorage.getItem(\"access_token\");\n    const savedUser = localStorage.getItem(\"user\");\n\n    if (savedToken && savedUser) {\n      try {\n        // Check if token is still valid\n        const decoded = jwtDecode(savedToken);\n        const currentTime = Date.now() / 1000;\n\n        if (decoded.exp > currentTime) {\n          setToken(savedToken);\n          setUser(JSON.parse(savedUser));\n        } else {\n          // Token expired, clear storage\n          localStorage.removeItem(\"access_token\");\n          localStorage.removeItem(\"user\");\n        }\n      } catch (error) {\n        console.error(\"Error decoding token:\", error);\n        localStorage.removeItem(\"access_token\");\n        localStorage.removeItem(\"user\");\n      }\n    }\n\n    setLoading(false);\n  }, []);\n\n  const login = async (googleToken) => {\n    try {\n      console.log(\"AuthContext: Starting login process...\");\n      const API_BASE_URL =\n        process.env.REACT_APP_API_BASE_URL || \"http://localhost:8001\";\n      console.log(\"AuthContext: Using API URL:\", API_BASE_URL);\n      const response = await fetch(`${API_BASE_URL}/auth/google`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify({ token: googleToken }),\n      });\n\n      console.log(\"AuthContext: Response status:\", response.status);\n      console.log(\"AuthContext: Response headers:\", response.headers);\n\n      if (!response.ok) {\n        const errorText = await response.text();\n        console.error(\"AuthContext: Response error:\", errorText);\n        console.error(\"AuthContext: Full response:\", response);\n        console.error(\"AuthContext: Response status:\", response.status);\n        console.error(\"AuthContext: Response statusText:\", response.statusText);\n\n        // Try to parse error as JSON if possible\n        try {\n          const errorJson = JSON.parse(errorText);\n          console.error(\"AuthContext: Parsed error JSON:\", errorJson);\n        } catch (e) {\n          console.error(\"AuthContext: Error text is not JSON:\", errorText);\n        }\n\n        throw new Error(\n          `Authentication failed: ${response.status} - ${errorText}`\n        );\n      }\n\n      const data = await response.json();\n      console.log(\"AuthContext: Response data:\", data);\n\n      // Save to localStorage\n      localStorage.setItem(\"access_token\", data.access_token);\n      localStorage.setItem(\"user\", JSON.stringify(data.user));\n\n      // Update state\n      setToken(data.access_token);\n      setUser(data.user);\n\n      console.log(\"AuthContext: Login completed successfully\");\n      return data;\n    } catch (error) {\n      console.error(\"AuthContext: Login error:\", error);\n      throw error;\n    }\n  };\n\n  const logout = async () => {\n    console.log(\"AuthContext: Starting logout process...\");\n\n    try {\n      // Call backend logout endpoint if user is authenticated\n      if (token) {\n        const API_BASE_URL =\n          process.env.REACT_APP_API_BASE_URL || \"http://localhost:8000\";\n        console.log(\"AuthContext: Calling backend logout endpoint...\");\n\n        const response = await fetch(`${API_BASE_URL}/auth/logout`, {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${token}`,\n          },\n        });\n\n        if (response.ok) {\n          const data = await response.json();\n          console.log(\"AuthContext: Backend logout successful:\", data);\n          console.log(\n            `AuthContext: Deactivated ${data.sessions_deactivated} sessions`\n          );\n        } else {\n          console.warn(\n            \"AuthContext: Backend logout failed, but continuing with frontend logout\"\n          );\n        }\n      }\n    } catch (error) {\n      console.error(\"AuthContext: Error calling backend logout:\", error);\n      console.log(\n        \"AuthContext: Continuing with frontend logout despite backend error\"\n      );\n    }\n\n    // Clear localStorage\n    localStorage.removeItem(\"access_token\");\n    localStorage.removeItem(\"user\");\n\n    // Clear state\n    setToken(null);\n    setUser(null);\n\n    // Sign out from Google more thoroughly\n    if (window.google && window.google.accounts) {\n      console.log(\"AuthContext: Signing out from Google...\");\n      try {\n        // Disable auto-select\n        window.google.accounts.id.disableAutoSelect();\n\n        // Revoke the current session\n        window.google.accounts.id.revoke(token, (done) => {\n          console.log(\"AuthContext: Google token revoked:\", done);\n        });\n\n        // Clear any Google session cookies\n        document.cookie =\n          \"g_state=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.google.com;\";\n        document.cookie =\n          \"g_csrf_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.google.com;\";\n      } catch (error) {\n        console.warn(\"AuthContext: Error during Google sign-out:\", error);\n      }\n    }\n\n    console.log(\"AuthContext: Logout completed\");\n  };\n\n  const forceLogout = () => {\n    console.log(\"AuthContext: Force logout - clearing all authentication data\");\n\n    // Clear localStorage immediately\n    localStorage.removeItem(\"access_token\");\n    localStorage.removeItem(\"user\");\n\n    // Clear state\n    setToken(null);\n    setUser(null);\n\n    // Clear Google session\n    if (window.google && window.google.accounts) {\n      try {\n        window.google.accounts.id.disableAutoSelect();\n      } catch (error) {\n        console.warn(\"AuthContext: Error during force Google sign-out:\", error);\n      }\n    }\n\n    console.log(\"AuthContext: Force logout completed\");\n  };\n\n  const isTokenExpired = (token) => {\n    if (!token) return true;\n\n    try {\n      // Decode JWT token to check expiry\n      const payload = JSON.parse(atob(token.split(\".\")[1]));\n      const currentTime = Math.floor(Date.now() / 1000);\n\n      // Check if token is expired (with 30 second buffer)\n      return payload.exp < currentTime + 30;\n    } catch (error) {\n      console.error(\"AuthContext: Error checking token expiry:\", error);\n      return true; // Assume expired if can't decode\n    }\n  };\n\n  const getAuthHeaders = () => {\n    if (token && !isTokenExpired(token)) {\n      return {\n        Authorization: `Bearer ${token}`,\n        \"Content-Type\": \"application/json\",\n      };\n    } else if (token && isTokenExpired(token)) {\n      // Token is expired, clear it\n      console.log(\"AuthContext: Token expired, clearing authentication\");\n      logout();\n      return {\n        \"Content-Type\": \"application/json\",\n      };\n    }\n    return {\n      \"Content-Type\": \"application/json\",\n    };\n  };\n\n  const value = {\n    user,\n    token,\n    loading,\n    login,\n    logout,\n    forceLogout,\n    getAuthHeaders,\n    isAuthenticated: !!user,\n  };\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,SAASC,SAAS,QAAQ,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,WAAW,gBAAGP,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMQ,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGT,UAAU,CAACM,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACe,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACAC,SAAS,CAAC,MAAM;IACd,MAAMkB,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;IACvD,MAAMC,SAAS,GAAGF,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IAE9C,IAAIF,UAAU,IAAIG,SAAS,EAAE;MAC3B,IAAI;QACF;QACA,MAAMC,OAAO,GAAGrB,SAAS,CAACiB,UAAU,CAAC;QACrC,MAAMK,WAAW,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI;QAErC,IAAIH,OAAO,CAACI,GAAG,GAAGH,WAAW,EAAE;UAC7BR,QAAQ,CAACG,UAAU,CAAC;UACpBL,OAAO,CAACc,IAAI,CAACC,KAAK,CAACP,SAAS,CAAC,CAAC;QAChC,CAAC,MAAM;UACL;UACAF,YAAY,CAACU,UAAU,CAAC,cAAc,CAAC;UACvCV,YAAY,CAACU,UAAU,CAAC,MAAM,CAAC;QACjC;MACF,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7CX,YAAY,CAACU,UAAU,CAAC,cAAc,CAAC;QACvCV,YAAY,CAACU,UAAU,CAAC,MAAM,CAAC;MACjC;IACF;IAEAZ,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMe,KAAK,GAAG,MAAOC,WAAW,IAAK;IACnC,IAAI;MACFF,OAAO,CAACG,GAAG,CAAC,wCAAwC,CAAC;MACrD,MAAMC,YAAY,GAChBC,OAAO,CAACC,GAAG,CAACC,sBAAsB,IAAI,uBAAuB;MAC/DP,OAAO,CAACG,GAAG,CAAC,6BAA6B,EAAEC,YAAY,CAAC;MACxD,MAAMI,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGL,YAAY,cAAc,EAAE;QAC1DM,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEhB,IAAI,CAACiB,SAAS,CAAC;UAAE9B,KAAK,EAAEmB;QAAY,CAAC;MAC7C,CAAC,CAAC;MAEFF,OAAO,CAACG,GAAG,CAAC,+BAA+B,EAAEK,QAAQ,CAACM,MAAM,CAAC;MAC7Dd,OAAO,CAACG,GAAG,CAAC,gCAAgC,EAAEK,QAAQ,CAACG,OAAO,CAAC;MAE/D,IAAI,CAACH,QAAQ,CAACO,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAMR,QAAQ,CAACS,IAAI,CAAC,CAAC;QACvCjB,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEiB,SAAS,CAAC;QACxDhB,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAES,QAAQ,CAAC;QACtDR,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAES,QAAQ,CAACM,MAAM,CAAC;QAC/Dd,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAES,QAAQ,CAACU,UAAU,CAAC;;QAEvE;QACA,IAAI;UACF,MAAMC,SAAS,GAAGvB,IAAI,CAACC,KAAK,CAACmB,SAAS,CAAC;UACvChB,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEoB,SAAS,CAAC;QAC7D,CAAC,CAAC,OAAOC,CAAC,EAAE;UACVpB,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEiB,SAAS,CAAC;QAClE;QAEA,MAAM,IAAIvC,KAAK,CACb,0BAA0B+B,QAAQ,CAACM,MAAM,MAAME,SAAS,EAC1D,CAAC;MACH;MAEA,MAAMK,IAAI,GAAG,MAAMb,QAAQ,CAACc,IAAI,CAAC,CAAC;MAClCtB,OAAO,CAACG,GAAG,CAAC,6BAA6B,EAAEkB,IAAI,CAAC;;MAEhD;MACAjC,YAAY,CAACmC,OAAO,CAAC,cAAc,EAAEF,IAAI,CAACG,YAAY,CAAC;MACvDpC,YAAY,CAACmC,OAAO,CAAC,MAAM,EAAE3B,IAAI,CAACiB,SAAS,CAACQ,IAAI,CAACxC,IAAI,CAAC,CAAC;;MAEvD;MACAG,QAAQ,CAACqC,IAAI,CAACG,YAAY,CAAC;MAC3B1C,OAAO,CAACuC,IAAI,CAACxC,IAAI,CAAC;MAElBmB,OAAO,CAACG,GAAG,CAAC,2CAA2C,CAAC;MACxD,OAAOkB,IAAI;IACb,CAAC,CAAC,OAAOtB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAM0B,MAAM,GAAG,MAAAA,CAAA,KAAY;IACzBzB,OAAO,CAACG,GAAG,CAAC,yCAAyC,CAAC;IAEtD,IAAI;MACF;MACA,IAAIpB,KAAK,EAAE;QACT,MAAMqB,YAAY,GAChBC,OAAO,CAACC,GAAG,CAACC,sBAAsB,IAAI,uBAAuB;QAC/DP,OAAO,CAACG,GAAG,CAAC,iDAAiD,CAAC;QAE9D,MAAMK,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGL,YAAY,cAAc,EAAE;UAC1DM,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClCe,aAAa,EAAE,UAAU3C,KAAK;UAChC;QACF,CAAC,CAAC;QAEF,IAAIyB,QAAQ,CAACO,EAAE,EAAE;UACf,MAAMM,IAAI,GAAG,MAAMb,QAAQ,CAACc,IAAI,CAAC,CAAC;UAClCtB,OAAO,CAACG,GAAG,CAAC,yCAAyC,EAAEkB,IAAI,CAAC;UAC5DrB,OAAO,CAACG,GAAG,CACT,4BAA4BkB,IAAI,CAACM,oBAAoB,WACvD,CAAC;QACH,CAAC,MAAM;UACL3B,OAAO,CAAC4B,IAAI,CACV,yEACF,CAAC;QACH;MACF;IACF,CAAC,CAAC,OAAO7B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;MAClEC,OAAO,CAACG,GAAG,CACT,oEACF,CAAC;IACH;;IAEA;IACAf,YAAY,CAACU,UAAU,CAAC,cAAc,CAAC;IACvCV,YAAY,CAACU,UAAU,CAAC,MAAM,CAAC;;IAE/B;IACAd,QAAQ,CAAC,IAAI,CAAC;IACdF,OAAO,CAAC,IAAI,CAAC;;IAEb;IACA,IAAI+C,MAAM,CAACC,MAAM,IAAID,MAAM,CAACC,MAAM,CAACC,QAAQ,EAAE;MAC3C/B,OAAO,CAACG,GAAG,CAAC,yCAAyC,CAAC;MACtD,IAAI;QACF;QACA0B,MAAM,CAACC,MAAM,CAACC,QAAQ,CAACC,EAAE,CAACC,iBAAiB,CAAC,CAAC;;QAE7C;QACAJ,MAAM,CAACC,MAAM,CAACC,QAAQ,CAACC,EAAE,CAACE,MAAM,CAACnD,KAAK,EAAGoD,IAAI,IAAK;UAChDnC,OAAO,CAACG,GAAG,CAAC,oCAAoC,EAAEgC,IAAI,CAAC;QACzD,CAAC,CAAC;;QAEF;QACAC,QAAQ,CAACC,MAAM,GACb,8EAA8E;QAChFD,QAAQ,CAACC,MAAM,GACb,mFAAmF;MACvF,CAAC,CAAC,OAAOtC,KAAK,EAAE;QACdC,OAAO,CAAC4B,IAAI,CAAC,4CAA4C,EAAE7B,KAAK,CAAC;MACnE;IACF;IAEAC,OAAO,CAACG,GAAG,CAAC,+BAA+B,CAAC;EAC9C,CAAC;EAED,MAAMmC,WAAW,GAAGA,CAAA,KAAM;IACxBtC,OAAO,CAACG,GAAG,CAAC,8DAA8D,CAAC;;IAE3E;IACAf,YAAY,CAACU,UAAU,CAAC,cAAc,CAAC;IACvCV,YAAY,CAACU,UAAU,CAAC,MAAM,CAAC;;IAE/B;IACAd,QAAQ,CAAC,IAAI,CAAC;IACdF,OAAO,CAAC,IAAI,CAAC;;IAEb;IACA,IAAI+C,MAAM,CAACC,MAAM,IAAID,MAAM,CAACC,MAAM,CAACC,QAAQ,EAAE;MAC3C,IAAI;QACFF,MAAM,CAACC,MAAM,CAACC,QAAQ,CAACC,EAAE,CAACC,iBAAiB,CAAC,CAAC;MAC/C,CAAC,CAAC,OAAOlC,KAAK,EAAE;QACdC,OAAO,CAAC4B,IAAI,CAAC,kDAAkD,EAAE7B,KAAK,CAAC;MACzE;IACF;IAEAC,OAAO,CAACG,GAAG,CAAC,qCAAqC,CAAC;EACpD,CAAC;EAED,MAAMoC,cAAc,GAAIxD,KAAK,IAAK;IAChC,IAAI,CAACA,KAAK,EAAE,OAAO,IAAI;IAEvB,IAAI;MACF;MACA,MAAMyD,OAAO,GAAG5C,IAAI,CAACC,KAAK,CAAC4C,IAAI,CAAC1D,KAAK,CAAC2D,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrD,MAAMlD,WAAW,GAAGmD,IAAI,CAACC,KAAK,CAACnD,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;;MAEjD;MACA,OAAO8C,OAAO,CAAC7C,GAAG,GAAGH,WAAW,GAAG,EAAE;IACvC,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACjE,OAAO,IAAI,CAAC,CAAC;IACf;EACF,CAAC;EAED,MAAM8C,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI9D,KAAK,IAAI,CAACwD,cAAc,CAACxD,KAAK,CAAC,EAAE;MACnC,OAAO;QACL2C,aAAa,EAAE,UAAU3C,KAAK,EAAE;QAChC,cAAc,EAAE;MAClB,CAAC;IACH,CAAC,MAAM,IAAIA,KAAK,IAAIwD,cAAc,CAACxD,KAAK,CAAC,EAAE;MACzC;MACAiB,OAAO,CAACG,GAAG,CAAC,qDAAqD,CAAC;MAClEsB,MAAM,CAAC,CAAC;MACR,OAAO;QACL,cAAc,EAAE;MAClB,CAAC;IACH;IACA,OAAO;MACL,cAAc,EAAE;IAClB,CAAC;EACH,CAAC;EAED,MAAMqB,KAAK,GAAG;IACZjE,IAAI;IACJE,KAAK;IACLE,OAAO;IACPgB,KAAK;IACLwB,MAAM;IACNa,WAAW;IACXO,cAAc;IACdE,eAAe,EAAE,CAAC,CAAClE;EACrB,CAAC;EAED,oBAAOT,OAAA,CAACC,WAAW,CAAC2E,QAAQ;IAACF,KAAK,EAAEA,KAAM;IAAAnE,QAAA,EAAEA;EAAQ;IAAAsE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAuB,CAAC;AAC9E,CAAC;AAACxE,GAAA,CAtOWF,YAAY;AAAA2E,EAAA,GAAZ3E,YAAY;AAAA,IAAA2E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}