{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M11 14h10\",\n  key: \"1w8e9d\"\n}], [\"path\", {\n  d: \"M16 4h2a2 2 0 0 1 2 2v1.344\",\n  key: \"1e62lh\"\n}], [\"path\", {\n  d: \"m17 18 4-4-4-4\",\n  key: \"z2g111\"\n}], [\"path\", {\n  d: \"M8 4H6a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h12a2 2 0 0 0 1.793-1.113\",\n  key: \"bjbb7m\"\n}], [\"rect\", {\n  x: \"8\",\n  y: \"2\",\n  width: \"8\",\n  height: \"4\",\n  rx: \"1\",\n  key: \"ublpy\"\n}]];\nconst ClipboardPaste = createLucideIcon(\"clipboard-paste\", __iconNode);\nexport { __iconNode, ClipboardPaste as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "x", "y", "width", "height", "rx", "ClipboardPaste", "createLucideIcon"], "sources": ["D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\node_modules\\lucide-react\\src\\icons\\clipboard-paste.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M11 14h10', key: '1w8e9d' }],\n  ['path', { d: 'M16 4h2a2 2 0 0 1 2 2v1.344', key: '1e62lh' }],\n  ['path', { d: 'm17 18 4-4-4-4', key: 'z2g111' }],\n  ['path', { d: 'M8 4H6a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h12a2 2 0 0 0 1.793-1.113', key: 'bjbb7m' }],\n  ['rect', { x: '8', y: '2', width: '8', height: '4', rx: '1', key: 'ublpy' }],\n];\n\n/**\n * @component @name ClipboardPaste\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTEgMTRoMTAiIC8+CiAgPHBhdGggZD0iTTE2IDRoMmEyIDIgMCAwIDEgMiAydjEuMzQ0IiAvPgogIDxwYXRoIGQ9Im0xNyAxOCA0LTQtNC00IiAvPgogIDxwYXRoIGQ9Ik04IDRINmEyIDIgMCAwIDAtMiAydjE0YTIgMiAwIDAgMCAyIDJoMTJhMiAyIDAgMCAwIDEuNzkzLTEuMTEzIiAvPgogIDxyZWN0IHg9IjgiIHk9IjIiIHdpZHRoPSI4IiBoZWlnaHQ9IjQiIHJ4PSIxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/clipboard-paste\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ClipboardPaste = createLucideIcon('clipboard-paste', __iconNode);\n\nexport default ClipboardPaste;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,6BAA+B;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC5D,CAAC,MAAQ;EAAED,CAAA,EAAG,gBAAkB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/C,CAAC,MAAQ;EAAED,CAAA,EAAG,gEAAkE;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/F,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAKC,CAAG;EAAKC,KAAO;EAAKC,MAAA,EAAQ,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAL,GAAA,EAAK;AAAS,GAC7E;AAaM,MAAAM,cAAA,GAAiBC,gBAAiB,oBAAmBT,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}