{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website kp\\\\chatbot-unand\\\\frontend\\\\src\\\\components\\\\UserProfile.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { useAuth } from \"../contexts/AuthContext\";\nimport { User, LogOut, ChevronDown } from \"lucide-react\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst UserProfile = () => {\n  _s();\n  const {\n    user,\n    logout\n  } = useAuth();\n  const [isOpen, setIsOpen] = useState(false);\n  if (!user) return null;\n  const handleLogout = async () => {\n    try {\n      await logout();\n      setIsOpen(false);\n    } catch (error) {\n      console.error(\"Error during logout:\", error);\n      // Still close the dropdown even if logout fails\n      setIsOpen(false);\n    }\n  };\n  const handleForceLogout = () => {\n    try {\n      forceLogout();\n    } catch (error) {\n      console.error(\"Error during force logout:\", error);\n      // Force reload as fallback\n      window.location.reload();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"relative\",\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: () => setIsOpen(!isOpen),\n      className: \"flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n      children: [user.picture ? /*#__PURE__*/_jsxDEV(\"img\", {\n        src: user.picture,\n        alt: user.name,\n        className: \"w-8 h-8 rounded-full border-2 border-green-500\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center\",\n        children: /*#__PURE__*/_jsxDEV(User, {\n          className: \"w-4 h-4 text-white\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hidden md:block text-left\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm font-medium text-gray-900 dark:text-white\",\n          children: user.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xs text-gray-500 dark:text-gray-400\",\n          children: user.email\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ChevronDown, {\n        className: `w-4 h-4 text-gray-500 transition-transform ${isOpen ? \"rotate-180\" : \"\"}`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 7\n    }, this), isOpen && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 z-10\",\n        onClick: () => setIsOpen(false)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute right-0 mt-2 w-64 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-20\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4 border-b border-gray-200 dark:border-gray-700\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [user.picture ? /*#__PURE__*/_jsxDEV(\"img\", {\n              src: user.picture,\n              alt: user.name,\n              className: \"w-12 h-12 rounded-full border-2 border-green-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-green-500 rounded-full flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(User, {\n                className: \"w-6 h-6 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 87,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1 min-w-0\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-900 dark:text-white truncate\",\n                children: user.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 91,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-500 dark:text-gray-400 truncate\",\n                children: user.email\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 94,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"py-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleLogout,\n            className: \"w-full flex items-center px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors\",\n            children: [/*#__PURE__*/_jsxDEV(LogOut, {\n              className: \"w-4 h-4 mr-3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 17\n            }, this), \"Keluar\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleForceLogout,\n            className: \"w-full flex items-center px-4 py-2 text-sm text-orange-600 dark:text-orange-400 hover:bg-orange-50 dark:hover:bg-orange-900/20 transition-colors border-t border-gray-200 dark:border-gray-700\",\n            title: \"Gunakan jika login bermasalah\",\n            children: [/*#__PURE__*/_jsxDEV(LogOut, {\n              className: \"w-4 h-4 mr-3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 17\n            }, this), \"Force Logout (Debug)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 33,\n    columnNumber: 5\n  }, this);\n};\n_s(UserProfile, \"aCT5CBLv1CC5S2TixZ20AzxBJgs=\", false, function () {\n  return [useAuth];\n});\n_c = UserProfile;\nexport default UserProfile;\nvar _c;\n$RefreshReg$(_c, \"UserProfile\");", "map": {"version": 3, "names": ["React", "useState", "useAuth", "User", "LogOut", "ChevronDown", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "UserProfile", "_s", "user", "logout", "isOpen", "setIsOpen", "handleLogout", "error", "console", "handleForceLogout", "forceLogout", "window", "location", "reload", "className", "children", "onClick", "picture", "src", "alt", "name", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "email", "title", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/website kp/chatbot-unand/frontend/src/components/UserProfile.jsx"], "sourcesContent": ["import React, { useState } from \"react\";\nimport { useAuth } from \"../contexts/AuthContext\";\nimport { User, LogOut, ChevronDown } from \"lucide-react\";\n\nconst UserProfile = () => {\n  const { user, logout } = useAuth();\n  const [isOpen, setIsOpen] = useState(false);\n\n  if (!user) return null;\n\n  const handleLogout = async () => {\n    try {\n      await logout();\n      setIsOpen(false);\n    } catch (error) {\n      console.error(\"Error during logout:\", error);\n      // Still close the dropdown even if logout fails\n      setIsOpen(false);\n    }\n  };\n\n  const handleForceLogout = () => {\n    try {\n      forceLogout();\n    } catch (error) {\n      console.error(\"Error during force logout:\", error);\n      // Force reload as fallback\n      window.location.reload();\n    }\n  };\n\n  return (\n    <div className=\"relative\">\n      {/* User Button */}\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n      >\n        {user.picture ? (\n          <img\n            src={user.picture}\n            alt={user.name}\n            className=\"w-8 h-8 rounded-full border-2 border-green-500\"\n          />\n        ) : (\n          <div className=\"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center\">\n            <User className=\"w-4 h-4 text-white\" />\n          </div>\n        )}\n        <div className=\"hidden md:block text-left\">\n          <p className=\"text-sm font-medium text-gray-900 dark:text-white\">\n            {user.name}\n          </p>\n          <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n            {user.email}\n          </p>\n        </div>\n        <ChevronDown\n          className={`w-4 h-4 text-gray-500 transition-transform ${\n            isOpen ? \"rotate-180\" : \"\"\n          }`}\n        />\n      </button>\n\n      {/* Dropdown Menu */}\n      {isOpen && (\n        <>\n          {/* Backdrop */}\n          <div\n            className=\"fixed inset-0 z-10\"\n            onClick={() => setIsOpen(false)}\n          />\n\n          {/* Menu */}\n          <div className=\"absolute right-0 mt-2 w-64 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-20\">\n            {/* User Info */}\n            <div className=\"p-4 border-b border-gray-200 dark:border-gray-700\">\n              <div className=\"flex items-center space-x-3\">\n                {user.picture ? (\n                  <img\n                    src={user.picture}\n                    alt={user.name}\n                    className=\"w-12 h-12 rounded-full border-2 border-green-500\"\n                  />\n                ) : (\n                  <div className=\"w-12 h-12 bg-green-500 rounded-full flex items-center justify-center\">\n                    <User className=\"w-6 h-6 text-white\" />\n                  </div>\n                )}\n                <div className=\"flex-1 min-w-0\">\n                  <p className=\"text-sm font-medium text-gray-900 dark:text-white truncate\">\n                    {user.name}\n                  </p>\n                  <p className=\"text-xs text-gray-500 dark:text-gray-400 truncate\">\n                    {user.email}\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            {/* Menu Items */}\n            <div className=\"py-2\">\n              <button\n                onClick={handleLogout}\n                className=\"w-full flex items-center px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors\"\n              >\n                <LogOut className=\"w-4 h-4 mr-3\" />\n                Keluar\n              </button>\n\n              {/* Force Logout Button for troubleshooting */}\n              <button\n                onClick={handleForceLogout}\n                className=\"w-full flex items-center px-4 py-2 text-sm text-orange-600 dark:text-orange-400 hover:bg-orange-50 dark:hover:bg-orange-900/20 transition-colors border-t border-gray-200 dark:border-gray-700\"\n                title=\"Gunakan jika login bermasalah\"\n              >\n                <LogOut className=\"w-4 h-4 mr-3\" />\n                Force Logout (Debug)\n              </button>\n            </div>\n          </div>\n        </>\n      )}\n    </div>\n  );\n};\n\nexport default UserProfile;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,IAAI,EAAEC,MAAM,EAAEC,WAAW,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEzD,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM;IAAEC,IAAI;IAAEC;EAAO,CAAC,GAAGX,OAAO,CAAC,CAAC;EAClC,MAAM,CAACY,MAAM,EAAEC,SAAS,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAE3C,IAAI,CAACW,IAAI,EAAE,OAAO,IAAI;EAEtB,MAAMI,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMH,MAAM,CAAC,CAAC;MACdE,SAAS,CAAC,KAAK,CAAC;IAClB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C;MACAF,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;EAED,MAAMI,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI;MACFC,WAAW,CAAC,CAAC;IACf,CAAC,CAAC,OAAOH,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD;MACAI,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;IAC1B;EACF,CAAC;EAED,oBACEhB,OAAA;IAAKiB,SAAS,EAAC,UAAU;IAAAC,QAAA,gBAEvBlB,OAAA;MACEmB,OAAO,EAAEA,CAAA,KAAMX,SAAS,CAAC,CAACD,MAAM,CAAE;MAClCU,SAAS,EAAC,uGAAuG;MAAAC,QAAA,GAEhHb,IAAI,CAACe,OAAO,gBACXpB,OAAA;QACEqB,GAAG,EAAEhB,IAAI,CAACe,OAAQ;QAClBE,GAAG,EAAEjB,IAAI,CAACkB,IAAK;QACfN,SAAS,EAAC;MAAgD;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC,gBAEF3B,OAAA;QAAKiB,SAAS,EAAC,oEAAoE;QAAAC,QAAA,eACjFlB,OAAA,CAACJ,IAAI;UAACqB,SAAS,EAAC;QAAoB;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CACN,eACD3B,OAAA;QAAKiB,SAAS,EAAC,2BAA2B;QAAAC,QAAA,gBACxClB,OAAA;UAAGiB,SAAS,EAAC,mDAAmD;UAAAC,QAAA,EAC7Db,IAAI,CAACkB;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACJ3B,OAAA;UAAGiB,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EACpDb,IAAI,CAACuB;QAAK;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACN3B,OAAA,CAACF,WAAW;QACVmB,SAAS,EAAE,8CACTV,MAAM,GAAG,YAAY,GAAG,EAAE;MACzB;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,EAGRpB,MAAM,iBACLP,OAAA,CAAAE,SAAA;MAAAgB,QAAA,gBAEElB,OAAA;QACEiB,SAAS,EAAC,oBAAoB;QAC9BE,OAAO,EAAEA,CAAA,KAAMX,SAAS,CAAC,KAAK;MAAE;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,eAGF3B,OAAA;QAAKiB,SAAS,EAAC,4HAA4H;QAAAC,QAAA,gBAEzIlB,OAAA;UAAKiB,SAAS,EAAC,mDAAmD;UAAAC,QAAA,eAChElB,OAAA;YAAKiB,SAAS,EAAC,6BAA6B;YAAAC,QAAA,GACzCb,IAAI,CAACe,OAAO,gBACXpB,OAAA;cACEqB,GAAG,EAAEhB,IAAI,CAACe,OAAQ;cAClBE,GAAG,EAAEjB,IAAI,CAACkB,IAAK;cACfN,SAAS,EAAC;YAAkD;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC,gBAEF3B,OAAA;cAAKiB,SAAS,EAAC,sEAAsE;cAAAC,QAAA,eACnFlB,OAAA,CAACJ,IAAI;gBAACqB,SAAS,EAAC;cAAoB;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CACN,eACD3B,OAAA;cAAKiB,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BlB,OAAA;gBAAGiB,SAAS,EAAC,4DAA4D;gBAAAC,QAAA,EACtEb,IAAI,CAACkB;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACJ3B,OAAA;gBAAGiB,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,EAC7Db,IAAI,CAACuB;cAAK;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN3B,OAAA;UAAKiB,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBlB,OAAA;YACEmB,OAAO,EAAEV,YAAa;YACtBQ,SAAS,EAAC,sIAAsI;YAAAC,QAAA,gBAEhJlB,OAAA,CAACH,MAAM;cAACoB,SAAS,EAAC;YAAc;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,UAErC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAGT3B,OAAA;YACEmB,OAAO,EAAEP,iBAAkB;YAC3BK,SAAS,EAAC,gMAAgM;YAC1MY,KAAK,EAAC,+BAA+B;YAAAX,QAAA,gBAErClB,OAAA,CAACH,MAAM;cAACoB,SAAS,EAAC;YAAc;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,wBAErC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA,eACN,CACH;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACvB,EAAA,CAzHID,WAAW;EAAA,QACUR,OAAO;AAAA;AAAAmC,EAAA,GAD5B3B,WAAW;AA2HjB,eAAeA,WAAW;AAAC,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}