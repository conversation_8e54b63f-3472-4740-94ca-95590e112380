{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M7 22a5 5 0 0 1-2-4\",\n  key: \"umushi\"\n}], [\"path\", {\n  d: \"M7 16.93c.96.43 1.96.74 2.99.91\",\n  key: \"ybbtv3\"\n}], [\"path\", {\n  d: \"M3.34 14A6.8 6.8 0 0 1 2 10c0-4.42 4.48-8 10-8s10 3.58 10 8a7.19 7.19 0 0 1-.33 2\",\n  key: \"gt5e1w\"\n}], [\"path\", {\n  d: \"M5 18a2 2 0 1 0 0-4 2 2 0 0 0 0 4z\",\n  key: \"bq3ynw\"\n}], [\"path\", {\n  d: \"M14.33 22h-.09a.35.35 0 0 1-.24-.32v-10a.34.34 0 0 1 .33-.34c.08 0 .15.03.21.08l7.34 6a.33.33 0 0 1-.21.59h-4.49l-2.57 3.85a.35.35 0 0 1-.28.14z\",\n  key: \"72q637\"\n}]];\nconst LassoSelect = createLucideIcon(\"lasso-select\", __iconNode);\nexport { __iconNode, LassoSelect as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "LassoSelect", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\node_modules\\lucide-react\\src\\icons\\lasso-select.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M7 22a5 5 0 0 1-2-4', key: 'umushi' }],\n  ['path', { d: 'M7 16.93c.96.43 1.96.74 2.99.91', key: 'ybbtv3' }],\n  [\n    'path',\n    {\n      d: 'M3.34 14A6.8 6.8 0 0 1 2 10c0-4.42 4.48-8 10-8s10 3.58 10 8a7.19 7.19 0 0 1-.33 2',\n      key: 'gt5e1w',\n    },\n  ],\n  ['path', { d: 'M5 18a2 2 0 1 0 0-4 2 2 0 0 0 0 4z', key: 'bq3ynw' }],\n  [\n    'path',\n    {\n      d: 'M14.33 22h-.09a.35.35 0 0 1-.24-.32v-10a.34.34 0 0 1 .33-.34c.08 0 .15.03.21.08l7.34 6a.33.33 0 0 1-.21.59h-4.49l-2.57 3.85a.35.35 0 0 1-.28.14z',\n      key: '72q637',\n    },\n  ],\n];\n\n/**\n * @component @name LassoSelect\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNyAyMmE1IDUgMCAwIDEtMi00IiAvPgogIDxwYXRoIGQ9Ik03IDE2LjkzYy45Ni40MyAxLjk2Ljc0IDIuOTkuOTEiIC8+CiAgPHBhdGggZD0iTTMuMzQgMTRBNi44IDYuOCAwIDAgMSAyIDEwYzAtNC40MiA0LjQ4LTggMTAtOHMxMCAzLjU4IDEwIDhhNy4xOSA3LjE5IDAgMCAxLS4zMyAyIiAvPgogIDxwYXRoIGQ9Ik01IDE4YTIgMiAwIDEgMCAwLTQgMiAyIDAgMCAwIDAgNHoiIC8+CiAgPHBhdGggZD0iTTE0LjMzIDIyaC0uMDlhLjM1LjM1IDAgMCAxLS4yNC0uMzJ2LTEwYS4zNC4zNCAwIDAgMSAuMzMtLjM0Yy4wOCAwIC4xNS4wMy4yMS4wOGw3LjM0IDZhLjMzLjMzIDAgMCAxLS4yMS41OWgtNC40OWwtMi41NyAzLjg1YS4zNS4zNSAwIDAgMS0uMjguMTR6IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/lasso-select\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst LassoSelect = createLucideIcon('lasso-select', __iconNode);\n\nexport default LassoSelect;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,qBAAuB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACpD,CAAC,MAAQ;EAAED,CAAA,EAAG,iCAAmC;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChE,CACE,QACA;EACED,CAAG;EACHC,GAAK;AAAA,EAET,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,oCAAsC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACnE,CACE,QACA;EACED,CAAG;EACHC,GAAK;AAAA,EACP,CAEJ;AAaM,MAAAC,WAAA,GAAcC,gBAAiB,iBAAgBJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}