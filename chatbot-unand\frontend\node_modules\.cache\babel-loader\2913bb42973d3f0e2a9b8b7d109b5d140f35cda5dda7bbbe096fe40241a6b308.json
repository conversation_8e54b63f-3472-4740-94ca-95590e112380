{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website kp\\\\chatbot-unand\\\\frontend\\\\src\\\\components\\\\Login.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef, useCallback } from \"react\";\nimport { useAuth } from \"../contexts/AuthContext\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Login = () => {\n  _s();\n  const {\n    login\n  } = useAuth();\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const googleButtonRef = useRef(null);\n\n  // Initialize Google Sign-In function (moved outside useEffect to be accessible)\n  const initializeGoogleSignIn = () => {\n    if (window.google && window.google.accounts) {\n      console.log(\"✅ Initializing Google Sign-In...\");\n      console.log(\"🔑 Client ID:\", process.env.REACT_APP_GOOGLE_CLIENT_ID);\n      console.log(\"🌐 Current origin:\", window.location.origin);\n\n      // Clear any existing Google session first\n      try {\n        window.google.accounts.id.disableAutoSelect();\n        console.log(\"🧹 Cleared existing Google auto-select\");\n      } catch (e) {\n        console.log(\"ℹ️ No existing Google session to clear\");\n      }\n      window.google.accounts.id.initialize({\n        client_id: process.env.REACT_APP_GOOGLE_CLIENT_ID,\n        callback: handleCredentialResponse,\n        auto_select: false,\n        cancel_on_tap_outside: true,\n        use_fedcm_for_prompt: false,\n        // Disable FedCM to avoid conflicts\n        itp_support: true // Enable Intelligent Tracking Prevention support\n      });\n\n      // Render the Google Sign-In button\n      if (googleButtonRef.current) {\n        window.google.accounts.id.renderButton(googleButtonRef.current, {\n          theme: \"outline\",\n          size: \"large\",\n          width: \"100%\",\n          text: \"signin_with\",\n          locale: \"id\"\n        });\n      }\n    } else {\n      console.error(\"Google Identity Services not loaded\");\n      setError(\"Google Identity Services tidak dapat dimuat\");\n    }\n  };\n  useEffect(() => {\n    // Initialize Google Sign-In when component mounts\n\n    // Wait for Google script to load\n    const checkGoogleLoaded = () => {\n      if (window.google && window.google.accounts) {\n        initializeGoogleSignIn();\n      } else {\n        setTimeout(checkGoogleLoaded, 100);\n      }\n    };\n    checkGoogleLoaded();\n  }, [initializeGoogleSignIn]); // Add initializeGoogleSignIn as dependency\n\n  const handleCredentialResponse = async response => {\n    console.log(\"Google credential response:\", response);\n    setLoading(true);\n    setError(null);\n    try {\n      console.log(\"Attempting login with token...\");\n      await login(response.credential);\n      console.log(\"Login successful!\");\n    } catch (error) {\n      console.error(\"Login error details:\", error);\n      setError(`Login failed: ${error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const resetGoogleSession = () => {\n    console.log(\"🔄 Resetting Google session...\");\n    if (window.google && window.google.accounts) {\n      try {\n        // Disable auto-select\n        window.google.accounts.id.disableAutoSelect();\n\n        // Cancel any pending prompts\n        if (window.google.accounts.id.cancel) {\n          window.google.accounts.id.cancel();\n        }\n\n        // Clear Google cookies\n        const googleCookies = [\"g_state\", \"g_csrf_token\"];\n        googleCookies.forEach(cookieName => {\n          document.cookie = `${cookieName}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=.google.com`;\n          document.cookie = `${cookieName}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=.accounts.google.com`;\n          document.cookie = `${cookieName}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;\n        });\n        console.log(\"✅ Google session reset completed\");\n\n        // Re-initialize after a short delay\n        setTimeout(() => {\n          initializeGoogleSignIn();\n        }, 500);\n      } catch (error) {\n        console.warn(\"⚠️ Error resetting Google session:\", error);\n      }\n    }\n  };\n  const handleManualLogin = () => {\n    if (window.google && window.google.accounts) {\n      window.google.accounts.id.prompt();\n    } else {\n      setError(\"Google Identity Services tidak tersedia\");\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-r from-green-50 to-yellow-50 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-md w-full mx-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white dark:bg-gray-800 rounded-lg shadow-xl p-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mx-auto w-20 h-20 bg-gradient-to-r from-green-600 to-yellow-500 rounded-full flex items-center justify-center mb-4\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-white text-2xl font-bold\",\n              children: \"U\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-2xl font-bold text-gray-900 dark:text-white mb-2\",\n            children: \"Chatbot UNAND\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 dark:text-gray-400 text-sm\",\n            children: \"UNTUK KEDJAJAAN BANGSA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-500 dark:text-gray-500 text-sm mt-2\",\n            children: \"Masuk untuk mengakses chatbot peraturan kampus\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4 p-3 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-lg\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-red-700 dark:text-red-300 text-sm\",\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: googleButtonRef,\n            className: \"w-full\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this), loading && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center py-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-green-600 mr-3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-gray-600 dark:text-gray-400\",\n            children: \"Memproses login...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleManualLogin,\n            disabled: loading,\n            className: \"w-full flex items-center justify-center px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-5 h-5 mr-3\",\n                viewBox: \"0 0 24 24\",\n                children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                  fill: \"currentColor\",\n                  d: \"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 175,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  fill: \"currentColor\",\n                  d: \"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 179,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  fill: \"currentColor\",\n                  d: \"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 183,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  fill: \"currentColor\",\n                  d: \"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 17\n              }, this), \"Login Manual (jika tombol di atas tidak muncul)\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: resetGoogleSession,\n            disabled: loading,\n            className: \"w-full flex items-center justify-center px-4 py-2 border border-orange-300 dark:border-orange-600 rounded-lg shadow-sm bg-orange-50 dark:bg-orange-900/20 text-orange-700 dark:text-orange-300 hover:bg-orange-100 dark:hover:bg-orange-900/40 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-4 h-4 mr-2\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 15\n            }, this), \"Reset Google Session (jika login bermasalah)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-6 text-center\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs text-gray-500 dark:text-gray-400\",\n            children: \"Dengan masuk, Anda menyetujui penggunaan data untuk keperluan chatbot\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 124,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"UedV2vSZT0PByJj7YeEKAQAKJcU=\", false, function () {\n  return [useAuth];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useCallback", "useAuth", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "_s", "login", "loading", "setLoading", "error", "setError", "googleButtonRef", "initializeGoogleSignIn", "window", "google", "accounts", "console", "log", "process", "env", "REACT_APP_GOOGLE_CLIENT_ID", "location", "origin", "id", "disableAutoSelect", "e", "initialize", "client_id", "callback", "handleCredentialResponse", "auto_select", "cancel_on_tap_outside", "use_fedcm_for_prompt", "itp_support", "current", "renderButton", "theme", "size", "width", "text", "locale", "checkGoogleLoaded", "setTimeout", "response", "credential", "message", "resetGoogleSession", "cancel", "googleCookies", "for<PERSON>ach", "cookieName", "document", "cookie", "warn", "handleManualLogin", "prompt", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ref", "onClick", "disabled", "viewBox", "fill", "d", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/website kp/chatbot-unand/frontend/src/components/Login.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef, useCallback } from \"react\";\nimport { useAuth } from \"../contexts/AuthContext\";\n\nconst Login = () => {\n  const { login } = useAuth();\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const googleButtonRef = useRef(null);\n\n  // Initialize Google Sign-In function (moved outside useEffect to be accessible)\n  const initializeGoogleSignIn = () => {\n    if (window.google && window.google.accounts) {\n      console.log(\"✅ Initializing Google Sign-In...\");\n      console.log(\"🔑 Client ID:\", process.env.REACT_APP_GOOGLE_CLIENT_ID);\n      console.log(\"🌐 Current origin:\", window.location.origin);\n\n      // Clear any existing Google session first\n      try {\n        window.google.accounts.id.disableAutoSelect();\n        console.log(\"🧹 Cleared existing Google auto-select\");\n      } catch (e) {\n        console.log(\"ℹ️ No existing Google session to clear\");\n      }\n\n      window.google.accounts.id.initialize({\n        client_id: process.env.REACT_APP_GOOGLE_CLIENT_ID,\n        callback: handleCredentialResponse,\n        auto_select: false,\n        cancel_on_tap_outside: true,\n        use_fedcm_for_prompt: false, // Disable FedCM to avoid conflicts\n        itp_support: true, // Enable Intelligent Tracking Prevention support\n      });\n\n      // Render the Google Sign-In button\n      if (googleButtonRef.current) {\n        window.google.accounts.id.renderButton(googleButtonRef.current, {\n          theme: \"outline\",\n          size: \"large\",\n          width: \"100%\",\n          text: \"signin_with\",\n          locale: \"id\",\n        });\n      }\n    } else {\n      console.error(\"Google Identity Services not loaded\");\n      setError(\"Google Identity Services tidak dapat dimuat\");\n    }\n  };\n\n  useEffect(() => {\n    // Initialize Google Sign-In when component mounts\n\n    // Wait for Google script to load\n    const checkGoogleLoaded = () => {\n      if (window.google && window.google.accounts) {\n        initializeGoogleSignIn();\n      } else {\n        setTimeout(checkGoogleLoaded, 100);\n      }\n    };\n\n    checkGoogleLoaded();\n  }, [initializeGoogleSignIn]); // Add initializeGoogleSignIn as dependency\n\n  const handleCredentialResponse = async (response) => {\n    console.log(\"Google credential response:\", response);\n    setLoading(true);\n    setError(null);\n\n    try {\n      console.log(\"Attempting login with token...\");\n      await login(response.credential);\n      console.log(\"Login successful!\");\n    } catch (error) {\n      console.error(\"Login error details:\", error);\n      setError(`Login failed: ${error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const resetGoogleSession = () => {\n    console.log(\"🔄 Resetting Google session...\");\n\n    if (window.google && window.google.accounts) {\n      try {\n        // Disable auto-select\n        window.google.accounts.id.disableAutoSelect();\n\n        // Cancel any pending prompts\n        if (window.google.accounts.id.cancel) {\n          window.google.accounts.id.cancel();\n        }\n\n        // Clear Google cookies\n        const googleCookies = [\"g_state\", \"g_csrf_token\"];\n        googleCookies.forEach((cookieName) => {\n          document.cookie = `${cookieName}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=.google.com`;\n          document.cookie = `${cookieName}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=.accounts.google.com`;\n          document.cookie = `${cookieName}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;\n        });\n\n        console.log(\"✅ Google session reset completed\");\n\n        // Re-initialize after a short delay\n        setTimeout(() => {\n          initializeGoogleSignIn();\n        }, 500);\n      } catch (error) {\n        console.warn(\"⚠️ Error resetting Google session:\", error);\n      }\n    }\n  };\n\n  const handleManualLogin = () => {\n    if (window.google && window.google.accounts) {\n      window.google.accounts.id.prompt();\n    } else {\n      setError(\"Google Identity Services tidak tersedia\");\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-r from-green-50 to-yellow-50 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center\">\n      <div className=\"max-w-md w-full mx-4\">\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-xl p-8\">\n          {/* Logo and Title */}\n          <div className=\"text-center mb-8\">\n            <div className=\"mx-auto w-20 h-20 bg-gradient-to-r from-green-600 to-yellow-500 rounded-full flex items-center justify-center mb-4\">\n              <span className=\"text-white text-2xl font-bold\">U</span>\n            </div>\n            <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-2\">\n              Chatbot UNAND\n            </h1>\n            <p className=\"text-gray-600 dark:text-gray-400 text-sm\">\n              UNTUK KEDJAJAAN BANGSA\n            </p>\n            <p className=\"text-gray-500 dark:text-gray-500 text-sm mt-2\">\n              Masuk untuk mengakses chatbot peraturan kampus\n            </p>\n          </div>\n\n          {/* Error Message */}\n          {error && (\n            <div className=\"mb-4 p-3 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-lg\">\n              <p className=\"text-red-700 dark:text-red-300 text-sm\">{error}</p>\n            </div>\n          )}\n\n          {/* Google Sign-In Button Container */}\n          <div className=\"mb-4\">\n            <div ref={googleButtonRef} className=\"w-full\"></div>\n          </div>\n\n          {/* Loading State */}\n          {loading && (\n            <div className=\"flex items-center justify-center py-3\">\n              <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-green-600 mr-3\"></div>\n              <span className=\"text-gray-600 dark:text-gray-400\">\n                Memproses login...\n              </span>\n            </div>\n          )}\n\n          {/* Action Buttons */}\n          <div className=\"space-y-2\">\n            {/* Fallback Manual Login Button */}\n            <button\n              onClick={handleManualLogin}\n              disabled={loading}\n              className=\"w-full flex items-center justify-center px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n            >\n              <div className=\"flex items-center\">\n                <svg className=\"w-5 h-5 mr-3\" viewBox=\"0 0 24 24\">\n                  <path\n                    fill=\"currentColor\"\n                    d=\"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\n                  />\n                  <path\n                    fill=\"currentColor\"\n                    d=\"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\n                  />\n                  <path\n                    fill=\"currentColor\"\n                    d=\"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\n                  />\n                  <path\n                    fill=\"currentColor\"\n                    d=\"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\n                  />\n                </svg>\n                Login Manual (jika tombol di atas tidak muncul)\n              </div>\n            </button>\n\n            {/* Reset Session Button */}\n            <button\n              onClick={resetGoogleSession}\n              disabled={loading}\n              className=\"w-full flex items-center justify-center px-4 py-2 border border-orange-300 dark:border-orange-600 rounded-lg shadow-sm bg-orange-50 dark:bg-orange-900/20 text-orange-700 dark:text-orange-300 hover:bg-orange-100 dark:hover:bg-orange-900/40 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm\"\n            >\n              <svg\n                className=\"w-4 h-4 mr-2\"\n                fill=\"none\"\n                stroke=\"currentColor\"\n                viewBox=\"0 0 24 24\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                />\n              </svg>\n              Reset Google Session (jika login bermasalah)\n            </button>\n          </div>\n\n          {/* Footer */}\n          <div className=\"mt-6 text-center\">\n            <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n              Dengan masuk, Anda menyetujui penggunaan data untuk keperluan\n              chatbot\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Login;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AACvE,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM;IAAEC;EAAM,CAAC,GAAGL,OAAO,CAAC,CAAC;EAC3B,MAAM,CAACM,OAAO,EAAEC,UAAU,CAAC,GAAGX,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACY,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAMc,eAAe,GAAGZ,MAAM,CAAC,IAAI,CAAC;;EAEpC;EACA,MAAMa,sBAAsB,GAAGA,CAAA,KAAM;IACnC,IAAIC,MAAM,CAACC,MAAM,IAAID,MAAM,CAACC,MAAM,CAACC,QAAQ,EAAE;MAC3CC,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;MAC/CD,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEC,OAAO,CAACC,GAAG,CAACC,0BAA0B,CAAC;MACpEJ,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEJ,MAAM,CAACQ,QAAQ,CAACC,MAAM,CAAC;;MAEzD;MACA,IAAI;QACFT,MAAM,CAACC,MAAM,CAACC,QAAQ,CAACQ,EAAE,CAACC,iBAAiB,CAAC,CAAC;QAC7CR,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MACvD,CAAC,CAAC,OAAOQ,CAAC,EAAE;QACVT,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MACvD;MAEAJ,MAAM,CAACC,MAAM,CAACC,QAAQ,CAACQ,EAAE,CAACG,UAAU,CAAC;QACnCC,SAAS,EAAET,OAAO,CAACC,GAAG,CAACC,0BAA0B;QACjDQ,QAAQ,EAAEC,wBAAwB;QAClCC,WAAW,EAAE,KAAK;QAClBC,qBAAqB,EAAE,IAAI;QAC3BC,oBAAoB,EAAE,KAAK;QAAE;QAC7BC,WAAW,EAAE,IAAI,CAAE;MACrB,CAAC,CAAC;;MAEF;MACA,IAAItB,eAAe,CAACuB,OAAO,EAAE;QAC3BrB,MAAM,CAACC,MAAM,CAACC,QAAQ,CAACQ,EAAE,CAACY,YAAY,CAACxB,eAAe,CAACuB,OAAO,EAAE;UAC9DE,KAAK,EAAE,SAAS;UAChBC,IAAI,EAAE,OAAO;UACbC,KAAK,EAAE,MAAM;UACbC,IAAI,EAAE,aAAa;UACnBC,MAAM,EAAE;QACV,CAAC,CAAC;MACJ;IACF,CAAC,MAAM;MACLxB,OAAO,CAACP,KAAK,CAAC,qCAAqC,CAAC;MACpDC,QAAQ,CAAC,6CAA6C,CAAC;IACzD;EACF,CAAC;EAEDZ,SAAS,CAAC,MAAM;IACd;;IAEA;IACA,MAAM2C,iBAAiB,GAAGA,CAAA,KAAM;MAC9B,IAAI5B,MAAM,CAACC,MAAM,IAAID,MAAM,CAACC,MAAM,CAACC,QAAQ,EAAE;QAC3CH,sBAAsB,CAAC,CAAC;MAC1B,CAAC,MAAM;QACL8B,UAAU,CAACD,iBAAiB,EAAE,GAAG,CAAC;MACpC;IACF,CAAC;IAEDA,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,CAAC7B,sBAAsB,CAAC,CAAC,CAAC,CAAC;;EAE9B,MAAMiB,wBAAwB,GAAG,MAAOc,QAAQ,IAAK;IACnD3B,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE0B,QAAQ,CAAC;IACpDnC,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACFM,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;MAC7C,MAAMX,KAAK,CAACqC,QAAQ,CAACC,UAAU,CAAC;MAChC5B,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;IAClC,CAAC,CAAC,OAAOR,KAAK,EAAE;MACdO,OAAO,CAACP,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CC,QAAQ,CAAC,iBAAiBD,KAAK,CAACoC,OAAO,EAAE,CAAC;IAC5C,CAAC,SAAS;MACRrC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMsC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B9B,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;IAE7C,IAAIJ,MAAM,CAACC,MAAM,IAAID,MAAM,CAACC,MAAM,CAACC,QAAQ,EAAE;MAC3C,IAAI;QACF;QACAF,MAAM,CAACC,MAAM,CAACC,QAAQ,CAACQ,EAAE,CAACC,iBAAiB,CAAC,CAAC;;QAE7C;QACA,IAAIX,MAAM,CAACC,MAAM,CAACC,QAAQ,CAACQ,EAAE,CAACwB,MAAM,EAAE;UACpClC,MAAM,CAACC,MAAM,CAACC,QAAQ,CAACQ,EAAE,CAACwB,MAAM,CAAC,CAAC;QACpC;;QAEA;QACA,MAAMC,aAAa,GAAG,CAAC,SAAS,EAAE,cAAc,CAAC;QACjDA,aAAa,CAACC,OAAO,CAAEC,UAAU,IAAK;UACpCC,QAAQ,CAACC,MAAM,GAAG,GAAGF,UAAU,mEAAmE;UAClGC,QAAQ,CAACC,MAAM,GAAG,GAAGF,UAAU,4EAA4E;UAC3GC,QAAQ,CAACC,MAAM,GAAG,GAAGF,UAAU,gDAAgD;QACjF,CAAC,CAAC;QAEFlC,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;;QAE/C;QACAyB,UAAU,CAAC,MAAM;UACf9B,sBAAsB,CAAC,CAAC;QAC1B,CAAC,EAAE,GAAG,CAAC;MACT,CAAC,CAAC,OAAOH,KAAK,EAAE;QACdO,OAAO,CAACqC,IAAI,CAAC,oCAAoC,EAAE5C,KAAK,CAAC;MAC3D;IACF;EACF,CAAC;EAED,MAAM6C,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAIzC,MAAM,CAACC,MAAM,IAAID,MAAM,CAACC,MAAM,CAACC,QAAQ,EAAE;MAC3CF,MAAM,CAACC,MAAM,CAACC,QAAQ,CAACQ,EAAE,CAACgC,MAAM,CAAC,CAAC;IACpC,CAAC,MAAM;MACL7C,QAAQ,CAAC,yCAAyC,CAAC;IACrD;EACF,CAAC;EAED,oBACEP,OAAA;IAAKqD,SAAS,EAAC,+HAA+H;IAAAC,QAAA,eAC5ItD,OAAA;MAAKqD,SAAS,EAAC,sBAAsB;MAAAC,QAAA,eACnCtD,OAAA;QAAKqD,SAAS,EAAC,oDAAoD;QAAAC,QAAA,gBAEjEtD,OAAA;UAAKqD,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BtD,OAAA;YAAKqD,SAAS,EAAC,oHAAoH;YAAAC,QAAA,eACjItD,OAAA;cAAMqD,SAAS,EAAC,+BAA+B;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC,eACN1D,OAAA;YAAIqD,SAAS,EAAC,uDAAuD;YAAAC,QAAA,EAAC;UAEtE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL1D,OAAA;YAAGqD,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAExD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJ1D,OAAA;YAAGqD,SAAS,EAAC,+CAA+C;YAAAC,QAAA,EAAC;UAE7D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,EAGLpD,KAAK,iBACJN,OAAA;UAAKqD,SAAS,EAAC,yFAAyF;UAAAC,QAAA,eACtGtD,OAAA;YAAGqD,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAEhD;UAAK;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CACN,eAGD1D,OAAA;UAAKqD,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBtD,OAAA;YAAK2D,GAAG,EAAEnD,eAAgB;YAAC6C,SAAS,EAAC;UAAQ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC,EAGLtD,OAAO,iBACNJ,OAAA;UAAKqD,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpDtD,OAAA;YAAKqD,SAAS,EAAC;UAAoE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1F1D,OAAA;YAAMqD,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAEnD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN,eAGD1D,OAAA;UAAKqD,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAExBtD,OAAA;YACE4D,OAAO,EAAET,iBAAkB;YAC3BU,QAAQ,EAAEzD,OAAQ;YAClBiD,SAAS,EAAC,kWAAkW;YAAAC,QAAA,eAE5WtD,OAAA;cAAKqD,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCtD,OAAA;gBAAKqD,SAAS,EAAC,cAAc;gBAACS,OAAO,EAAC,WAAW;gBAAAR,QAAA,gBAC/CtD,OAAA;kBACE+D,IAAI,EAAC,cAAc;kBACnBC,CAAC,EAAC;gBAAyH;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5H,CAAC,eACF1D,OAAA;kBACE+D,IAAI,EAAC,cAAc;kBACnBC,CAAC,EAAC;gBAAuI;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1I,CAAC,eACF1D,OAAA;kBACE+D,IAAI,EAAC,cAAc;kBACnBC,CAAC,EAAC;gBAA+H;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClI,CAAC,eACF1D,OAAA;kBACE+D,IAAI,EAAC,cAAc;kBACnBC,CAAC,EAAC;gBAAqI;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,mDAER;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eAGT1D,OAAA;YACE4D,OAAO,EAAEjB,kBAAmB;YAC5BkB,QAAQ,EAAEzD,OAAQ;YAClBiD,SAAS,EAAC,oYAAoY;YAAAC,QAAA,gBAE9YtD,OAAA;cACEqD,SAAS,EAAC,cAAc;cACxBU,IAAI,EAAC,MAAM;cACXE,MAAM,EAAC,cAAc;cACrBH,OAAO,EAAC,WAAW;cAAAR,QAAA,eAEnBtD,OAAA;gBACEkE,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC,OAAO;gBACtBC,WAAW,EAAE,CAAE;gBACfJ,CAAC,EAAC;cAA6G;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,gDAER;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGN1D,OAAA;UAAKqD,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC/BtD,OAAA;YAAGqD,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAGxD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACxD,EAAA,CAlOID,KAAK;EAAA,QACSH,OAAO;AAAA;AAAAuE,EAAA,GADrBpE,KAAK;AAoOX,eAAeA,KAAK;AAAC,IAAAoE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}