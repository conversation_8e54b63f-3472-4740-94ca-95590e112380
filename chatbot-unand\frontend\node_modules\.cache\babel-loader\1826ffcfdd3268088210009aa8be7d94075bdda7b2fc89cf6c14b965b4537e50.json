{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M13.5 10.5 15 9\",\n  key: \"1nsxvm\"\n}], [\"path\", {\n  d: \"M4 4v15a1 1 0 0 0 1 1h15\",\n  key: \"1w6lkd\"\n}], [\"path\", {\n  d: \"M4.293 19.707 6 18\",\n  key: \"3g1p8c\"\n}], [\"path\", {\n  d: \"m9 15 1.5-1.5\",\n  key: \"1xfbes\"\n}]];\nconst Axis3d = createLucideIcon(\"axis-3d\", __iconNode);\nexport { __iconNode, Axis3d as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "Axis3d", "createLucideIcon"], "sources": ["D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\node_modules\\lucide-react\\src\\icons\\axis-3d.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M13.5 10.5 15 9', key: '1nsxvm' }],\n  ['path', { d: 'M4 4v15a1 1 0 0 0 1 1h15', key: '1w6lkd' }],\n  ['path', { d: 'M4.293 19.707 6 18', key: '3g1p8c' }],\n  ['path', { d: 'm9 15 1.5-1.5', key: '1xfbes' }],\n];\n\n/**\n * @component @name Axis3d\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTMuNSAxMC41IDE1IDkiIC8+CiAgPHBhdGggZD0iTTQgNHYxNWExIDEgMCAwIDAgMSAxaDE1IiAvPgogIDxwYXRoIGQ9Ik00LjI5MyAxOS43MDcgNiAxOCIgLz4KICA8cGF0aCBkPSJtOSAxNSAxLjUtMS41IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/axis-3d\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Axis3d = createLucideIcon('axis-3d', __iconNode);\n\nexport default Axis3d;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,iBAAmB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChD,CAAC,MAAQ;EAAED,CAAA,EAAG,0BAA4B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzD,CAAC,MAAQ;EAAED,CAAA,EAAG,oBAAsB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACnD,CAAC,MAAQ;EAAED,CAAA,EAAG,eAAiB;EAAAC,GAAA,EAAK;AAAU,GAChD;AAaM,MAAAC,MAAA,GAASC,gBAAiB,YAAWJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}