{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website kp\\\\chatbot-unand\\\\frontend\\\\src\\\\components\\\\AdminDashboard.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from \"react\";\nimport AdminLayout from \"./AdminLayout\";\nimport { useAuth } from \"../contexts/AuthContext\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminDashboard = () => {\n  _s();\n  var _dashboardData$user_s, _dashboardData$user_s2, _dashboardData$user_s3, _dashboardData$user_s4, _dashboardData$recent, _dashboardData$all_us;\n  const [dashboardData, setDashboardData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const {\n    getAdminAuthHeaders\n  } = useAuth();\n  const fetchDashboardData = useCallback(async () => {\n    try {\n      setLoading(true);\n      console.log(\"🔄 Fetching dashboard data...\");\n      console.log(\"🔑 Auth headers:\", getAdminAuthHeaders());\n      const response = await fetch(`${process.env.REACT_APP_API_BASE_URL}/admin/dashboard`, {\n        headers: getAdminAuthHeaders()\n      });\n      console.log(\"📡 Response status:\", response.status);\n      console.log(\"📡 Response ok:\", response.ok);\n      if (!response.ok) {\n        const errorText = await response.text();\n        console.error(\"❌ Response error:\", errorText);\n        throw new Error(`Failed to fetch dashboard data: ${response.status} ${errorText}`);\n      }\n      const data = await response.json();\n      console.log(\"✅ Dashboard data received:\", data);\n      setDashboardData(data);\n    } catch (error) {\n      console.error(\"❌ Error fetching dashboard data:\", error);\n      setError(error.message);\n    } finally {\n      setLoading(false);\n    }\n  }, [getAdminAuthHeaders]);\n  useEffect(() => {\n    fetchDashboardData();\n  }, [fetchDashboardData]);\n  const formatDate = dateString => {\n    return new Date(dateString).toLocaleString(\"id-ID\", {\n      year: \"numeric\",\n      month: \"short\",\n      day: \"numeric\",\n      hour: \"2-digit\",\n      minute: \"2-digit\"\n    });\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(AdminLayout, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center h-64\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(AdminLayout, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-red-50 border border-red-200 rounded-md p-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-red-800\",\n          children: [\"Error: \", error]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: fetchDashboardData,\n          className: \"mt-2 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700\",\n          children: \"Retry\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(AdminLayout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-2 bg-blue-100 rounded-md\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-6 h-6 text-blue-600\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 101,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 95,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-600\",\n                children: \"Total Users\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-semibold text-gray-900\",\n                children: (dashboardData === null || dashboardData === void 0 ? void 0 : (_dashboardData$user_s = dashboardData.user_stats) === null || _dashboardData$user_s === void 0 ? void 0 : _dashboardData$user_s.total_users) || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 111,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-2 bg-green-100 rounded-md\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-6 h-6 text-green-600\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 127,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 121,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-600\",\n                children: \"Active Sessions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 136,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-semibold text-gray-900\",\n                children: (dashboardData === null || dashboardData === void 0 ? void 0 : (_dashboardData$user_s2 = dashboardData.user_stats) === null || _dashboardData$user_s2 === void 0 ? void 0 : _dashboardData$user_s2.active_sessions) || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-2 bg-yellow-100 rounded-md\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-6 h-6 text-yellow-600\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 155,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-600\",\n                children: \"Total Messages\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-semibold text-gray-900\",\n                children: (dashboardData === null || dashboardData === void 0 ? void 0 : (_dashboardData$user_s3 = dashboardData.user_stats) === null || _dashboardData$user_s3 === void 0 ? void 0 : _dashboardData$user_s3.total_messages) || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-2 bg-purple-100 rounded-md\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-6 h-6 text-purple-600\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 183,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-600\",\n                children: \"Total Sessions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-semibold text-gray-900\",\n                children: (dashboardData === null || dashboardData === void 0 ? void 0 : (_dashboardData$user_s4 = dashboardData.user_stats) === null || _dashboardData$user_s4 === void 0 ? void 0 : _dashboardData$user_s4.total_sessions) || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-6 py-4 border-b border-gray-200\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-900\",\n            children: \"Log Aktivitas User Terbaru\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"overflow-x-auto\",\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"min-w-full divide-y divide-gray-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              className: \"bg-gray-50\",\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"User\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Aktivitas\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 217,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Session ID\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 220,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Waktu\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 223,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              className: \"bg-white divide-y divide-gray-200\",\n              children: dashboardData === null || dashboardData === void 0 ? void 0 : (_dashboardData$recent = dashboardData.recent_activities) === null || _dashboardData$recent === void 0 ? void 0 : _dashboardData$recent.slice(0, 10).map(activity => {\n                var _activity$user, _activity$user2;\n                return /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"px-6 py-4 whitespace-nowrap\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm font-medium text-gray-900\",\n                      children: ((_activity$user = activity.user) === null || _activity$user === void 0 ? void 0 : _activity$user.name) || \"Unknown\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 234,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-gray-500\",\n                      children: ((_activity$user2 = activity.user) === null || _activity$user2 === void 0 ? void 0 : _activity$user2.email) || \"N/A\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 237,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 233,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"px-6 py-4 whitespace-nowrap\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${activity.activity_type === \"login\" ? \"bg-green-100 text-green-800\" : activity.activity_type === \"logout\" ? \"bg-red-100 text-red-800\" : \"bg-blue-100 text-blue-800\"}`,\n                      children: activity.activity_type\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 242,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 241,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                    children: activity.session_id ? activity.session_id.substring(0, 8) + \"...\" : \"N/A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 254,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                    children: formatDate(activity.timestamp)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 259,\n                    columnNumber: 23\n                  }, this)]\n                }, activity.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 232,\n                  columnNumber: 21\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-6 py-4 border-b border-gray-200\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-900\",\n            children: \"Seluruh User\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"overflow-x-auto\",\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"min-w-full divide-y divide-gray-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              className: \"bg-gray-50\",\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"No.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Email\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Nama\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 284,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                  children: \"Total Sessions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 287,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              className: \"bg-white divide-y divide-gray-200\",\n              children: dashboardData === null || dashboardData === void 0 ? void 0 : (_dashboardData$all_us = dashboardData.all_users) === null || _dashboardData$all_us === void 0 ? void 0 : _dashboardData$all_us.map((user, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm font-medium text-gray-900\",\n                    children: index + 1\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 296,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 295,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm font-medium text-gray-900\",\n                    children: user.email\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 301,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 300,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                  children: user.name || \"N/A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 305,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                  children: user.session_count\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 21\n                }, this)]\n              }, user.user_id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 270,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 88,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminDashboard, \"rXiU2LDhNBt4RrPn4fGdHZtj6UI=\", false, function () {\n  return [useAuth];\n});\n_c = AdminDashboard;\nexport default AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "AdminLayout", "useAuth", "jsxDEV", "_jsxDEV", "AdminDashboard", "_s", "_dashboardData$user_s", "_dashboardData$user_s2", "_dashboardData$user_s3", "_dashboardData$user_s4", "_dashboardData$recent", "_dashboardData$all_us", "dashboardData", "setDashboardData", "loading", "setLoading", "error", "setError", "getAdminAuthHeaders", "fetchDashboardData", "console", "log", "response", "fetch", "process", "env", "REACT_APP_API_BASE_URL", "headers", "status", "ok", "errorText", "text", "Error", "data", "json", "message", "formatDate", "dateString", "Date", "toLocaleString", "year", "month", "day", "hour", "minute", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "user_stats", "total_users", "active_sessions", "total_messages", "total_sessions", "recent_activities", "slice", "map", "activity", "_activity$user", "_activity$user2", "user", "name", "email", "activity_type", "session_id", "substring", "timestamp", "id", "all_users", "index", "session_count", "user_id", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/website kp/chatbot-unand/frontend/src/components/AdminDashboard.jsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from \"react\";\nimport AdminLayout from \"./AdminLayout\";\nimport { useAuth } from \"../contexts/AuthContext\";\n\nconst AdminDashboard = () => {\n  const [dashboardData, setDashboardData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  const { getAdminAuthHeaders } = useAuth();\n\n  const fetchDashboardData = useCallback(async () => {\n    try {\n      setLoading(true);\n      console.log(\"🔄 Fetching dashboard data...\");\n      console.log(\"🔑 Auth headers:\", getAdminAuthHeaders());\n\n      const response = await fetch(\n        `${process.env.REACT_APP_API_BASE_URL}/admin/dashboard`,\n        {\n          headers: getAdminAuthHeaders(),\n        }\n      );\n\n      console.log(\"📡 Response status:\", response.status);\n      console.log(\"📡 Response ok:\", response.ok);\n\n      if (!response.ok) {\n        const errorText = await response.text();\n        console.error(\"❌ Response error:\", errorText);\n        throw new Error(\n          `Failed to fetch dashboard data: ${response.status} ${errorText}`\n        );\n      }\n\n      const data = await response.json();\n      console.log(\"✅ Dashboard data received:\", data);\n      setDashboardData(data);\n    } catch (error) {\n      console.error(\"❌ Error fetching dashboard data:\", error);\n      setError(error.message);\n    } finally {\n      setLoading(false);\n    }\n  }, [getAdminAuthHeaders]);\n\n  useEffect(() => {\n    fetchDashboardData();\n  }, [fetchDashboardData]);\n\n  const formatDate = (dateString) => {\n    return new Date(dateString).toLocaleString(\"id-ID\", {\n      year: \"numeric\",\n      month: \"short\",\n      day: \"numeric\",\n      hour: \"2-digit\",\n      minute: \"2-digit\",\n    });\n  };\n\n  if (loading) {\n    return (\n      <AdminLayout>\n        <div className=\"flex items-center justify-center h-64\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600\"></div>\n        </div>\n      </AdminLayout>\n    );\n  }\n\n  if (error) {\n    return (\n      <AdminLayout>\n        <div className=\"bg-red-50 border border-red-200 rounded-md p-4\">\n          <p className=\"text-red-800\">Error: {error}</p>\n          <button\n            onClick={fetchDashboardData}\n            className=\"mt-2 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700\"\n          >\n            Retry\n          </button>\n        </div>\n      </AdminLayout>\n    );\n  }\n\n  return (\n    <AdminLayout>\n      <div className=\"space-y-6\">\n        {/* Statistics Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-blue-100 rounded-md\">\n                <svg\n                  className=\"w-6 h-6 text-blue-600\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  viewBox=\"0 0 24 24\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    strokeWidth={2}\n                    d=\"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\"\n                  />\n                </svg>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Total Users</p>\n                <p className=\"text-2xl font-semibold text-gray-900\">\n                  {dashboardData?.user_stats?.total_users || 0}\n                </p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-green-100 rounded-md\">\n                <svg\n                  className=\"w-6 h-6 text-green-600\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  viewBox=\"0 0 24 24\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    strokeWidth={2}\n                    d=\"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                  />\n                </svg>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">\n                  Active Sessions\n                </p>\n                <p className=\"text-2xl font-semibold text-gray-900\">\n                  {dashboardData?.user_stats?.active_sessions || 0}\n                </p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-yellow-100 rounded-md\">\n                <svg\n                  className=\"w-6 h-6 text-yellow-600\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  viewBox=\"0 0 24 24\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    strokeWidth={2}\n                    d=\"M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z\"\n                  />\n                </svg>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">\n                  Total Messages\n                </p>\n                <p className=\"text-2xl font-semibold text-gray-900\">\n                  {dashboardData?.user_stats?.total_messages || 0}\n                </p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-purple-100 rounded-md\">\n                <svg\n                  className=\"w-6 h-6 text-purple-600\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  viewBox=\"0 0 24 24\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    strokeWidth={2}\n                    d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                  />\n                </svg>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">\n                  Total Sessions\n                </p>\n                <p className=\"text-2xl font-semibold text-gray-900\">\n                  {dashboardData?.user_stats?.total_sessions || 0}\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Recent User Activities */}\n        <div className=\"bg-white rounded-lg shadow\">\n          <div className=\"px-6 py-4 border-b border-gray-200\">\n            <h3 className=\"text-lg font-medium text-gray-900\">\n              Log Aktivitas User Terbaru\n            </h3>\n          </div>\n          <div className=\"overflow-x-auto\">\n            <table className=\"min-w-full divide-y divide-gray-200\">\n              <thead className=\"bg-gray-50\">\n                <tr>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    User\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Aktivitas\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Session ID\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Waktu\n                  </th>\n                </tr>\n              </thead>\n              <tbody className=\"bg-white divide-y divide-gray-200\">\n                {dashboardData?.recent_activities\n                  ?.slice(0, 10)\n                  .map((activity) => (\n                    <tr key={activity.id}>\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <div className=\"text-sm font-medium text-gray-900\">\n                          {activity.user?.name || \"Unknown\"}\n                        </div>\n                        <div className=\"text-sm text-gray-500\">\n                          {activity.user?.email || \"N/A\"}\n                        </div>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <span\n                          className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${\n                            activity.activity_type === \"login\"\n                              ? \"bg-green-100 text-green-800\"\n                              : activity.activity_type === \"logout\"\n                              ? \"bg-red-100 text-red-800\"\n                              : \"bg-blue-100 text-blue-800\"\n                          }`}\n                        >\n                          {activity.activity_type}\n                        </span>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                        {activity.session_id\n                          ? activity.session_id.substring(0, 8) + \"...\"\n                          : \"N/A\"}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                        {formatDate(activity.timestamp)}\n                      </td>\n                    </tr>\n                  ))}\n              </tbody>\n            </table>\n          </div>\n        </div>\n\n        {/* All Users */}\n        <div className=\"bg-white rounded-lg shadow\">\n          <div className=\"px-6 py-4 border-b border-gray-200\">\n            <h3 className=\"text-lg font-medium text-gray-900\">Seluruh User</h3>\n          </div>\n          <div className=\"overflow-x-auto\">\n            <table className=\"min-w-full divide-y divide-gray-200\">\n              <thead className=\"bg-gray-50\">\n                <tr>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    No.\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Email\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Nama\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Total Sessions\n                  </th>\n                </tr>\n              </thead>\n              <tbody className=\"bg-white divide-y divide-gray-200\">\n                {dashboardData?.all_users?.map((user, index) => (\n                  <tr key={user.user_id}>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div className=\"text-sm font-medium text-gray-900\">\n                        {index + 1}\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div className=\"text-sm font-medium text-gray-900\">\n                        {user.email}\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                      {user.name || \"N/A\"}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                      {user.session_count}\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        </div>\n      </div>\n    </AdminLayout>\n  );\n};\n\nexport default AdminDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,qBAAA;EAC3B,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmB,KAAK,EAAEC,QAAQ,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAExC,MAAM;IAAEqB;EAAoB,CAAC,GAAGjB,OAAO,CAAC,CAAC;EAEzC,MAAMkB,kBAAkB,GAAGpB,WAAW,CAAC,YAAY;IACjD,IAAI;MACFgB,UAAU,CAAC,IAAI,CAAC;MAChBK,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAC5CD,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEH,mBAAmB,CAAC,CAAC,CAAC;MAEtD,MAAMI,QAAQ,GAAG,MAAMC,KAAK,CAC1B,GAAGC,OAAO,CAACC,GAAG,CAACC,sBAAsB,kBAAkB,EACvD;QACEC,OAAO,EAAET,mBAAmB,CAAC;MAC/B,CACF,CAAC;MAEDE,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEC,QAAQ,CAACM,MAAM,CAAC;MACnDR,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEC,QAAQ,CAACO,EAAE,CAAC;MAE3C,IAAI,CAACP,QAAQ,CAACO,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAMR,QAAQ,CAACS,IAAI,CAAC,CAAC;QACvCX,OAAO,CAACJ,KAAK,CAAC,mBAAmB,EAAEc,SAAS,CAAC;QAC7C,MAAM,IAAIE,KAAK,CACb,mCAAmCV,QAAQ,CAACM,MAAM,IAAIE,SAAS,EACjE,CAAC;MACH;MAEA,MAAMG,IAAI,GAAG,MAAMX,QAAQ,CAACY,IAAI,CAAC,CAAC;MAClCd,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEY,IAAI,CAAC;MAC/CpB,gBAAgB,CAACoB,IAAI,CAAC;IACxB,CAAC,CAAC,OAAOjB,KAAK,EAAE;MACdI,OAAO,CAACJ,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxDC,QAAQ,CAACD,KAAK,CAACmB,OAAO,CAAC;IACzB,CAAC,SAAS;MACRpB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACG,mBAAmB,CAAC,CAAC;EAEzBpB,SAAS,CAAC,MAAM;IACdqB,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,CAACA,kBAAkB,CAAC,CAAC;EAExB,MAAMiB,UAAU,GAAIC,UAAU,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,cAAc,CAAC,OAAO,EAAE;MAClDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,OAAO;MACdC,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED,IAAI9B,OAAO,EAAE;IACX,oBACEX,OAAA,CAACH,WAAW;MAAA6C,QAAA,eACV1C,OAAA;QAAK2C,SAAS,EAAC,uCAAuC;QAAAD,QAAA,eACpD1C,OAAA;UAAK2C,SAAS,EAAC;QAAiE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC;EAElB;EAEA,IAAIlC,KAAK,EAAE;IACT,oBACEb,OAAA,CAACH,WAAW;MAAA6C,QAAA,eACV1C,OAAA;QAAK2C,SAAS,EAAC,gDAAgD;QAAAD,QAAA,gBAC7D1C,OAAA;UAAG2C,SAAS,EAAC,cAAc;UAAAD,QAAA,GAAC,SAAO,EAAC7B,KAAK;QAAA;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9C/C,OAAA;UACEgD,OAAO,EAAEhC,kBAAmB;UAC5B2B,SAAS,EAAC,kEAAkE;UAAAD,QAAA,EAC7E;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC;EAElB;EAEA,oBACE/C,OAAA,CAACH,WAAW;IAAA6C,QAAA,eACV1C,OAAA;MAAK2C,SAAS,EAAC,WAAW;MAAAD,QAAA,gBAExB1C,OAAA;QAAK2C,SAAS,EAAC,sDAAsD;QAAAD,QAAA,gBACnE1C,OAAA;UAAK2C,SAAS,EAAC,gCAAgC;UAAAD,QAAA,eAC7C1C,OAAA;YAAK2C,SAAS,EAAC,mBAAmB;YAAAD,QAAA,gBAChC1C,OAAA;cAAK2C,SAAS,EAAC,4BAA4B;cAAAD,QAAA,eACzC1C,OAAA;gBACE2C,SAAS,EAAC,uBAAuB;gBACjCM,IAAI,EAAC,MAAM;gBACXC,MAAM,EAAC,cAAc;gBACrBC,OAAO,EAAC,WAAW;gBAAAT,QAAA,eAEnB1C,OAAA;kBACEoD,aAAa,EAAC,OAAO;kBACrBC,cAAc,EAAC,OAAO;kBACtBC,WAAW,EAAE,CAAE;kBACfC,CAAC,EAAC;gBAAyH;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5H;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN/C,OAAA;cAAK2C,SAAS,EAAC,MAAM;cAAAD,QAAA,gBACnB1C,OAAA;gBAAG2C,SAAS,EAAC,mCAAmC;gBAAAD,QAAA,EAAC;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAChE/C,OAAA;gBAAG2C,SAAS,EAAC,sCAAsC;gBAAAD,QAAA,EAChD,CAAAjC,aAAa,aAAbA,aAAa,wBAAAN,qBAAA,GAAbM,aAAa,CAAE+C,UAAU,cAAArD,qBAAA,uBAAzBA,qBAAA,CAA2BsD,WAAW,KAAI;cAAC;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN/C,OAAA;UAAK2C,SAAS,EAAC,gCAAgC;UAAAD,QAAA,eAC7C1C,OAAA;YAAK2C,SAAS,EAAC,mBAAmB;YAAAD,QAAA,gBAChC1C,OAAA;cAAK2C,SAAS,EAAC,6BAA6B;cAAAD,QAAA,eAC1C1C,OAAA;gBACE2C,SAAS,EAAC,wBAAwB;gBAClCM,IAAI,EAAC,MAAM;gBACXC,MAAM,EAAC,cAAc;gBACrBC,OAAO,EAAC,WAAW;gBAAAT,QAAA,eAEnB1C,OAAA;kBACEoD,aAAa,EAAC,OAAO;kBACrBC,cAAc,EAAC,OAAO;kBACtBC,WAAW,EAAE,CAAE;kBACfC,CAAC,EAAC;gBAA+J;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN/C,OAAA;cAAK2C,SAAS,EAAC,MAAM;cAAAD,QAAA,gBACnB1C,OAAA;gBAAG2C,SAAS,EAAC,mCAAmC;gBAAAD,QAAA,EAAC;cAEjD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJ/C,OAAA;gBAAG2C,SAAS,EAAC,sCAAsC;gBAAAD,QAAA,EAChD,CAAAjC,aAAa,aAAbA,aAAa,wBAAAL,sBAAA,GAAbK,aAAa,CAAE+C,UAAU,cAAApD,sBAAA,uBAAzBA,sBAAA,CAA2BsD,eAAe,KAAI;cAAC;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN/C,OAAA;UAAK2C,SAAS,EAAC,gCAAgC;UAAAD,QAAA,eAC7C1C,OAAA;YAAK2C,SAAS,EAAC,mBAAmB;YAAAD,QAAA,gBAChC1C,OAAA;cAAK2C,SAAS,EAAC,8BAA8B;cAAAD,QAAA,eAC3C1C,OAAA;gBACE2C,SAAS,EAAC,yBAAyB;gBACnCM,IAAI,EAAC,MAAM;gBACXC,MAAM,EAAC,cAAc;gBACrBC,OAAO,EAAC,WAAW;gBAAAT,QAAA,eAEnB1C,OAAA;kBACEoD,aAAa,EAAC,OAAO;kBACrBC,cAAc,EAAC,OAAO;kBACtBC,WAAW,EAAE,CAAE;kBACfC,CAAC,EAAC;gBAA+J;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN/C,OAAA;cAAK2C,SAAS,EAAC,MAAM;cAAAD,QAAA,gBACnB1C,OAAA;gBAAG2C,SAAS,EAAC,mCAAmC;gBAAAD,QAAA,EAAC;cAEjD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJ/C,OAAA;gBAAG2C,SAAS,EAAC,sCAAsC;gBAAAD,QAAA,EAChD,CAAAjC,aAAa,aAAbA,aAAa,wBAAAJ,sBAAA,GAAbI,aAAa,CAAE+C,UAAU,cAAAnD,sBAAA,uBAAzBA,sBAAA,CAA2BsD,cAAc,KAAI;cAAC;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN/C,OAAA;UAAK2C,SAAS,EAAC,gCAAgC;UAAAD,QAAA,eAC7C1C,OAAA;YAAK2C,SAAS,EAAC,mBAAmB;YAAAD,QAAA,gBAChC1C,OAAA;cAAK2C,SAAS,EAAC,8BAA8B;cAAAD,QAAA,eAC3C1C,OAAA;gBACE2C,SAAS,EAAC,yBAAyB;gBACnCM,IAAI,EAAC,MAAM;gBACXC,MAAM,EAAC,cAAc;gBACrBC,OAAO,EAAC,WAAW;gBAAAT,QAAA,eAEnB1C,OAAA;kBACEoD,aAAa,EAAC,OAAO;kBACrBC,cAAc,EAAC,OAAO;kBACtBC,WAAW,EAAE,CAAE;kBACfC,CAAC,EAAC;gBAAsH;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN/C,OAAA;cAAK2C,SAAS,EAAC,MAAM;cAAAD,QAAA,gBACnB1C,OAAA;gBAAG2C,SAAS,EAAC,mCAAmC;gBAAAD,QAAA,EAAC;cAEjD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJ/C,OAAA;gBAAG2C,SAAS,EAAC,sCAAsC;gBAAAD,QAAA,EAChD,CAAAjC,aAAa,aAAbA,aAAa,wBAAAH,sBAAA,GAAbG,aAAa,CAAE+C,UAAU,cAAAlD,sBAAA,uBAAzBA,sBAAA,CAA2BsD,cAAc,KAAI;cAAC;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN/C,OAAA;QAAK2C,SAAS,EAAC,4BAA4B;QAAAD,QAAA,gBACzC1C,OAAA;UAAK2C,SAAS,EAAC,oCAAoC;UAAAD,QAAA,eACjD1C,OAAA;YAAI2C,SAAS,EAAC,mCAAmC;YAAAD,QAAA,EAAC;UAElD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACN/C,OAAA;UAAK2C,SAAS,EAAC,iBAAiB;UAAAD,QAAA,eAC9B1C,OAAA;YAAO2C,SAAS,EAAC,qCAAqC;YAAAD,QAAA,gBACpD1C,OAAA;cAAO2C,SAAS,EAAC,YAAY;cAAAD,QAAA,eAC3B1C,OAAA;gBAAA0C,QAAA,gBACE1C,OAAA;kBAAI2C,SAAS,EAAC,gFAAgF;kBAAAD,QAAA,EAAC;gBAE/F;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL/C,OAAA;kBAAI2C,SAAS,EAAC,gFAAgF;kBAAAD,QAAA,EAAC;gBAE/F;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL/C,OAAA;kBAAI2C,SAAS,EAAC,gFAAgF;kBAAAD,QAAA,EAAC;gBAE/F;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL/C,OAAA;kBAAI2C,SAAS,EAAC,gFAAgF;kBAAAD,QAAA,EAAC;gBAE/F;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACR/C,OAAA;cAAO2C,SAAS,EAAC,mCAAmC;cAAAD,QAAA,EACjDjC,aAAa,aAAbA,aAAa,wBAAAF,qBAAA,GAAbE,aAAa,CAAEoD,iBAAiB,cAAAtD,qBAAA,uBAAhCA,qBAAA,CACGuD,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CACbC,GAAG,CAAEC,QAAQ;gBAAA,IAAAC,cAAA,EAAAC,eAAA;gBAAA,oBACZlE,OAAA;kBAAA0C,QAAA,gBACE1C,OAAA;oBAAI2C,SAAS,EAAC,6BAA6B;oBAAAD,QAAA,gBACzC1C,OAAA;sBAAK2C,SAAS,EAAC,mCAAmC;sBAAAD,QAAA,EAC/C,EAAAuB,cAAA,GAAAD,QAAQ,CAACG,IAAI,cAAAF,cAAA,uBAAbA,cAAA,CAAeG,IAAI,KAAI;oBAAS;sBAAAxB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9B,CAAC,eACN/C,OAAA;sBAAK2C,SAAS,EAAC,uBAAuB;sBAAAD,QAAA,EACnC,EAAAwB,eAAA,GAAAF,QAAQ,CAACG,IAAI,cAAAD,eAAA,uBAAbA,eAAA,CAAeG,KAAK,KAAI;oBAAK;sBAAAzB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACL/C,OAAA;oBAAI2C,SAAS,EAAC,6BAA6B;oBAAAD,QAAA,eACzC1C,OAAA;sBACE2C,SAAS,EAAE,4DACTqB,QAAQ,CAACM,aAAa,KAAK,OAAO,GAC9B,6BAA6B,GAC7BN,QAAQ,CAACM,aAAa,KAAK,QAAQ,GACnC,yBAAyB,GACzB,2BAA2B,EAC9B;sBAAA5B,QAAA,EAEFsB,QAAQ,CAACM;oBAAa;sBAAA1B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACL/C,OAAA;oBAAI2C,SAAS,EAAC,mDAAmD;oBAAAD,QAAA,EAC9DsB,QAAQ,CAACO,UAAU,GAChBP,QAAQ,CAACO,UAAU,CAACC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,GAC3C;kBAAK;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC,eACL/C,OAAA;oBAAI2C,SAAS,EAAC,mDAAmD;oBAAAD,QAAA,EAC9DT,UAAU,CAAC+B,QAAQ,CAACS,SAAS;kBAAC;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7B,CAAC;gBAAA,GA7BEiB,QAAQ,CAACU,EAAE;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA8BhB,CAAC;cAAA,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN/C,OAAA;QAAK2C,SAAS,EAAC,4BAA4B;QAAAD,QAAA,gBACzC1C,OAAA;UAAK2C,SAAS,EAAC,oCAAoC;UAAAD,QAAA,eACjD1C,OAAA;YAAI2C,SAAS,EAAC,mCAAmC;YAAAD,QAAA,EAAC;UAAY;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CAAC,eACN/C,OAAA;UAAK2C,SAAS,EAAC,iBAAiB;UAAAD,QAAA,eAC9B1C,OAAA;YAAO2C,SAAS,EAAC,qCAAqC;YAAAD,QAAA,gBACpD1C,OAAA;cAAO2C,SAAS,EAAC,YAAY;cAAAD,QAAA,eAC3B1C,OAAA;gBAAA0C,QAAA,gBACE1C,OAAA;kBAAI2C,SAAS,EAAC,gFAAgF;kBAAAD,QAAA,EAAC;gBAE/F;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL/C,OAAA;kBAAI2C,SAAS,EAAC,gFAAgF;kBAAAD,QAAA,EAAC;gBAE/F;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL/C,OAAA;kBAAI2C,SAAS,EAAC,gFAAgF;kBAAAD,QAAA,EAAC;gBAE/F;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL/C,OAAA;kBAAI2C,SAAS,EAAC,gFAAgF;kBAAAD,QAAA,EAAC;gBAE/F;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACR/C,OAAA;cAAO2C,SAAS,EAAC,mCAAmC;cAAAD,QAAA,EACjDjC,aAAa,aAAbA,aAAa,wBAAAD,qBAAA,GAAbC,aAAa,CAAEkE,SAAS,cAAAnE,qBAAA,uBAAxBA,qBAAA,CAA0BuD,GAAG,CAAC,CAACI,IAAI,EAAES,KAAK,kBACzC5E,OAAA;gBAAA0C,QAAA,gBACE1C,OAAA;kBAAI2C,SAAS,EAAC,6BAA6B;kBAAAD,QAAA,eACzC1C,OAAA;oBAAK2C,SAAS,EAAC,mCAAmC;oBAAAD,QAAA,EAC/CkC,KAAK,GAAG;kBAAC;oBAAAhC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACL/C,OAAA;kBAAI2C,SAAS,EAAC,6BAA6B;kBAAAD,QAAA,eACzC1C,OAAA;oBAAK2C,SAAS,EAAC,mCAAmC;oBAAAD,QAAA,EAC/CyB,IAAI,CAACE;kBAAK;oBAAAzB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACL/C,OAAA;kBAAI2C,SAAS,EAAC,mDAAmD;kBAAAD,QAAA,EAC9DyB,IAAI,CAACC,IAAI,IAAI;gBAAK;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC,eACL/C,OAAA;kBAAI2C,SAAS,EAAC,mDAAmD;kBAAAD,QAAA,EAC9DyB,IAAI,CAACU;gBAAa;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC;cAAA,GAhBEoB,IAAI,CAACW,OAAO;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAiBjB,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAElB,CAAC;AAAC7C,EAAA,CA3TID,cAAc;EAAA,QAKcH,OAAO;AAAA;AAAAiF,EAAA,GALnC9E,cAAc;AA6TpB,eAAeA,cAAc;AAAC,IAAA8E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}