{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M15 2c-1.35 1.5-2.092 3-2.5 4.5L14 8\",\n  key: \"1bivrr\"\n}], [\"path\", {\n  d: \"m17 6-2.891-2.891\",\n  key: \"xu6p2f\"\n}], [\"path\", {\n  d: \"M2 15c3.333-3 6.667-3 10-3\",\n  key: \"nxix30\"\n}], [\"path\", {\n  d: \"m2 2 20 20\",\n  key: \"1ooewy\"\n}], [\"path\", {\n  d: \"m20 9 .891.891\",\n  key: \"3xwk7g\"\n}], [\"path\", {\n  d: \"M22 9c-1.5 1.35-3 2.092-4.5 2.5l-1-1\",\n  key: \"18cutr\"\n}], [\"path\", {\n  d: \"M3.109 14.109 4 15\",\n  key: \"q76aoh\"\n}], [\"path\", {\n  d: \"m6.5 12.5 1 1\",\n  key: \"cs35ky\"\n}], [\"path\", {\n  d: \"m7 18 2.891 2.891\",\n  key: \"1sisit\"\n}], [\"path\", {\n  d: \"M9 22c1.35-1.5 2.092-3 2.5-4.5L10 16\",\n  key: \"rlvei3\"\n}]];\nconst DnaOff = createLucideIcon(\"dna-off\", __iconNode);\nexport { __iconNode, DnaOff as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "<PERSON><PERSON><PERSON><PERSON>", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\node_modules\\lucide-react\\src\\icons\\dna-off.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M15 2c-1.35 1.5-2.092 3-2.5 4.5L14 8', key: '1bivrr' }],\n  ['path', { d: 'm17 6-2.891-2.891', key: 'xu6p2f' }],\n  ['path', { d: 'M2 15c3.333-3 6.667-3 10-3', key: 'nxix30' }],\n  ['path', { d: 'm2 2 20 20', key: '1ooewy' }],\n  ['path', { d: 'm20 9 .891.891', key: '3xwk7g' }],\n  ['path', { d: 'M22 9c-1.5 1.35-3 2.092-4.5 2.5l-1-1', key: '18cutr' }],\n  ['path', { d: 'M3.109 14.109 4 15', key: 'q76aoh' }],\n  ['path', { d: 'm6.5 12.5 1 1', key: 'cs35ky' }],\n  ['path', { d: 'm7 18 2.891 2.891', key: '1sisit' }],\n  ['path', { d: 'M9 22c1.35-1.5 2.092-3 2.5-4.5L10 16', key: 'rlvei3' }],\n];\n\n/**\n * @component @name DnaOff\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTUgMmMtMS4zNSAxLjUtMi4wOTIgMy0yLjUgNC41TDE0IDgiIC8+CiAgPHBhdGggZD0ibTE3IDYtMi44OTEtMi44OTEiIC8+CiAgPHBhdGggZD0iTTIgMTVjMy4zMzMtMyA2LjY2Ny0zIDEwLTMiIC8+CiAgPHBhdGggZD0ibTIgMiAyMCAyMCIgLz4KICA8cGF0aCBkPSJtMjAgOSAuODkxLjg5MSIgLz4KICA8cGF0aCBkPSJNMjIgOWMtMS41IDEuMzUtMyAyLjA5Mi00LjUgMi41bC0xLTEiIC8+CiAgPHBhdGggZD0iTTMuMTA5IDE0LjEwOSA0IDE1IiAvPgogIDxwYXRoIGQ9Im02LjUgMTIuNSAxIDEiIC8+CiAgPHBhdGggZD0ibTcgMTggMi44OTEgMi44OTEiIC8+CiAgPHBhdGggZD0iTTkgMjJjMS4zNS0xLjUgMi4wOTItMyAyLjUtNC41TDEwIDE2IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/dna-off\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst DnaOff = createLucideIcon('dna-off', __iconNode);\n\nexport default DnaOff;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,sCAAwC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACrE,CAAC,MAAQ;EAAED,CAAA,EAAG,mBAAqB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAClD,CAAC,MAAQ;EAAED,CAAA,EAAG,4BAA8B;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3D,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,gBAAkB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/C,CAAC,MAAQ;EAAED,CAAA,EAAG,sCAAwC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACrE,CAAC,MAAQ;EAAED,CAAA,EAAG,oBAAsB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACnD,CAAC,MAAQ;EAAED,CAAA,EAAG,eAAiB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9C,CAAC,MAAQ;EAAED,CAAA,EAAG,mBAAqB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAClD,CAAC,MAAQ;EAAED,CAAA,EAAG,sCAAwC;EAAAC,GAAA,EAAK;AAAU,GACvE;AAaM,MAAAC,MAAA,GAASC,gBAAiB,YAAWJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}