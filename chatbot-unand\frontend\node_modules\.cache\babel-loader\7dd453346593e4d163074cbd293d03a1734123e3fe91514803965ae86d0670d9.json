{"ast": null, "code": "const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || \"http://localhost:8000\";\nexport const sendMessageToChatbot = async query => {\n  try {\n    const response = await fetch(`${API_BASE_URL}/chat`, {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\"\n      },\n      body: JSON.stringify({\n        query\n      })\n    });\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.detail || \"Terjadi kesalahan pada server.\");\n    }\n    const data = await response.json();\n    return data.response;\n  } catch (error) {\n    console.error(\"Error sending message to chatbot:\", error);\n    return `Maaf, terjadi masalah saat menghubungi chatbot: ${error.message}.`;\n  }\n};\nexport const uploadDocument = async file => {\n  try {\n    const formData = new FormData();\n    formData.append(\"file\", file); // 'file' harus cocok dengan nama parameter di FastAPI\n\n    const response = await fetch(`${API_BASE_URL}/upload-document`, {\n      method: \"POST\",\n      body: formData // FormData akan mengatur header Content-Type secara otomatis\n    });\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.detail || \"Gagal mengunggah dokumen.\");\n    }\n    const data = await response.json();\n    return data.message;\n  } catch (error) {\n    console.error(\"Error uploading document:\", error);\n    throw error; // Re-throw to be handled by the component\n  }\n};", "map": {"version": 3, "names": ["API_BASE_URL", "process", "env", "REACT_APP_API_BASE_URL", "sendMessageToChatbot", "query", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "ok", "errorData", "json", "Error", "detail", "data", "error", "console", "message", "uploadDocument", "file", "formData", "FormData", "append"], "sources": ["C:/Users/<USER>/Downloads/website kp/chatbot-unand/frontend/src/api.js"], "sourcesContent": ["const API_BASE_URL =\n  process.env.REACT_APP_API_BASE_URL || \"http://localhost:8000\";\n\nexport const sendMessageToChatbot = async (query) => {\n  try {\n    const response = await fetch(`${API_BASE_URL}/chat`, {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n      },\n      body: JSON.stringify({ query }),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.detail || \"Terjadi kesalahan pada server.\");\n    }\n\n    const data = await response.json();\n    return data.response;\n  } catch (error) {\n    console.error(\"Error sending message to chatbot:\", error);\n    return `Maaf, terjadi masalah saat menghubungi chatbot: ${error.message}.`;\n  }\n};\n\nexport const uploadDocument = async (file) => {\n  try {\n    const formData = new FormData();\n    formData.append(\"file\", file); // 'file' harus cocok dengan nama parameter di FastAPI\n\n    const response = await fetch(`${API_BASE_URL}/upload-document`, {\n      method: \"POST\",\n      body: formData, // FormData akan mengatur header Content-Type secara otomatis\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json();\n      throw new Error(errorData.detail || \"Gagal mengunggah dokumen.\");\n    }\n\n    const data = await response.json();\n    return data.message;\n  } catch (error) {\n    console.error(\"Error uploading document:\", error);\n    throw error; // Re-throw to be handled by the component\n  }\n};\n"], "mappings": "AAAA,MAAMA,YAAY,GAChBC,OAAO,CAACC,GAAG,CAACC,sBAAsB,IAAI,uBAAuB;AAE/D,OAAO,MAAMC,oBAAoB,GAAG,MAAOC,KAAK,IAAK;EACnD,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGP,YAAY,OAAO,EAAE;MACnDQ,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB,CAAC;MACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QAAEP;MAAM,CAAC;IAChC,CAAC,CAAC;IAEF,IAAI,CAACC,QAAQ,CAACO,EAAE,EAAE;MAChB,MAAMC,SAAS,GAAG,MAAMR,QAAQ,CAACS,IAAI,CAAC,CAAC;MACvC,MAAM,IAAIC,KAAK,CAACF,SAAS,CAACG,MAAM,IAAI,gCAAgC,CAAC;IACvE;IAEA,MAAMC,IAAI,GAAG,MAAMZ,QAAQ,CAACS,IAAI,CAAC,CAAC;IAClC,OAAOG,IAAI,CAACZ,QAAQ;EACtB,CAAC,CAAC,OAAOa,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;IACzD,OAAO,mDAAmDA,KAAK,CAACE,OAAO,GAAG;EAC5E;AACF,CAAC;AAED,OAAO,MAAMC,cAAc,GAAG,MAAOC,IAAI,IAAK;EAC5C,IAAI;IACF,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEH,IAAI,CAAC,CAAC,CAAC;;IAE/B,MAAMjB,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGP,YAAY,kBAAkB,EAAE;MAC9DQ,MAAM,EAAE,MAAM;MACdE,IAAI,EAAEc,QAAQ,CAAE;IAClB,CAAC,CAAC;IAEF,IAAI,CAAClB,QAAQ,CAACO,EAAE,EAAE;MAChB,MAAMC,SAAS,GAAG,MAAMR,QAAQ,CAACS,IAAI,CAAC,CAAC;MACvC,MAAM,IAAIC,KAAK,CAACF,SAAS,CAACG,MAAM,IAAI,2BAA2B,CAAC;IAClE;IAEA,MAAMC,IAAI,GAAG,MAAMZ,QAAQ,CAACS,IAAI,CAAC,CAAC;IAClC,OAAOG,IAAI,CAACG,OAAO;EACrB,CAAC,CAAC,OAAOF,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACjD,MAAMA,KAAK,CAAC,CAAC;EACf;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}