{"ast": null, "code": "import { clsx } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\nexport function cn(...inputs) {\n  return twMerge(clsx(inputs));\n}", "map": {"version": 3, "names": ["clsx", "twMerge", "cn", "inputs"], "sources": ["C:/Users/<USER>/Downloads/website kp/chatbot-unand/frontend/src/lib/utils.js"], "sourcesContent": ["import { clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs) {\n  return twMerge(clsx(inputs))\n}\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,MAAM;AAC3B,SAASC,OAAO,QAAQ,gBAAgB;AAExC,OAAO,SAASC,EAAEA,CAAC,GAAGC,MAAM,EAAE;EAC5B,OAAOF,OAAO,CAACD,IAAI,CAACG,MAAM,CAAC,CAAC;AAC9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}