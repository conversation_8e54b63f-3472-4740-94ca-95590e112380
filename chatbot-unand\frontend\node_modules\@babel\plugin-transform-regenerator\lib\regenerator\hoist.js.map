{"version": 3, "names": ["util", "require", "hasOwn", "Object", "prototype", "hasOwnProperty", "hoist", "funPath", "t", "getTypes", "assertFunction", "node", "vars", "varDeclToExpr", "vdec", "scope", "includeIdentifiers", "assertVariableDeclaration", "exprs", "declarations", "for<PERSON>ach", "dec", "id", "name", "identifier", "removeBinding", "init", "push", "assignmentExpression", "length", "sequenceExpression", "get", "traverse", "VariableDeclaration", "exit", "path", "expr", "remove", "replaceWithOrRemove", "expressionStatement", "skip", "ForStatement", "isVariableDeclaration", "ForXStatement", "left", "FunctionDeclaration", "assignment", "clone", "functionExpression", "generateUidIdentifierBasedOnNode", "params", "body", "generator", "expression", "parentPath", "isBlockStatement", "unshiftContainer", "FunctionExpression", "ArrowFunctionExpression", "paramNames", "<PERSON><PERSON><PERSON><PERSON>", "param", "isIdentifier", "keys", "call", "variableDeclarator"], "sources": ["../../src/regenerator/hoist.ts"], "sourcesContent": ["import * as util from \"./util.ts\";\nconst hasOwn = Object.prototype.hasOwnProperty;\n\n// The hoist function takes a FunctionExpression or FunctionDeclaration\n// and replaces any Declaration nodes in its body with assignments, then\n// returns a VariableDeclaration containing just the names of the removed\n// declarations.\nexport function hoist(funPath: any) {\n  const t = util.getTypes();\n  t.assertFunction(funPath.node);\n\n  const vars: Record<string, any> = {};\n\n  function varDeclToExpr({ node: vdec, scope }: any, includeIdentifiers: any) {\n    t.assertVariableDeclaration(vdec);\n    // TODO assert.equal(vdec.kind, \"var\");\n    const exprs: any[] = [];\n\n    vdec.declarations.forEach(function (dec: any) {\n      // Note: We duplicate 'dec.id' here to ensure that the variable declaration IDs don't\n      // have the same 'loc' value, since that can make sourcemaps and retainLines behave poorly.\n      vars[dec.id.name] = t.identifier(dec.id.name);\n\n      // Remove the binding, to avoid \"duplicate declaration\" errors when it will\n      // be injected again.\n      scope.removeBinding(dec.id.name);\n\n      if (dec.init) {\n        exprs.push(t.assignmentExpression(\"=\", dec.id, dec.init));\n      } else if (includeIdentifiers) {\n        exprs.push(dec.id);\n      }\n    });\n\n    if (exprs.length === 0) return null;\n\n    if (exprs.length === 1) return exprs[0];\n\n    return t.sequenceExpression(exprs);\n  }\n\n  funPath.get(\"body\").traverse({\n    VariableDeclaration: {\n      exit: function (path: any) {\n        const expr = varDeclToExpr(path, false);\n        if (expr === null) {\n          path.remove();\n        } else {\n          // We don't need to traverse this expression any further because\n          // there can't be any new declarations inside an expression.\n          util.replaceWithOrRemove(path, t.expressionStatement(expr));\n        }\n\n        // Since the original node has been either removed or replaced,\n        // avoid traversing it any further.\n        path.skip();\n      },\n    },\n\n    ForStatement: function (path: any) {\n      const init = path.get(\"init\");\n      if (init.isVariableDeclaration()) {\n        util.replaceWithOrRemove(init, varDeclToExpr(init, false));\n      }\n    },\n\n    ForXStatement: function (path: any) {\n      const left = path.get(\"left\");\n      if (left.isVariableDeclaration()) {\n        util.replaceWithOrRemove(left, varDeclToExpr(left, true));\n      }\n    },\n\n    FunctionDeclaration: function (path: any) {\n      const node = path.node;\n      vars[node.id.name] = node.id;\n\n      const assignment = t.expressionStatement(\n        t.assignmentExpression(\n          \"=\",\n          t.clone(node.id),\n          t.functionExpression(\n            path.scope.generateUidIdentifierBasedOnNode(node),\n            node.params,\n            node.body,\n            node.generator,\n            node.expression,\n          ),\n        ),\n      );\n\n      if (path.parentPath.isBlockStatement()) {\n        // Insert the assignment form before the first statement in the\n        // enclosing block.\n        path.parentPath.unshiftContainer(\"body\", assignment);\n\n        // Remove the function declaration now that we've inserted the\n        // equivalent assignment form at the beginning of the block.\n        path.remove();\n      } else {\n        // If the parent node is not a block statement, then we can just\n        // replace the declaration with the equivalent assignment form\n        // without worrying about hoisting it.\n        util.replaceWithOrRemove(path, assignment);\n      }\n\n      // Remove the binding, to avoid \"duplicate declaration\" errors when it will\n      // be injected again.\n      path.scope.removeBinding(node.id.name);\n\n      // Don't hoist variables out of inner functions.\n      path.skip();\n    },\n\n    FunctionExpression: function (path: any) {\n      // Don't descend into nested function expressions.\n      path.skip();\n    },\n\n    ArrowFunctionExpression: function (path: any) {\n      // Don't descend into nested function expressions.\n      path.skip();\n    },\n  });\n\n  const paramNames: Record<string, any> = {};\n  funPath.get(\"params\").forEach(function (paramPath: any) {\n    const param = paramPath.node;\n    if (t.isIdentifier(param)) {\n      paramNames[param.name] = param;\n    } else {\n      // Variables declared by destructuring parameter patterns will be\n      // harmlessly re-declared.\n    }\n  });\n\n  const declarations: any[] = [];\n\n  Object.keys(vars).forEach(function (name: any) {\n    if (!hasOwn.call(paramNames, name)) {\n      declarations.push(t.variableDeclarator(vars[name], null));\n    }\n  });\n\n  return declarations;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,IAAA,GAAAC,OAAA;AACA,MAAMC,MAAM,GAAGC,MAAM,CAACC,SAAS,CAACC,cAAc;AAMvC,SAASC,KAAKA,CAACC,OAAY,EAAE;EAClC,MAAMC,CAAC,GAAGR,IAAI,CAACS,QAAQ,CAAC,CAAC;EACzBD,CAAC,CAACE,cAAc,CAACH,OAAO,CAACI,IAAI,CAAC;EAE9B,MAAMC,IAAyB,GAAG,CAAC,CAAC;EAEpC,SAASC,aAAaA,CAAC;IAAEF,IAAI,EAAEG,IAAI;IAAEC;EAAW,CAAC,EAAEC,kBAAuB,EAAE;IAC1ER,CAAC,CAACS,yBAAyB,CAACH,IAAI,CAAC;IAEjC,MAAMI,KAAY,GAAG,EAAE;IAEvBJ,IAAI,CAACK,YAAY,CAACC,OAAO,CAAC,UAAUC,GAAQ,EAAE;MAG5CT,IAAI,CAACS,GAAG,CAACC,EAAE,CAACC,IAAI,CAAC,GAAGf,CAAC,CAACgB,UAAU,CAACH,GAAG,CAACC,EAAE,CAACC,IAAI,CAAC;MAI7CR,KAAK,CAACU,aAAa,CAACJ,GAAG,CAACC,EAAE,CAACC,IAAI,CAAC;MAEhC,IAAIF,GAAG,CAACK,IAAI,EAAE;QACZR,KAAK,CAACS,IAAI,CAACnB,CAAC,CAACoB,oBAAoB,CAAC,GAAG,EAAEP,GAAG,CAACC,EAAE,EAAED,GAAG,CAACK,IAAI,CAAC,CAAC;MAC3D,CAAC,MAAM,IAAIV,kBAAkB,EAAE;QAC7BE,KAAK,CAACS,IAAI,CAACN,GAAG,CAACC,EAAE,CAAC;MACpB;IACF,CAAC,CAAC;IAEF,IAAIJ,KAAK,CAACW,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;IAEnC,IAAIX,KAAK,CAACW,MAAM,KAAK,CAAC,EAAE,OAAOX,KAAK,CAAC,CAAC,CAAC;IAEvC,OAAOV,CAAC,CAACsB,kBAAkB,CAACZ,KAAK,CAAC;EACpC;EAEAX,OAAO,CAACwB,GAAG,CAAC,MAAM,CAAC,CAACC,QAAQ,CAAC;IAC3BC,mBAAmB,EAAE;MACnBC,IAAI,EAAE,SAAAA,CAAUC,IAAS,EAAE;QACzB,MAAMC,IAAI,GAAGvB,aAAa,CAACsB,IAAI,EAAE,KAAK,CAAC;QACvC,IAAIC,IAAI,KAAK,IAAI,EAAE;UACjBD,IAAI,CAACE,MAAM,CAAC,CAAC;QACf,CAAC,MAAM;UAGLrC,IAAI,CAACsC,mBAAmB,CAACH,IAAI,EAAE3B,CAAC,CAAC+B,mBAAmB,CAACH,IAAI,CAAC,CAAC;QAC7D;QAIAD,IAAI,CAACK,IAAI,CAAC,CAAC;MACb;IACF,CAAC;IAEDC,YAAY,EAAE,SAAAA,CAAUN,IAAS,EAAE;MACjC,MAAMT,IAAI,GAAGS,IAAI,CAACJ,GAAG,CAAC,MAAM,CAAC;MAC7B,IAAIL,IAAI,CAACgB,qBAAqB,CAAC,CAAC,EAAE;QAChC1C,IAAI,CAACsC,mBAAmB,CAACZ,IAAI,EAAEb,aAAa,CAACa,IAAI,EAAE,KAAK,CAAC,CAAC;MAC5D;IACF,CAAC;IAEDiB,aAAa,EAAE,SAAAA,CAAUR,IAAS,EAAE;MAClC,MAAMS,IAAI,GAAGT,IAAI,CAACJ,GAAG,CAAC,MAAM,CAAC;MAC7B,IAAIa,IAAI,CAACF,qBAAqB,CAAC,CAAC,EAAE;QAChC1C,IAAI,CAACsC,mBAAmB,CAACM,IAAI,EAAE/B,aAAa,CAAC+B,IAAI,EAAE,IAAI,CAAC,CAAC;MAC3D;IACF,CAAC;IAEDC,mBAAmB,EAAE,SAAAA,CAAUV,IAAS,EAAE;MACxC,MAAMxB,IAAI,GAAGwB,IAAI,CAACxB,IAAI;MACtBC,IAAI,CAACD,IAAI,CAACW,EAAE,CAACC,IAAI,CAAC,GAAGZ,IAAI,CAACW,EAAE;MAE5B,MAAMwB,UAAU,GAAGtC,CAAC,CAAC+B,mBAAmB,CACtC/B,CAAC,CAACoB,oBAAoB,CACpB,GAAG,EACHpB,CAAC,CAACuC,KAAK,CAACpC,IAAI,CAACW,EAAE,CAAC,EAChBd,CAAC,CAACwC,kBAAkB,CAClBb,IAAI,CAACpB,KAAK,CAACkC,gCAAgC,CAACtC,IAAI,CAAC,EACjDA,IAAI,CAACuC,MAAM,EACXvC,IAAI,CAACwC,IAAI,EACTxC,IAAI,CAACyC,SAAS,EACdzC,IAAI,CAAC0C,UACP,CACF,CACF,CAAC;MAED,IAAIlB,IAAI,CAACmB,UAAU,CAACC,gBAAgB,CAAC,CAAC,EAAE;QAGtCpB,IAAI,CAACmB,UAAU,CAACE,gBAAgB,CAAC,MAAM,EAAEV,UAAU,CAAC;QAIpDX,IAAI,CAACE,MAAM,CAAC,CAAC;MACf,CAAC,MAAM;QAILrC,IAAI,CAACsC,mBAAmB,CAACH,IAAI,EAAEW,UAAU,CAAC;MAC5C;MAIAX,IAAI,CAACpB,KAAK,CAACU,aAAa,CAACd,IAAI,CAACW,EAAE,CAACC,IAAI,CAAC;MAGtCY,IAAI,CAACK,IAAI,CAAC,CAAC;IACb,CAAC;IAEDiB,kBAAkB,EAAE,SAAAA,CAAUtB,IAAS,EAAE;MAEvCA,IAAI,CAACK,IAAI,CAAC,CAAC;IACb,CAAC;IAEDkB,uBAAuB,EAAE,SAAAA,CAAUvB,IAAS,EAAE;MAE5CA,IAAI,CAACK,IAAI,CAAC,CAAC;IACb;EACF,CAAC,CAAC;EAEF,MAAMmB,UAA+B,GAAG,CAAC,CAAC;EAC1CpD,OAAO,CAACwB,GAAG,CAAC,QAAQ,CAAC,CAACX,OAAO,CAAC,UAAUwC,SAAc,EAAE;IACtD,MAAMC,KAAK,GAAGD,SAAS,CAACjD,IAAI;IAC5B,IAAIH,CAAC,CAACsD,YAAY,CAACD,KAAK,CAAC,EAAE;MACzBF,UAAU,CAACE,KAAK,CAACtC,IAAI,CAAC,GAAGsC,KAAK;IAChC,CAAC,MAAM,CAGP;EACF,CAAC,CAAC;EAEF,MAAM1C,YAAmB,GAAG,EAAE;EAE9BhB,MAAM,CAAC4D,IAAI,CAACnD,IAAI,CAAC,CAACQ,OAAO,CAAC,UAAUG,IAAS,EAAE;IAC7C,IAAI,CAACrB,MAAM,CAAC8D,IAAI,CAACL,UAAU,EAAEpC,IAAI,CAAC,EAAE;MAClCJ,YAAY,CAACQ,IAAI,CAACnB,CAAC,CAACyD,kBAAkB,CAACrD,IAAI,CAACW,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;IAC3D;EACF,CAAC,CAAC;EAEF,OAAOJ,YAAY;AACrB", "ignoreList": []}