{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website kp\\\\chatbot-unand\\\\frontend\\\\src\\\\components\\\\SessionGuard.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from \"react\";\nimport { useNavigate, useLocation } from \"react-router-dom\";\nimport { useAuth } from \"../contexts/AuthContext\";\n\n// Session Guard Component to prevent cross-interface access\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SessionGuard = ({\n  children,\n  requiredSessionType\n}) => {\n  _s();\n  const {\n    user,\n    adminUser,\n    hasRequiredSession,\n    isWrongInterface,\n    loading\n  } = useAuth();\n  const navigate = useNavigate();\n  const location = useLocation();\n  useEffect(() => {\n    // Skip check during loading\n    if (loading) return;\n    const currentPath = location.pathname;\n    console.log(\"SessionGuard: Checking interface access\", {\n      currentPath,\n      requiredSessionType,\n      hasUser: !!user,\n      hasAdmin: !!adminUser,\n      hasRequiredSession: hasRequiredSession(requiredSessionType)\n    });\n\n    // Check if user has the required session for this route\n    if (!hasRequiredSession(requiredSessionType)) {\n      console.log(`SessionGuard: No ${requiredSessionType} session found, redirecting to login`);\n\n      // Redirect to appropriate login\n      if (requiredSessionType === \"admin\") {\n        navigate(\"/admin/login\", {\n          replace: true\n        });\n      } else {\n        navigate(\"/\", {\n          replace: true\n        });\n      }\n      return;\n    }\n\n    // Prevent users from accessing wrong interface\n    // If user is logged in and trying to access admin interface (except login page)\n    if (user && isWrongInterface(currentPath, \"user\") && currentPath !== \"/admin/login\") {\n      console.log(\"SessionGuard: User trying to access admin interface, redirecting to user interface\");\n      navigate(\"/\", {\n        replace: true\n      });\n      return;\n    }\n\n    // If admin is logged in and trying to access user interface\n    if (adminUser && isWrongInterface(currentPath, \"admin\")) {\n      console.log(\"SessionGuard: Admin trying to access user interface, redirecting to admin dashboard\");\n      navigate(\"/admin/dashboard\", {\n        replace: true\n      });\n      return;\n    }\n  }, [user, adminUser, requiredSessionType, location.pathname, navigate, hasRequiredSession, isWrongInterface, loading]);\n\n  // Show loading during session check\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-r from-green-50 to-yellow-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Memeriksa sesi...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 7\n    }, this);\n  }\n  return children;\n};\n_s(SessionGuard, \"5nd/XjAh93Wtrl82DNafPQ5LArQ=\", false, function () {\n  return [useAuth, useNavigate, useLocation];\n});\n_c = SessionGuard;\nexport default SessionGuard;\nvar _c;\n$RefreshReg$(_c, \"SessionGuard\");", "map": {"version": 3, "names": ["React", "useEffect", "useNavigate", "useLocation", "useAuth", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON>", "children", "requiredSessionType", "_s", "user", "adminUser", "hasRequiredSession", "isWrongInterface", "loading", "navigate", "location", "currentPath", "pathname", "console", "log", "<PERSON><PERSON>ser", "has<PERSON>dmin", "replace", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/website kp/chatbot-unand/frontend/src/components/SessionGuard.jsx"], "sourcesContent": ["import React, { useEffect } from \"react\";\nimport { useNavigate, useLocation } from \"react-router-dom\";\nimport { useAuth } from \"../contexts/AuthContext\";\n\n// Session Guard Component to prevent cross-interface access\nconst SessionGuard = ({ children, requiredSessionType }) => {\n  const { user, adminUser, hasRequiredSession, isWrongInterface, loading } =\n    useAuth();\n  const navigate = useNavigate();\n  const location = useLocation();\n\n  useEffect(() => {\n    // Skip check during loading\n    if (loading) return;\n\n    const currentPath = location.pathname;\n\n    console.log(\"SessionGuard: Checking interface access\", {\n      currentPath,\n      requiredSessionType,\n      hasUser: !!user,\n      hasAdmin: !!adminUser,\n      hasRequiredSession: hasRequiredSession(requiredSessionType),\n    });\n\n    // Check if user has the required session for this route\n    if (!hasRequiredSession(requiredSessionType)) {\n      console.log(\n        `SessionGuard: No ${requiredSessionType} session found, redirecting to login`\n      );\n\n      // Redirect to appropriate login\n      if (requiredSessionType === \"admin\") {\n        navigate(\"/admin/login\", { replace: true });\n      } else {\n        navigate(\"/\", { replace: true });\n      }\n      return;\n    }\n\n    // Prevent users from accessing wrong interface\n    // If user is logged in and trying to access admin interface (except login page)\n    if (\n      user &&\n      isWrongInterface(currentPath, \"user\") &&\n      currentPath !== \"/admin/login\"\n    ) {\n      console.log(\n        \"SessionGuard: User trying to access admin interface, redirecting to user interface\"\n      );\n      navigate(\"/\", { replace: true });\n      return;\n    }\n\n    // If admin is logged in and trying to access user interface\n    if (adminUser && isWrongInterface(currentPath, \"admin\")) {\n      console.log(\n        \"SessionGuard: Admin trying to access user interface, redirecting to admin dashboard\"\n      );\n      navigate(\"/admin/dashboard\", { replace: true });\n      return;\n    }\n  }, [\n    user,\n    adminUser,\n    requiredSessionType,\n    location.pathname,\n    navigate,\n    hasRequiredSession,\n    isWrongInterface,\n    loading,\n  ]);\n\n  // Show loading during session check\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-r from-green-50 to-yellow-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600\">Memeriksa sesi...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return children;\n};\n\nexport default SessionGuard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,OAAO,QAAQ,yBAAyB;;AAEjD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,YAAY,GAAGA,CAAC;EAAEC,QAAQ;EAAEC;AAAoB,CAAC,KAAK;EAAAC,EAAA;EAC1D,MAAM;IAAEC,IAAI;IAAEC,SAAS;IAAEC,kBAAkB;IAAEC,gBAAgB;IAAEC;EAAQ,CAAC,GACtEX,OAAO,CAAC,CAAC;EACX,MAAMY,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAC9B,MAAMe,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAE9BF,SAAS,CAAC,MAAM;IACd;IACA,IAAIc,OAAO,EAAE;IAEb,MAAMG,WAAW,GAAGD,QAAQ,CAACE,QAAQ;IAErCC,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAE;MACrDH,WAAW;MACXT,mBAAmB;MACnBa,OAAO,EAAE,CAAC,CAACX,IAAI;MACfY,QAAQ,EAAE,CAAC,CAACX,SAAS;MACrBC,kBAAkB,EAAEA,kBAAkB,CAACJ,mBAAmB;IAC5D,CAAC,CAAC;;IAEF;IACA,IAAI,CAACI,kBAAkB,CAACJ,mBAAmB,CAAC,EAAE;MAC5CW,OAAO,CAACC,GAAG,CACT,oBAAoBZ,mBAAmB,sCACzC,CAAC;;MAED;MACA,IAAIA,mBAAmB,KAAK,OAAO,EAAE;QACnCO,QAAQ,CAAC,cAAc,EAAE;UAAEQ,OAAO,EAAE;QAAK,CAAC,CAAC;MAC7C,CAAC,MAAM;QACLR,QAAQ,CAAC,GAAG,EAAE;UAAEQ,OAAO,EAAE;QAAK,CAAC,CAAC;MAClC;MACA;IACF;;IAEA;IACA;IACA,IACEb,IAAI,IACJG,gBAAgB,CAACI,WAAW,EAAE,MAAM,CAAC,IACrCA,WAAW,KAAK,cAAc,EAC9B;MACAE,OAAO,CAACC,GAAG,CACT,oFACF,CAAC;MACDL,QAAQ,CAAC,GAAG,EAAE;QAAEQ,OAAO,EAAE;MAAK,CAAC,CAAC;MAChC;IACF;;IAEA;IACA,IAAIZ,SAAS,IAAIE,gBAAgB,CAACI,WAAW,EAAE,OAAO,CAAC,EAAE;MACvDE,OAAO,CAACC,GAAG,CACT,qFACF,CAAC;MACDL,QAAQ,CAAC,kBAAkB,EAAE;QAAEQ,OAAO,EAAE;MAAK,CAAC,CAAC;MAC/C;IACF;EACF,CAAC,EAAE,CACDb,IAAI,EACJC,SAAS,EACTH,mBAAmB,EACnBQ,QAAQ,CAACE,QAAQ,EACjBH,QAAQ,EACRH,kBAAkB,EAClBC,gBAAgB,EAChBC,OAAO,CACR,CAAC;;EAEF;EACA,IAAIA,OAAO,EAAE;IACX,oBACET,OAAA;MAAKmB,SAAS,EAAC,2FAA2F;MAAAjB,QAAA,eACxGF,OAAA;QAAKmB,SAAS,EAAC,aAAa;QAAAjB,QAAA,gBAC1BF,OAAA;UAAKmB,SAAS,EAAC;QAA8E;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACpGvB,OAAA;UAAGmB,SAAS,EAAC,eAAe;UAAAjB,QAAA,EAAC;QAAiB;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,OAAOrB,QAAQ;AACjB,CAAC;AAACE,EAAA,CAjFIH,YAAY;EAAA,QAEdH,OAAO,EACQF,WAAW,EACXC,WAAW;AAAA;AAAA2B,EAAA,GAJxBvB,YAAY;AAmFlB,eAAeA,YAAY;AAAC,IAAAuB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}