# =============================================================================
# CHATBOT UNAND - MAIN BACKEND APPLICATION
# =============================================================================

import os
import json
from datetime import datetime
from dotenv import load_dotenv
from contextlib import asynccontextmanager
from typing import List, Dict, Optional
import traceback
import shutil

# FastAPI imports
from fastapi import FastAPI, HTTPException, UploadFile, File, Depends, Header, status, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from fastapi.responses import PlainTextResponse
from pydantic import BaseModel

# Google AI imports
import google.generativeai as genai
import numpy as np

# Document processing
from docx import Document

# Database imports
from sqlalchemy.orm import Session
from database import get_db, create_tables, ChatSession, ChatMessage, User, UserActivity, KnowledgeFile

# Service imports
from services.chat_service import ChatService
from services.auth_service import AuthService
from services.admin_service import AdminService
from services.qdrant_service_pure import QdrantService

# =============================================================================
# ENVIRONMENT CONFIGURATION
# =============================================================================

# Load environment variables
load_dotenv(dotenv_path=os.path.join(os.path.dirname(os.path.dirname(__file__)), '.env'))
load_dotenv()

# API Configuration
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
if not GEMINI_API_KEY:
    raise ValueError("GEMINI_API_KEY not found. Please check your .env file.")

genai.configure(api_key=GEMINI_API_KEY)

# Model Configuration
EMBEDDING_MODEL = "models/text-embedding-004"
GENERATIVE_MODEL = "gemini-1.5-flash"

# Directory Configuration
DATA_DIR = os.path.join(os.path.dirname(__file__), "data")
VECTOR_DB_DIR = os.path.join(os.path.dirname(__file__), "vector_db")

# Vector Database Configuration
USE_QDRANT = os.getenv("USE_QDRANT", "true").lower() == "true"

# =============================================================================
# GLOBAL VARIABLES
# =============================================================================

qdrant_service = None
generative_model = None

# --- Helper Functions for Document Processing ---
def initialize_qdrant():
    """Initialize Qdrant service"""
    global qdrant_service
    try:
        print("Initializing Qdrant service...")
        qdrant_service = QdrantService()

        # Check if collection has data
        info = qdrant_service.get_collection_info()
        print(f"Qdrant collection info: {info}")

        if info.get('points_count', 0) == 0:
            print("Qdrant collection is empty. Please run migration script first.")
        else:
            print(f"Qdrant service initialized with {info.get('points_count', 0)} documents.")

    except Exception as e:
        print(f"Error initializing Qdrant: {e}")
        qdrant_service = None

def extract_text_from_docx(filepath):
    """Extracts text from a .docx file."""
    doc = Document(filepath)
    full_text = []
    for para in doc.paragraphs:
        if para.text.strip():
            full_text.append(para.text.strip())
    return "\n".join(full_text)

def chunk_text(text, max_chars=1000, overlap=100):
    """Chunks text into smaller pieces with optional overlap."""
    chunks = []
    start = 0
    while start < len(text):
        end = min(start + max_chars, len(text))
        chunk = text[start:end]
        chunks.append(chunk)
        if end == len(text):
            break
        start += (max_chars - overlap)
    return chunks

def generate_embeddings_batch(texts: List[str], task_type="RETRIEVAL_DOCUMENT") -> List[List[float]]:
    """Generates embeddings for a list of texts in batches."""
    embeddings = []
    batch_size = 50
    for i in range(0, len(texts), batch_size):
        batch_texts = texts[i:i+batch_size]
        try:
            results = genai.embed_content(model=EMBEDDING_MODEL, content=batch_texts, task_type=task_type)
            embeddings.extend(results['embedding'])
        except Exception as e:
            print(f"Error generating embeddings for batch (index {i}): {e}")
            traceback.print_exc()
            embeddings.extend([np.zeros(768).tolist()] * len(batch_texts))
    return embeddings

async def preprocess_documents_and_build_index():
    """Extract text, chunk, embed, and build/update Qdrant index."""
    print("Starting document pre-processing...")

    os.makedirs(DATA_DIR, exist_ok=True)

    all_chunks_with_metadata = []
    for filename in os.listdir(DATA_DIR):
        if filename.endswith(".docx") and not filename.startswith("~$"):
            filepath = os.path.join(DATA_DIR, filename)
            print(f"Processing {filepath}...")
            try:
                text = extract_text_from_docx(filepath)
                chunks = chunk_text(text)
                # Add metadata for each chunk
                for chunk in chunks:
                    all_chunks_with_metadata.append({
                        "text": chunk,
                        "filename": filename,
                        "filepath": filepath
                    })
            except Exception as e:
                print(f"Error processing {filepath}: {e}")
                continue

    if not all_chunks_with_metadata:
        print("No text extracted from documents. Check your .docx files in backend/data.")
        return

    print(f"Generated {len(all_chunks_with_metadata)} text chunks.")

    # Use Qdrant service to store documents
    if qdrant_service:
        try:
            print("Storing documents in Qdrant...")
            qdrant_service.add_documents(all_chunks_with_metadata)
            print("Document pre-processing complete. Qdrant index updated.")
        except Exception as e:
            print(f"Error storing documents in Qdrant: {e}")
    else:
        print("Qdrant service not available for document storage.")
@asynccontextmanager
async def lifespan(app: FastAPI):
    global generative_model
    generative_model = genai.GenerativeModel(GENERATIVE_MODEL)
    print("Gemini generative model loaded.")

    create_tables()
    print("Database tables created/verified.")

    print("Initializing Qdrant vector database...")
    initialize_qdrant()

    if not qdrant_service:
        print("Failed to initialize Qdrant service!")
    else:
        info = qdrant_service.get_collection_info()
        if info.get('points_count', 0) == 0:
            print("Qdrant collection is empty. Please run migration script to populate data.")

    yield
    print("Application shutting down...")

app = FastAPI(lifespan=lifespan)
security = HTTPBearer()

origins = [
    "http://localhost:3000",
    "http://localhost:3001",
    "http://localhost:3002",
    "http://localhost:8080",
]
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security), db: Session = Depends(get_db)) -> User:
    """Get current authenticated user"""
    auth_service = AuthService(db)
    user_id = auth_service.verify_access_token(credentials.credentials)

    if user_id is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )

    user = auth_service.get_user_by_id(user_id)
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found",
            headers={"WWW-Authenticate": "Bearer"},
        )

    return user


async def get_current_admin(credentials: HTTPAuthorizationCredentials = Depends(security), db: Session = Depends(get_db)) -> str:
    """Get current authenticated admin"""
    admin_service = AdminService(db)

    admin_email = admin_service.verify_admin_token(credentials.credentials)
    if admin_email is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid admin credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )

    return admin_email


async def get_current_user_optional(authorization: str = Header(None), db: Session = Depends(get_db)) -> Optional[User]:
    """Get current user if authenticated, otherwise return None"""
    if not authorization or not authorization.startswith("Bearer "):
        return None

    try:
        token = authorization.split(" ")[1]
        auth_service = AuthService(db)
        user_id = auth_service.verify_access_token(token)

        if user_id is None:
            return None

        return auth_service.get_user_by_id(user_id)
    except:
        return None


class ChatRequest(BaseModel):
    query: str
    session_id: Optional[str] = None

class ChatResponse(BaseModel):
    response: str
    session_id: str
    sources: List[str] = []
    sources_count: int = 0
    summary: Optional[str] = None
    suggestions: Optional[str] = None
    is_greeting: bool = False

class SessionRequest(BaseModel):
    title: Optional[str] = None

class SessionResponse(BaseModel):
    session_id: str
    title: str
    created_at: str
    updated_at: str

class MessageResponse(BaseModel):
    id: int
    message_type: str
    content: str
    timestamp: str
    sources: Optional[List[str]] = None
    summary: Optional[str] = None
    suggestions: Optional[str] = None


class GoogleAuthRequest(BaseModel):
    token: str

class AuthResponse(BaseModel):
    access_token: str
    token_type: str
    user: dict

class UserResponse(BaseModel):
    id: int
    email: str
    name: str
    picture: Optional[str] = None


class AdminLoginRequest(BaseModel):
    email: str
    password: str

class AdminAuthResponse(BaseModel):
    access_token: str
    token_type: str
    admin_email: str

class UserActivityResponse(BaseModel):
    id: int
    user: Optional[dict] = None
    activity_type: str
    session_id: Optional[str] = None
    ip_address: Optional[str] = None
    timestamp: str
    details: Optional[dict] = None

class UserStatsResponse(BaseModel):
    total_users: int
    active_sessions: int
    total_sessions: int
    total_messages: int
    top_users: List[dict]

class KnowledgeFileResponse(BaseModel):
    id: int
    filename: str
    original_filename: str
    file_size: int
    file_type: str
    upload_date: str
    uploaded_by: str
    processed_chunks: int
    last_processed: Optional[str] = None




@app.post("/auth/google", response_model=AuthResponse)
async def google_auth(request: GoogleAuthRequest, db: Session = Depends(get_db), req: Request = None):
    """Authenticate user with Google OAuth token"""
    auth_service = AuthService(db)
    admin_service = AdminService(db)

    try:
        user, access_token = auth_service.authenticate_user(request.token)

        # Log user login activity
        admin_service.log_user_activity(
            user_id=user.id,
            activity_type="login",
            request=req,
            details={"login_method": "google_oauth"}
        )

        return AuthResponse(
            access_token=access_token,
            token_type="bearer",
            user={
                "id": user.id,
                "email": user.email,
                "name": user.name,
                "picture": user.picture
            }
        )
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Authentication failed: {str(e)}"
        )

@app.post("/auth/logout")
async def logout(db: Session = Depends(get_db), current_user: User = Depends(get_current_user_optional)):
    """Logout user - sessions remain active for future access"""
    try:
        if current_user:
            return {
                "message": "Logout successful",
                "user_email": current_user.email
            }
        else:
            return {
                "message": "Logout successful",
                "user_email": None
            }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Logout failed: {str(e)}"
        )

@app.get("/auth/me", response_model=UserResponse)
async def get_current_user_info(current_user: User = Depends(get_current_user)):
    """Get current user information"""
    return UserResponse(
        id=current_user.id,
        email=current_user.email,
        name=current_user.name,
        picture=current_user.picture
    )


@app.options("/chat")
async def options_chat():
    """Handle CORS preflight for /chat endpoint."""
    print("[CHAT] OPTIONS request received")
    return PlainTextResponse(status_code=200)

@app.post("/chat", response_model=ChatResponse)
async def chat_with_rag(request: ChatRequest, db: Session = Depends(get_db), current_user: User = Depends(get_current_user_optional)):
    print("[CHAT] ===== CHAT ENDPOINT CALLED =====")
    print(f"[CHAT] Request received at: {datetime.utcnow()}")
    print(f"[CHAT] Request type: {type(request)}")
    print(f"[CHAT] Raw request data: {request}")

    print(f"[CHAT] Received request: {request.query}")
    print(f"[CHAT] Current user: {current_user.email if current_user else 'Guest'}")
    print(f"[CHAT] Qdrant service status: {qdrant_service is not None}")

    if qdrant_service:
        info = qdrant_service.get_collection_info()
        print(f"[CHAT] Qdrant collection points: {info.get('points_count', 0)}")

    if not qdrant_service:
        print(f"[CHAT] Error: Qdrant service not available")
        raise HTTPException(status_code=500, detail="Sistem belum siap. Vector database sedang diinisialisasi. Mohon tunggu sebentar.")

    query_text = request.query
    chat_service = ChatService(db)

    # Handle session
    session_id = request.session_id
    is_new_session = False
    if not session_id:
        # Create new session with title from first message
        title = chat_service.generate_session_title(query_text)
        user_id = current_user.id if current_user else None
        print(f"[CHAT] Creating new session for user_id: {user_id}, title: {title}")
        session_id = chat_service.create_session(title, user_id)
        print(f"[CHAT] Created session with ID: {session_id}")
        is_new_session = True

    # Save user message
    chat_service.add_message(session_id, "user", query_text)

    # Check if this is a greeting message (new session and simple greeting) or test question
    greeting_keywords = ["halo", "hai", "hello", "hi", "selamat", "assalamualaikum", "permisi"]
    test_keywords = ["tes", "test"]
    is_greeting = is_new_session and any(keyword in query_text.lower() for keyword in greeting_keywords)
    is_test_question = query_text.lower().strip() in test_keywords

    # If it's a greeting or test question, return appropriate response without RAG
    if is_greeting or is_test_question:
        if is_test_question:
            response_text = "Halo! Saya adalah chatbot Universitas Andalas yang siap membantu Anda dengan informasi seputar peraturan akademik dan kebijakan universitas. Anda dapat bertanya tentang berbagai topik seperti syarat kelulusan, sanksi akademik, peraturan studi, dan informasi akademik lainnya. Silakan ajukan pertanyaan Anda!"
        else:
            response_text = "Halo! Saya adalah Chatbot UNAND. Saya siap membantu Anda dengan pertanyaan seputar peraturan kampus dan pemerintah. Silakan ajukan pertanyaan Anda!"

        # Save bot response without sources for greeting/test
        chat_service.add_message(session_id, "bot", response_text, [], None, None)

        return ChatResponse(
            response=response_text,
            session_id=session_id,
            sources=[],
            sources_count=0,
            summary=None,
            suggestions=None,
            is_greeting=True
        )

    try:
        # Check if Qdrant service is available
        if not qdrant_service:
            return {
                "response": "Maaf, sistem pencarian sedang tidak tersedia. Silakan coba lagi nanti.",
                "session_id": session_id,
                "sources": [],
                "sources_count": 0,
                "summary": None,
                "suggestions": None
            }

        # 1. Search using Qdrant
        k = 15  # Number of top relevant chunks to retrieve
        score_threshold = 0.6  # Score threshold for relevance

        search_results = qdrant_service.search(
            query=query_text,
            limit=k,
            score_threshold=score_threshold
        )

        # Convert Qdrant results to the expected format
        relevant_chunks_with_metadata = []
        for result in search_results:
            chunk_data = {
                "text": result["text"],
                "filename": result["filename"],
                "filepath": result["filepath"],
                "similarity_score": result["score"]
            }
            relevant_chunks_with_metadata.append(chunk_data)

        # If no chunks meet threshold, get top results anyway (fallback)
        if not relevant_chunks_with_metadata:
            fallback_results = qdrant_service.search(
                query=query_text,
                limit=8,
                score_threshold=0.0  # No threshold for fallback
            )
            for result in fallback_results:
                chunk_data = {
                    "text": result["text"],
                    "filename": result["filename"],
                    "filepath": result["filepath"],
                    "similarity_score": result["score"]
                }
                relevant_chunks_with_metadata.append(chunk_data)
        else:
            # Limit to top 8 chunks for context
            relevant_chunks_with_metadata = relevant_chunks_with_metadata[:8]

        # Extract text for processing and group by filename
        chunks_by_file = {}
        for chunk_data in relevant_chunks_with_metadata:
            filename = chunk_data["filename"]
            if filename not in chunks_by_file:
                chunks_by_file[filename] = []
            chunks_by_file[filename].append(chunk_data["text"])

        # Debug info with enhanced retrieval metrics
        print(f"Query: {query_text}")
        print(f"Retrieval Config: k={k}, score_threshold={score_threshold}, max_context_chunks=8")
        print(f"Found {len(relevant_chunks_with_metadata)} relevant chunks from {len(chunks_by_file)} files")

        # Show similarity scores for debugging
        for i, chunk_data in enumerate(relevant_chunks_with_metadata[:5]):  # Show top 5
            score = chunk_data.get('similarity_score', 'N/A')
            print(f"Chunk {i+1}: similarity={score:.3f}" if score != 'N/A' else f"Chunk {i+1}: similarity=N/A")

        for filename, chunks in chunks_by_file.items():
            print(f"File {filename}: {len(chunks)} chunks")

        if not relevant_chunks_with_metadata:
            return {"response": "Maaf, saya tidak menemukan informasi relevan dalam dokumen peraturan yang ada untuk pertanyaan Anda."}

        # 3. Construct prompt for Gemini (RAG approach)
        # Combine all chunks for context
        all_relevant_texts = [chunk_data["text"] for chunk_data in relevant_chunks_with_metadata]
        context = "\n\n".join(all_relevant_texts)

        # Create sources list from relevant chunks (will be updated later with detailed answer)
        sources = [f"Dokumen {i+1}: {chunk_data['text'][:100]}..." for i, chunk_data in enumerate(relevant_chunks_with_metadata)]

        # Detect question type and create appropriate prompt
        query_lower = query_text.lower()
        is_dropout_question = any(keyword in query_lower for keyword in ['drop out', 'dropout', 'sanksi', 'pemutusan', 'dikeluarkan'])

        if is_dropout_question:
            # Specific format for dropout questions
            short_prompt = f"""Anda adalah asisten AI untuk Universitas Andalas. Jawab pertanyaan tentang syarat drop out mahasiswa dengan format yang PERSIS seperti contoh.

WAJIB IKUTI FORMAT INI:

Berdasarkan Peraturan Rektor Universitas Andalas Nomor 7 Tahun 2022 tentang Penyelenggaraan Pendidikan, mahasiswa dapat terkena sanksi pemutusan hubungan studi (drop out) dengan beberapa syarat khusus, tergantung jenjang program pendidikannya:

1. Program Sarjana (S1)
Mahasiswa dinyatakan drop out apabila:
• Sampai 4 semester efektif hanya menyelesaikan kurang dari 40 SKS atau memiliki IPK kurang dari 2,00.
• Sampai akhir semester XIV belum menyelesaikan kurikulum, memiliki nilai D, atau IPK kurang dari 2,00.

2. Program Diploma III
Drop out diberlakukan apabila:
• Sampai semester IV hanya menyelesaikan kurang dari 40 SKS atau IPK < 2,00.
• Sampai akhir semester X belum menyelesaikan kurikulum, memiliki nilai D atau E, atau IPK < 2,00.

[Lanjutkan untuk jenjang lainnya dengan format yang sama]

DOKUMEN PERATURAN:
{context}

PERTANYAAN: {query_text}

JAWABAN (gunakan format PERSIS seperti contoh di atas):"""
        else:
            # General academic question format
            short_prompt = f"""Anda adalah asisten AI untuk Universitas Andalas yang ahli dalam peraturan akademik.

INSTRUKSI:
- Berikan jawaban yang akurat berdasarkan dokumen peraturan yang tersedia
- Gunakan format yang rapi dengan bullet points dan numbering jika diperlukan
- Sertakan referensi peraturan yang spesifik jika tersedia
- Gunakan bahasa Indonesia yang formal namun mudah dipahami
- Jika informasi tidak tersedia dalam dokumen, sampaikan dengan jelas
- Berikan jawaban yang relevan dengan pertanyaan yang diajukan

DOKUMEN PERATURAN:
{context}

PERTANYAAN: {query_text}

JAWABAN:"""

        # Generate detailed structured answer for each document separately
        detailed_answers_per_doc = []
        relevant_documents_found = False

        for filename, chunks in chunks_by_file.items():
            # Combine chunks from the same file
            combined_text = "\n\n".join(chunks)

            doc_prompt = (
                "Anda adalah asisten AI yang cerdas dan informatif untuk Universitas Andalas. "
                "Berikan jawaban terstruktur berdasarkan HANYA dokumen yang diberikan di bawah ini.\n\n"

                "INSTRUKSI FORMAT JAWABAN:\n"
                "1. Gunakan format yang rapi dengan heading, bullet points, dan numbering\n"
                "2. Pisahkan informasi menjadi bagian-bagian yang logis\n"
                "3. Gunakan **bold** untuk poin penting dan *italic* untuk penekanan\n"
                "4. Sertakan referensi peraturan (pasal, ayat, nomor) dengan jelas\n"
                "5. Berikan jawaban lengkap dan detail dari dokumen ini saja\n"
                "6. Gunakan bahasa Indonesia yang formal namun mudah dipahami\n"
                "7. Jika dokumen tidak relevan dengan pertanyaan, jawab 'Tidak ada informasi relevan dalam dokumen ini'\n\n"

                "STRUKTUR JAWABAN:\n"
                "- Mulai dengan penjelasan utama dari dokumen ini\n"
                "- Buat poin-poin terorganisir dengan baik\n"
                "- Sertakan detail spesifik dari peraturan dalam dokumen ini\n"
                "- Jawaban harus lengkap berdasarkan dokumen ini\n\n"

                f"DOKUMEN: {filename}\n"
                f"ISI:\n{combined_text}\n\n"

                "PERTANYAAN: " + query_text + "\n\n"

                "JAWABAN BERDASARKAN DOKUMEN INI:"
            )

            doc_response = generative_model.generate_content(doc_prompt)
            doc_answer = doc_response.text if doc_response.text else "Tidak ada informasi relevan dalam dokumen ini."

            # Only add if the answer is relevant (not just "Tidak ada informasi relevan")
            if doc_answer and "tidak ada informasi relevan" not in doc_answer.lower():
                detailed_answers_per_doc.append(f"{filename}: {doc_answer}")
                relevant_documents_found = True

        # 4. Generate short response for main display
        short_response = generative_model.generate_content(short_prompt)
        if not short_response.text:
            bot_response = "Maaf, saya tidak dapat menghasilkan jawaban yang relevan saat ini. Mungkin pertanyaan Anda sensitif atau tidak sesuai dengan peraturan yang ada."
        else:
            bot_response = short_response.text

        # 5. Replace sources with detailed answers per document only if relevant documents found
        if relevant_documents_found:
            sources = detailed_answers_per_doc
        else:
            sources = []  # No sources if no relevant documents found

        # 6. Generate summary using a separate prompt
        summary_prompt = (
            "Berdasarkan jawaban berikut, buatlah kesimpulan singkat dalam 2-3 kalimat yang merangkum poin utama:\n\n"
            f"JAWABAN: {bot_response}\n\n"
            "KESIMPULAN:"
        )

        summary_response = generative_model.generate_content(summary_prompt)
        summary = summary_response.text if summary_response.text else None

        # 7. Generate suggestions if relevant
        suggestions_prompt = (
            f"Berdasarkan pertanyaan '{query_text}' dan jawaban yang diberikan, "
            "berikan 2-3 saran praktis yang dapat membantu pengguna. "
            "Saran bisa berupa langkah-langkah tindak lanjut, rekomendasi, atau tips yang relevan. "
            "Jika benar-benar tidak ada saran yang relevan, jawab 'Tidak ada saran khusus'.\n\n"
            f"JAWABAN: {bot_response}\n\n"
            "SARAN PRAKTIS:"
        )

        suggestions_response = generative_model.generate_content(suggestions_prompt)
        suggestions = suggestions_response.text if suggestions_response.text else None

        # Don't include suggestions if it's just "Tidak ada saran khusus"
        if suggestions and "tidak ada saran" in suggestions.lower():
            suggestions = None

        # Save bot response
        chat_service.add_message(session_id, "bot", bot_response, sources, summary, suggestions)

        return ChatResponse(
            response=bot_response,
            session_id=session_id,
            sources=sources,
            sources_count=len(sources),
            summary=summary,
            suggestions=suggestions,
            is_greeting=False
        )

    except Exception as e:
        print(f"Error during chat processing for query '{query_text}': {e}")
        # Log the full traceback for debugging in development
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Terjadi kesalahan internal saat memproses pertanyaan Anda: {e}. Mohon coba lagi.")

# Session management endpoints
@app.post("/sessions", response_model=SessionResponse)
async def create_session(request: SessionRequest, db: Session = Depends(get_db), current_user: User = Depends(get_current_user_optional)):
    """Create a new chat session"""
    chat_service = ChatService(db)
    user_id = current_user.id if current_user else None
    session_id = chat_service.create_session(request.title or "New Chat", user_id)
    session = chat_service.get_session(session_id)

    return SessionResponse(
        session_id=session.session_id,
        title=session.title,
        created_at=session.created_at.isoformat(),
        updated_at=session.updated_at.isoformat()
    )

@app.get("/sessions", response_model=List[SessionResponse])
async def get_sessions(db: Session = Depends(get_db), current_user: User = Depends(get_current_user_optional)):
    """Get all chat sessions for current user"""
    print(f"[SESSIONS] Get sessions called")
    print(f"[SESSIONS] Current user: {current_user.email if current_user else 'Guest'}")

    chat_service = ChatService(db)
    if current_user:
        print(f"[SESSIONS] Loading sessions for user ID: {current_user.id}")
        sessions = chat_service.get_all_sessions(current_user.id)
        print(f"[SESSIONS] Found {len(sessions)} sessions for user {current_user.email}")
    else:
        # Return empty list for guest users
        print(f"[SESSIONS] Guest user - returning empty sessions")
        sessions = []

    return [
        SessionResponse(
            session_id=session.session_id,
            title=session.title,
            created_at=session.created_at.isoformat(),
            updated_at=session.updated_at.isoformat()
        )
        for session in sessions
    ]

@app.get("/sessions/{session_id}/messages", response_model=List[MessageResponse])
async def get_session_messages(session_id: str, db: Session = Depends(get_db), current_user: User = Depends(get_current_user_optional)):
    """Get all messages for a session"""
    chat_service = ChatService(db)

    # Verify session belongs to current user
    session = chat_service.get_session(session_id)
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")

    # If user is logged in, check if session belongs to them
    if current_user and session.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied to this session")

    # If user is not logged in and session has a user_id, deny access
    if not current_user and session.user_id is not None:
        raise HTTPException(status_code=401, detail="Authentication required")

    messages = chat_service.get_messages(session_id)

    return [
        MessageResponse(
            id=msg.id,
            message_type=msg.message_type,
            content=msg.content,
            timestamp=msg.timestamp.isoformat(),
            sources=json.loads(msg.sources) if msg.sources else None,
            summary=msg.summary,
            suggestions=msg.suggestions
        )
        for msg in messages
    ]

@app.put("/sessions/{session_id}")
async def update_session(session_id: str, request: SessionRequest, db: Session = Depends(get_db), current_user: User = Depends(get_current_user_optional)):
    """Update session title"""
    chat_service = ChatService(db)

    # Verify session belongs to current user
    session = chat_service.get_session(session_id)
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")

    # If user is logged in, check if session belongs to them
    if current_user and session.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied to this session")

    # If user is not logged in and session has a user_id, deny access
    if not current_user and session.user_id is not None:
        raise HTTPException(status_code=401, detail="Authentication required")

    success = chat_service.update_session_title(session_id, request.title)

    if not success:
        raise HTTPException(status_code=404, detail="Session not found")

    return {"message": "Session updated successfully"}

@app.delete("/sessions/{session_id}")
async def delete_session(session_id: str, db: Session = Depends(get_db), current_user: User = Depends(get_current_user_optional)):
    """Delete a session"""
    chat_service = ChatService(db)

    # Verify session belongs to current user
    session = chat_service.get_session(session_id)
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")

    # If user is logged in, check if session belongs to them
    if current_user and session.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied to this session")

    # If user is not logged in and session has a user_id, deny access
    if not current_user and session.user_id is not None:
        raise HTTPException(status_code=401, detail="Authentication required")

    success = chat_service.delete_session(session_id)

    if not success:
        raise HTTPException(status_code=404, detail="Session not found")

    return {"message": "Session deleted successfully"}

# Handle CORS preflight for /upload-document explicitly


# Admin endpoints
@app.post("/admin/login", response_model=AdminAuthResponse)
async def admin_login(request: AdminLoginRequest, db: Session = Depends(get_db)):
    """Admin login with email and password"""
    admin_service = AdminService(db)

    if not admin_service.authenticate_admin(request.email, request.password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid admin credentials"
        )

    access_token = admin_service.create_admin_token(request.email)

    return AdminAuthResponse(
        access_token=access_token,
        token_type="bearer",
        admin_email=request.email
    )

@app.get("/admin/dashboard")
async def get_admin_dashboard(admin_email: str = Depends(get_current_admin), db: Session = Depends(get_db)):
    """Get admin dashboard data"""
    admin_service = AdminService(db)

    user_stats = admin_service.get_user_stats()
    user_activities = admin_service.get_user_activities(limit=50)
    all_users = admin_service.get_all_users()

    return {
        "user_stats": user_stats,
        "recent_activities": user_activities,
        "all_users": all_users
    }

@app.get("/admin/users/activities")
async def get_user_activities(
    limit: int = 100,
    admin_email: str = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """Get user activities log"""
    admin_service = AdminService(db)
    return admin_service.get_user_activities(limit)

@app.get("/admin/users/stats")
async def get_user_stats(admin_email: str = Depends(get_current_admin), db: Session = Depends(get_db)):
    """Get user statistics"""
    admin_service = AdminService(db)
    return admin_service.get_user_stats()

@app.get("/admin/files")
async def get_knowledge_files(admin_email: str = Depends(get_current_admin), db: Session = Depends(get_db)):
    """Get all knowledge base files"""
    admin_service = AdminService(db)
    return admin_service.get_knowledge_files()

@app.get("/admin/users")
async def get_all_users(admin_email: str = Depends(get_current_admin), db: Session = Depends(get_db)):
    """Get all users with their statistics"""
    admin_service = AdminService(db)
    return admin_service.get_all_users()

@app.post("/admin/files/upload")
async def upload_knowledge_file(
    file: UploadFile = File(...),
    admin_email: str = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """Upload new knowledge base file"""
    admin_service = AdminService(db)

    # Validate file type
    if not file.filename.endswith('.docx'):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Only .docx files are supported"
        )

    try:
        # Save file to data directory
        data_dir = os.path.join(os.path.dirname(__file__), "data")
        os.makedirs(data_dir, exist_ok=True)

        file_path = os.path.join(data_dir, file.filename)

        # Check if file already exists
        if os.path.exists(file_path):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"File {file.filename} already exists"
            )

        # Save file
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)

        # Get file size
        file_size = os.path.getsize(file_path)

        # Add to database
        knowledge_file = admin_service.add_knowledge_file(
            filename=file.filename,
            original_filename=file.filename,
            file_path=file_path,
            file_size=file_size,
            file_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            uploaded_by=admin_email
        )

        # Trigger reindexing immediately after upload
        print("File uploaded successfully. Rebuilding knowledge base...")
        try:
            await preprocess_documents_and_build_index()
            print("Knowledge base rebuilt successfully after file upload.")

            return {
                "message": "File uploaded successfully and knowledge base updated",
                "file_id": knowledge_file.id,
                "filename": knowledge_file.filename,
                "file_size": knowledge_file.file_size,
                "knowledge_base_updated": True
            }
        except Exception as rebuild_error:
            print(f"Error rebuilding knowledge base after upload: {rebuild_error}")
            return {
                "message": "File uploaded successfully but knowledge base update failed",
                "file_id": knowledge_file.id,
                "filename": knowledge_file.filename,
                "file_size": knowledge_file.file_size,
                "knowledge_base_updated": False,
                "error": str(rebuild_error)
            }

    except Exception as e:
        # Clean up file if database operation failed
        if os.path.exists(file_path):
            os.remove(file_path)

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to upload file: {str(e)}"
        )

@app.delete("/admin/files/{file_id}")
async def delete_knowledge_file(
    file_id: int,
    admin_email: str = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """Delete knowledge base file and rebuild index"""
    admin_service = AdminService(db)

    if admin_service.delete_knowledge_file(file_id):
        # Rebuild knowledge base after file deletion
        print("File deleted successfully. Rebuilding knowledge base...")
        try:
            await preprocess_documents_and_build_index()
            print("Knowledge base rebuilt successfully after file deletion.")
            return {
                "message": "File deleted successfully and knowledge base updated",
                "knowledge_base_updated": True
            }
        except Exception as e:
            print(f"Error rebuilding knowledge base after deletion: {e}")
            return {
                "message": "File deleted successfully but knowledge base update failed",
                "knowledge_base_updated": False,
                "error": str(e)
            }
    else:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="File not found"
        )

@app.get("/health")
async def health_check():
    """Endpoint to check if the server is running and Qdrant is available."""
    if qdrant_service:
        info = qdrant_service.get_collection_info()
        points_count = info.get('points_count', 0)
        status = "OK" if points_count > 0 else "No_Data"
        return {
            "status": status,
            "qdrant_available": True,
            "points_count": points_count,
            "collection_info": info
        }
    else:
        return {
            "status": "Service_Unavailable",
            "qdrant_available": False,
            "points_count": 0,
            "collection_info": {}
        }



# Run the server
if __name__ == "__main__":
    import uvicorn
    print("Starting FastAPI server...")
    uvicorn.run(app, host="0.0.0.0", port=8000)