{"ast": null, "code": "// packages/react/use-previous/src/use-previous.tsx\nimport * as React from \"react\";\nfunction usePrevious(value) {\n  const ref = React.useRef({\n    value,\n    previous: value\n  });\n  return React.useMemo(() => {\n    if (ref.current.value !== value) {\n      ref.current.previous = ref.current.value;\n      ref.current.value = value;\n    }\n    return ref.current.previous;\n  }, [value]);\n}\nexport { usePrevious };", "map": {"version": 3, "names": ["React", "usePrevious", "value", "ref", "useRef", "previous", "useMemo", "current"], "sources": ["D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\node_modules\\@radix-ui\\react-use-previous\\src\\use-previous.tsx"], "sourcesContent": ["import * as React from 'react';\n\nfunction usePrevious<T>(value: T) {\n  const ref = React.useRef({ value, previous: value });\n\n  // We compare values before making an update to ensure that\n  // a change has been made. This ensures the previous value is\n  // persisted correctly between renders.\n  return React.useMemo(() => {\n    if (ref.current.value !== value) {\n      ref.current.previous = ref.current.value;\n      ref.current.value = value;\n    }\n    return ref.current.previous;\n  }, [value]);\n}\n\nexport { usePrevious };\n"], "mappings": ";AAAA,YAAYA,KAAA,MAAW;AAEvB,SAASC,YAAeC,KAAA,EAAU;EAChC,MAAMC,GAAA,GAAYH,KAAA,CAAAI,MAAA,CAAO;IAAEF,KAAA;IAAOG,QAAA,EAAUH;EAAM,CAAC;EAKnD,OAAaF,KAAA,CAAAM,OAAA,CAAQ,MAAM;IACzB,IAAIH,GAAA,CAAII,OAAA,CAAQL,KAAA,KAAUA,KAAA,EAAO;MAC/BC,GAAA,CAAII,OAAA,CAAQF,QAAA,GAAWF,GAAA,CAAII,OAAA,CAAQL,KAAA;MACnCC,GAAA,CAAII,OAAA,CAAQL,KAAA,GAAQA,KAAA;IACtB;IACA,OAAOC,GAAA,CAAII,OAAA,CAAQF,QAAA;EACrB,GAAG,CAACH,KAAK,CAAC;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}