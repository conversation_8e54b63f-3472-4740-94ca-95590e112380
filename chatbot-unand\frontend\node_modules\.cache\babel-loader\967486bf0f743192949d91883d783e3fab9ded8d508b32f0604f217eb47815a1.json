{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M20.341 6.484A10 10 0 0 1 10.266 21.85\",\n  key: \"1enhxb\"\n}], [\"path\", {\n  d: \"M3.659 17.516A10 10 0 0 1 13.74 2.152\",\n  key: \"1crzgf\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"3\",\n  key: \"1v7zrd\"\n}], [\"circle\", {\n  cx: \"19\",\n  cy: \"5\",\n  r: \"2\",\n  key: \"mhkx31\"\n}], [\"circle\", {\n  cx: \"5\",\n  cy: \"19\",\n  r: \"2\",\n  key: \"v8kfzx\"\n}]];\nconst Orbit = createLucideIcon(\"orbit\", __iconNode);\nexport { __iconNode, Orbit as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "cx", "cy", "r", "Orbit", "createLucideIcon"], "sources": ["D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\node_modules\\lucide-react\\src\\icons\\orbit.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M20.341 6.484A10 10 0 0 1 10.266 21.85', key: '1enhxb' }],\n  ['path', { d: 'M3.659 17.516A10 10 0 0 1 13.74 2.152', key: '1crzgf' }],\n  ['circle', { cx: '12', cy: '12', r: '3', key: '1v7zrd' }],\n  ['circle', { cx: '19', cy: '5', r: '2', key: 'mhkx31' }],\n  ['circle', { cx: '5', cy: '19', r: '2', key: 'v8kfzx' }],\n];\n\n/**\n * @component @name Orbit\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjAuMzQxIDYuNDg0QTEwIDEwIDAgMCAxIDEwLjI2NiAyMS44NSIgLz4KICA8cGF0aCBkPSJNMy42NTkgMTcuNTE2QTEwIDEwIDAgMCAxIDEzLjc0IDIuMTUyIiAvPgogIDxjaXJjbGUgY3g9IjEyIiBjeT0iMTIiIHI9IjMiIC8+CiAgPGNpcmNsZSBjeD0iMTkiIGN5PSI1IiByPSIyIiAvPgogIDxjaXJjbGUgY3g9IjUiIGN5PSIxOSIgcj0iMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/orbit\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Orbit = createLucideIcon('orbit', __iconNode);\n\nexport default Orbit;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,wCAA0C;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvE,CAAC,MAAQ;EAAED,CAAA,EAAG,uCAAyC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACtE,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKH,GAAK;AAAA,CAAU,GACxD,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAKC,CAAG;EAAKH,GAAK;AAAA,CAAU,GACvD,CAAC,QAAU;EAAEC,EAAI;EAAKC,EAAI;EAAMC,CAAG;EAAKH,GAAK;AAAU,GACzD;AAaM,MAAAI,KAAA,GAAQC,gBAAiB,UAASP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}