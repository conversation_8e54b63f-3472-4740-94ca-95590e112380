{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website kp\\\\chatbot-unand\\\\frontend\\\\src\\\\components\\\\Login.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from \"react\";\nimport { useAuth } from \"../contexts/AuthContext\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Login = () => {\n  _s();\n  const {\n    login,\n    isSessionConflict,\n    clearAllSessions\n  } = useAuth();\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const googleButtonRef = useRef(null);\n  const handleCredentialResponse = async response => {\n    console.log(\"Google credential response:\", response);\n    setLoading(true);\n    setError(null);\n\n    // Check for session conflicts before login\n    if (isSessionConflict(\"user\")) {\n      setError(\"Sesi admin aktif terdeteksi. <PERSON>lakan logout dari admin panel terlebih dahulu atau refresh halaman untuk memulai sesi user baru.\");\n      setLoading(false);\n      return;\n    }\n    try {\n      console.log(\"Attempting login with token...\");\n      await login(response.credential);\n      console.log(\"Login successful!\");\n    } catch (error) {\n      console.error(\"Login error details:\", error);\n      setError(`Login failed: ${error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleClearSessions = () => {\n    clearAllSessions();\n    setError(null);\n    window.location.reload();\n  };\n\n  // Initialize Google Sign-In function\n  const initializeGoogleSignIn = () => {\n    if (window.google && window.google.accounts) {\n      console.log(\"✅ Initializing Google Sign-In...\");\n      console.log(\"🔑 Client ID:\", process.env.REACT_APP_GOOGLE_CLIENT_ID);\n      console.log(\"🌐 Current origin:\", window.location.origin);\n\n      // Clear any existing Google session first\n      try {\n        window.google.accounts.id.disableAutoSelect();\n        console.log(\"🧹 Cleared existing Google auto-select\");\n      } catch (e) {\n        console.log(\"ℹ️ No existing Google session to clear\");\n      }\n      window.google.accounts.id.initialize({\n        client_id: process.env.REACT_APP_GOOGLE_CLIENT_ID,\n        callback: handleCredentialResponse,\n        auto_select: false,\n        cancel_on_tap_outside: true,\n        use_fedcm_for_prompt: false,\n        // Disable FedCM to avoid conflicts\n        itp_support: true // Enable Intelligent Tracking Prevention support\n      });\n\n      // Render the Google Sign-In button\n      if (googleButtonRef.current) {\n        window.google.accounts.id.renderButton(googleButtonRef.current, {\n          theme: \"outline\",\n          size: \"large\",\n          width: \"100%\",\n          text: \"signin_with\",\n          locale: \"id\"\n        });\n      }\n    } else {\n      console.error(\"Google Identity Services not loaded\");\n      setError(\"Google Identity Services tidak dapat dimuat\");\n    }\n  };\n  useEffect(() => {\n    // Initialize Google Sign-In when component mounts\n\n    // Wait for Google script to load\n    const checkGoogleLoaded = () => {\n      if (window.google && window.google.accounts) {\n        initializeGoogleSignIn();\n      } else {\n        setTimeout(checkGoogleLoaded, 100);\n      }\n    };\n    checkGoogleLoaded();\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []); // Remove dependency to avoid infinite loop\n\n  const handleManualLogin = () => {\n    if (window.google && window.google.accounts) {\n      window.google.accounts.id.prompt();\n    } else {\n      setError(\"Google Identity Services tidak tersedia\");\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-r from-green-50 to-yellow-50 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-md w-full mx-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white dark:bg-gray-800 rounded-lg shadow-xl p-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mx-auto w-20 h-20 bg-gradient-to-r from-green-600 to-yellow-500 rounded-full flex items-center justify-center mb-4\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-white text-2xl font-bold\",\n              children: \"U\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-2xl font-bold text-gray-900 dark:text-white mb-2\",\n            children: \"Chatbot UNAND\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 dark:text-gray-400 text-sm\",\n            children: \"UNTUK KEDJAJAAN BANGSA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-500 dark:text-gray-500 text-sm mt-2\",\n            children: \"Masuk untuk mengakses chatbot peraturan kampus\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4 p-3 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-lg\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-red-700 dark:text-red-300 text-sm\",\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 15\n          }, this), isSessionConflict(\"user\") && /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleClearSessions,\n            className: \"mt-2 text-sm text-red-600 hover:text-red-800 underline\",\n            children: \"Hapus semua sesi dan mulai ulang\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: googleButtonRef,\n            className: \"w-full\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this), loading && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center py-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-green-600 mr-3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-gray-600 dark:text-gray-400\",\n            children: \"Memproses login...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleManualLogin,\n          disabled: loading,\n          className: \"w-full flex items-center justify-center px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors mt-2\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-5 h-5 mr-3\",\n              viewBox: \"0 0 24 24\",\n              children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                fill: \"currentColor\",\n                d: \"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                fill: \"currentColor\",\n                d: \"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                fill: \"currentColor\",\n                d: \"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                fill: \"currentColor\",\n                d: \"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this), \"Login Manual (jika tombol di atas tidak muncul)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-6 text-center\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs text-gray-500 dark:text-gray-400\",\n            children: \"Dengan masuk, Anda menyetujui penggunaan data untuk keperluan chatbot\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 107,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"hJXh5J1jFbjN1cfFsKSOMxd0GKE=\", false, function () {\n  return [useAuth];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useAuth", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "_s", "login", "isSessionConflict", "clearAllSessions", "loading", "setLoading", "error", "setError", "googleButtonRef", "handleCredentialResponse", "response", "console", "log", "credential", "message", "handleClearSessions", "window", "location", "reload", "initializeGoogleSignIn", "google", "accounts", "process", "env", "REACT_APP_GOOGLE_CLIENT_ID", "origin", "id", "disableAutoSelect", "e", "initialize", "client_id", "callback", "auto_select", "cancel_on_tap_outside", "use_fedcm_for_prompt", "itp_support", "current", "renderButton", "theme", "size", "width", "text", "locale", "checkGoogleLoaded", "setTimeout", "handleManualLogin", "prompt", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "ref", "disabled", "viewBox", "fill", "d", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/website kp/chatbot-unand/frontend/src/components/Login.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from \"react\";\nimport { useAuth } from \"../contexts/AuthContext\";\n\nconst Login = () => {\n  const { login, isSessionConflict, clearAllSessions } = useAuth();\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const googleButtonRef = useRef(null);\n\n  const handleCredentialResponse = async (response) => {\n    console.log(\"Google credential response:\", response);\n    setLoading(true);\n    setError(null);\n\n    // Check for session conflicts before login\n    if (isSessionConflict(\"user\")) {\n      setError(\n        \"Sesi admin aktif terdeteksi. Silakan logout dari admin panel terlebih dahulu atau refresh halaman untuk memulai sesi user baru.\"\n      );\n      setLoading(false);\n      return;\n    }\n\n    try {\n      console.log(\"Attempting login with token...\");\n      await login(response.credential);\n      console.log(\"Login successful!\");\n    } catch (error) {\n      console.error(\"Login error details:\", error);\n      setError(`Login failed: ${error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleClearSessions = () => {\n    clearAllSessions();\n    setError(null);\n    window.location.reload();\n  };\n\n  // Initialize Google Sign-In function\n  const initializeGoogleSignIn = () => {\n    if (window.google && window.google.accounts) {\n      console.log(\"✅ Initializing Google Sign-In...\");\n      console.log(\"🔑 Client ID:\", process.env.REACT_APP_GOOGLE_CLIENT_ID);\n      console.log(\"🌐 Current origin:\", window.location.origin);\n\n      // Clear any existing Google session first\n      try {\n        window.google.accounts.id.disableAutoSelect();\n        console.log(\"🧹 Cleared existing Google auto-select\");\n      } catch (e) {\n        console.log(\"ℹ️ No existing Google session to clear\");\n      }\n\n      window.google.accounts.id.initialize({\n        client_id: process.env.REACT_APP_GOOGLE_CLIENT_ID,\n        callback: handleCredentialResponse,\n        auto_select: false,\n        cancel_on_tap_outside: true,\n        use_fedcm_for_prompt: false, // Disable FedCM to avoid conflicts\n        itp_support: true, // Enable Intelligent Tracking Prevention support\n      });\n\n      // Render the Google Sign-In button\n      if (googleButtonRef.current) {\n        window.google.accounts.id.renderButton(googleButtonRef.current, {\n          theme: \"outline\",\n          size: \"large\",\n          width: \"100%\",\n          text: \"signin_with\",\n          locale: \"id\",\n        });\n      }\n    } else {\n      console.error(\"Google Identity Services not loaded\");\n      setError(\"Google Identity Services tidak dapat dimuat\");\n    }\n  };\n\n  useEffect(() => {\n    // Initialize Google Sign-In when component mounts\n\n    // Wait for Google script to load\n    const checkGoogleLoaded = () => {\n      if (window.google && window.google.accounts) {\n        initializeGoogleSignIn();\n      } else {\n        setTimeout(checkGoogleLoaded, 100);\n      }\n    };\n\n    checkGoogleLoaded();\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []); // Remove dependency to avoid infinite loop\n\n  const handleManualLogin = () => {\n    if (window.google && window.google.accounts) {\n      window.google.accounts.id.prompt();\n    } else {\n      setError(\"Google Identity Services tidak tersedia\");\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-r from-green-50 to-yellow-50 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center\">\n      <div className=\"max-w-md w-full mx-4\">\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-xl p-8\">\n          {/* Logo and Title */}\n          <div className=\"text-center mb-8\">\n            <div className=\"mx-auto w-20 h-20 bg-gradient-to-r from-green-600 to-yellow-500 rounded-full flex items-center justify-center mb-4\">\n              <span className=\"text-white text-2xl font-bold\">U</span>\n            </div>\n            <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-2\">\n              Chatbot UNAND\n            </h1>\n            <p className=\"text-gray-600 dark:text-gray-400 text-sm\">\n              UNTUK KEDJAJAAN BANGSA\n            </p>\n            <p className=\"text-gray-500 dark:text-gray-500 text-sm mt-2\">\n              Masuk untuk mengakses chatbot peraturan kampus\n            </p>\n          </div>\n\n          {/* Error Message */}\n          {error && (\n            <div className=\"mb-4 p-3 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-lg\">\n              <p className=\"text-red-700 dark:text-red-300 text-sm\">{error}</p>\n              {isSessionConflict(\"user\") && (\n                <button\n                  onClick={handleClearSessions}\n                  className=\"mt-2 text-sm text-red-600 hover:text-red-800 underline\"\n                >\n                  Hapus semua sesi dan mulai ulang\n                </button>\n              )}\n            </div>\n          )}\n\n          {/* Google Sign-In Button Container */}\n          <div className=\"mb-4\">\n            <div ref={googleButtonRef} className=\"w-full\"></div>\n          </div>\n\n          {/* Loading State */}\n          {loading && (\n            <div className=\"flex items-center justify-center py-3\">\n              <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-green-600 mr-3\"></div>\n              <span className=\"text-gray-600 dark:text-gray-400\">\n                Memproses login...\n              </span>\n            </div>\n          )}\n\n          {/* Fallback Manual Login Button */}\n          <button\n            onClick={handleManualLogin}\n            disabled={loading}\n            className=\"w-full flex items-center justify-center px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors mt-2\"\n          >\n            <div className=\"flex items-center\">\n              <svg className=\"w-5 h-5 mr-3\" viewBox=\"0 0 24 24\">\n                <path\n                  fill=\"currentColor\"\n                  d=\"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\n                />\n                <path\n                  fill=\"currentColor\"\n                  d=\"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\n                />\n                <path\n                  fill=\"currentColor\"\n                  d=\"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\n                />\n                <path\n                  fill=\"currentColor\"\n                  d=\"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\n                />\n              </svg>\n              Login Manual (jika tombol di atas tidak muncul)\n            </div>\n          </button>\n\n          {/* Footer */}\n          <div className=\"mt-6 text-center\">\n            <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n              Dengan masuk, Anda menyetujui penggunaan data untuk keperluan\n              chatbot\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Login;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM;IAAEC,KAAK;IAAEC,iBAAiB;IAAEC;EAAiB,CAAC,GAAGP,OAAO,CAAC,CAAC;EAChE,MAAM,CAACQ,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACa,KAAK,EAAEC,QAAQ,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAMe,eAAe,GAAGb,MAAM,CAAC,IAAI,CAAC;EAEpC,MAAMc,wBAAwB,GAAG,MAAOC,QAAQ,IAAK;IACnDC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEF,QAAQ,CAAC;IACpDL,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;;IAEd;IACA,IAAIL,iBAAiB,CAAC,MAAM,CAAC,EAAE;MAC7BK,QAAQ,CACN,iIACF,CAAC;MACDF,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;IAEA,IAAI;MACFM,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;MAC7C,MAAMX,KAAK,CAACS,QAAQ,CAACG,UAAU,CAAC;MAChCF,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;IAClC,CAAC,CAAC,OAAON,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CC,QAAQ,CAAC,iBAAiBD,KAAK,CAACQ,OAAO,EAAE,CAAC;IAC5C,CAAC,SAAS;MACRT,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMU,mBAAmB,GAAGA,CAAA,KAAM;IAChCZ,gBAAgB,CAAC,CAAC;IAClBI,QAAQ,CAAC,IAAI,CAAC;IACdS,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMC,sBAAsB,GAAGA,CAAA,KAAM;IACnC,IAAIH,MAAM,CAACI,MAAM,IAAIJ,MAAM,CAACI,MAAM,CAACC,QAAQ,EAAE;MAC3CV,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;MAC/CD,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEU,OAAO,CAACC,GAAG,CAACC,0BAA0B,CAAC;MACpEb,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEI,MAAM,CAACC,QAAQ,CAACQ,MAAM,CAAC;;MAEzD;MACA,IAAI;QACFT,MAAM,CAACI,MAAM,CAACC,QAAQ,CAACK,EAAE,CAACC,iBAAiB,CAAC,CAAC;QAC7ChB,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MACvD,CAAC,CAAC,OAAOgB,CAAC,EAAE;QACVjB,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MACvD;MAEAI,MAAM,CAACI,MAAM,CAACC,QAAQ,CAACK,EAAE,CAACG,UAAU,CAAC;QACnCC,SAAS,EAAER,OAAO,CAACC,GAAG,CAACC,0BAA0B;QACjDO,QAAQ,EAAEtB,wBAAwB;QAClCuB,WAAW,EAAE,KAAK;QAClBC,qBAAqB,EAAE,IAAI;QAC3BC,oBAAoB,EAAE,KAAK;QAAE;QAC7BC,WAAW,EAAE,IAAI,CAAE;MACrB,CAAC,CAAC;;MAEF;MACA,IAAI3B,eAAe,CAAC4B,OAAO,EAAE;QAC3BpB,MAAM,CAACI,MAAM,CAACC,QAAQ,CAACK,EAAE,CAACW,YAAY,CAAC7B,eAAe,CAAC4B,OAAO,EAAE;UAC9DE,KAAK,EAAE,SAAS;UAChBC,IAAI,EAAE,OAAO;UACbC,KAAK,EAAE,MAAM;UACbC,IAAI,EAAE,aAAa;UACnBC,MAAM,EAAE;QACV,CAAC,CAAC;MACJ;IACF,CAAC,MAAM;MACL/B,OAAO,CAACL,KAAK,CAAC,qCAAqC,CAAC;MACpDC,QAAQ,CAAC,6CAA6C,CAAC;IACzD;EACF,CAAC;EAEDb,SAAS,CAAC,MAAM;IACd;;IAEA;IACA,MAAMiD,iBAAiB,GAAGA,CAAA,KAAM;MAC9B,IAAI3B,MAAM,CAACI,MAAM,IAAIJ,MAAM,CAACI,MAAM,CAACC,QAAQ,EAAE;QAC3CF,sBAAsB,CAAC,CAAC;MAC1B,CAAC,MAAM;QACLyB,UAAU,CAACD,iBAAiB,EAAE,GAAG,CAAC;MACpC;IACF,CAAC;IAEDA,iBAAiB,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER,MAAME,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI7B,MAAM,CAACI,MAAM,IAAIJ,MAAM,CAACI,MAAM,CAACC,QAAQ,EAAE;MAC3CL,MAAM,CAACI,MAAM,CAACC,QAAQ,CAACK,EAAE,CAACoB,MAAM,CAAC,CAAC;IACpC,CAAC,MAAM;MACLvC,QAAQ,CAAC,yCAAyC,CAAC;IACrD;EACF,CAAC;EAED,oBACET,OAAA;IAAKiD,SAAS,EAAC,+HAA+H;IAAAC,QAAA,eAC5IlD,OAAA;MAAKiD,SAAS,EAAC,sBAAsB;MAAAC,QAAA,eACnClD,OAAA;QAAKiD,SAAS,EAAC,oDAAoD;QAAAC,QAAA,gBAEjElD,OAAA;UAAKiD,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BlD,OAAA;YAAKiD,SAAS,EAAC,oHAAoH;YAAAC,QAAA,eACjIlD,OAAA;cAAMiD,SAAS,EAAC,+BAA+B;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC,eACNtD,OAAA;YAAIiD,SAAS,EAAC,uDAAuD;YAAAC,QAAA,EAAC;UAEtE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLtD,OAAA;YAAGiD,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAExD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJtD,OAAA;YAAGiD,SAAS,EAAC,+CAA+C;YAAAC,QAAA,EAAC;UAE7D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,EAGL9C,KAAK,iBACJR,OAAA;UAAKiD,SAAS,EAAC,yFAAyF;UAAAC,QAAA,gBACtGlD,OAAA;YAAGiD,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAE1C;UAAK;YAAA2C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAChElD,iBAAiB,CAAC,MAAM,CAAC,iBACxBJ,OAAA;YACEuD,OAAO,EAAEtC,mBAAoB;YAC7BgC,SAAS,EAAC,wDAAwD;YAAAC,QAAA,EACnE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN,eAGDtD,OAAA;UAAKiD,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBlD,OAAA;YAAKwD,GAAG,EAAE9C,eAAgB;YAACuC,SAAS,EAAC;UAAQ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC,EAGLhD,OAAO,iBACNN,OAAA;UAAKiD,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpDlD,OAAA;YAAKiD,SAAS,EAAC;UAAoE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1FtD,OAAA;YAAMiD,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAEnD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN,eAGDtD,OAAA;UACEuD,OAAO,EAAER,iBAAkB;UAC3BU,QAAQ,EAAEnD,OAAQ;UAClB2C,SAAS,EAAC,uWAAuW;UAAAC,QAAA,eAEjXlD,OAAA;YAAKiD,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChClD,OAAA;cAAKiD,SAAS,EAAC,cAAc;cAACS,OAAO,EAAC,WAAW;cAAAR,QAAA,gBAC/ClD,OAAA;gBACE2D,IAAI,EAAC,cAAc;gBACnBC,CAAC,EAAC;cAAyH;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5H,CAAC,eACFtD,OAAA;gBACE2D,IAAI,EAAC,cAAc;gBACnBC,CAAC,EAAC;cAAuI;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1I,CAAC,eACFtD,OAAA;gBACE2D,IAAI,EAAC,cAAc;gBACnBC,CAAC,EAAC;cAA+H;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClI,CAAC,eACFtD,OAAA;gBACE2D,IAAI,EAAC,cAAc;gBACnBC,CAAC,EAAC;cAAqI;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,mDAER;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAGTtD,OAAA;UAAKiD,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC/BlD,OAAA;YAAGiD,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAGxD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpD,EAAA,CAhMID,KAAK;EAAA,QAC8CH,OAAO;AAAA;AAAA+D,EAAA,GAD1D5D,KAAK;AAkMX,eAAeA,KAAK;AAAC,IAAA4D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}