{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M15 5H9\",\n  key: \"1tp3ed\"\n}], [\"path\", {\n  d: \"M15 9v3h4l-7 7-7-7h4V9z\",\n  key: \"ncdc4b\"\n}]];\nconst ArrowBigDownDash = createLucideIcon(\"arrow-big-down-dash\", __iconNode);\nexport { __iconNode, ArrowBigDownDash as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "ArrowBigDownDash", "createLucideIcon"], "sources": ["D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\node_modules\\lucide-react\\src\\icons\\arrow-big-down-dash.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M15 5H9', key: '1tp3ed' }],\n  ['path', { d: 'M15 9v3h4l-7 7-7-7h4V9z', key: 'ncdc4b' }],\n];\n\n/**\n * @component @name ArrowBigDownDash\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTUgNUg5IiAvPgogIDxwYXRoIGQ9Ik0xNSA5djNoNGwtNyA3LTctN2g0Vjl6IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/arrow-big-down-dash\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowBigDownDash = createLucideIcon('arrow-big-down-dash', __iconNode);\n\nexport default ArrowBigDownDash;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,yBAA2B;EAAAC,GAAA,EAAK;AAAU,GAC1D;AAaM,MAAAC,gBAAA,GAAmBC,gBAAiB,wBAAuBJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}