{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 3h.01\",\n  key: \"lbucoy\"\n}], [\"path\", {\n  d: \"M14 2h.01\",\n  key: \"1k8aa1\"\n}], [\"path\", {\n  d: \"m2 9 20-5\",\n  key: \"1kz0j5\"\n}], [\"path\", {\n  d: \"M12 12V6.5\",\n  key: \"1vbrij\"\n}], [\"rect\", {\n  width: \"16\",\n  height: \"10\",\n  x: \"4\",\n  y: \"12\",\n  rx: \"3\",\n  key: \"if91er\"\n}], [\"path\", {\n  d: \"M9 12v5\",\n  key: \"3anwtq\"\n}], [\"path\", {\n  d: \"M15 12v5\",\n  key: \"5xh3zn\"\n}], [\"path\", {\n  d: \"M4 17h16\",\n  key: \"g4d7ey\"\n}]];\nconst CableCar = createLucideIcon(\"cable-car\", __iconNode);\nexport { __iconNode, CableCar as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "width", "height", "x", "y", "rx", "CableCar", "createLucideIcon"], "sources": ["D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\node_modules\\lucide-react\\src\\icons\\cable-car.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M10 3h.01', key: 'lbucoy' }],\n  ['path', { d: 'M14 2h.01', key: '1k8aa1' }],\n  ['path', { d: 'm2 9 20-5', key: '1kz0j5' }],\n  ['path', { d: 'M12 12V6.5', key: '1vbrij' }],\n  ['rect', { width: '16', height: '10', x: '4', y: '12', rx: '3', key: 'if91er' }],\n  ['path', { d: 'M9 12v5', key: '3anwtq' }],\n  ['path', { d: 'M15 12v5', key: '5xh3zn' }],\n  ['path', { d: 'M4 17h16', key: 'g4d7ey' }],\n];\n\n/**\n * @component @name CableCar\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAgM2guMDEiIC8+CiAgPHBhdGggZD0iTTE0IDJoLjAxIiAvPgogIDxwYXRoIGQ9Im0yIDkgMjAtNSIgLz4KICA8cGF0aCBkPSJNMTIgMTJWNi41IiAvPgogIDxyZWN0IHdpZHRoPSIxNiIgaGVpZ2h0PSIxMCIgeD0iNCIgeT0iMTIiIHJ4PSIzIiAvPgogIDxwYXRoIGQ9Ik05IDEydjUiIC8+CiAgPHBhdGggZD0iTTE1IDEydjUiIC8+CiAgPHBhdGggZD0iTTQgMTdoMTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/cable-car\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CableCar = createLucideIcon('cable-car', __iconNode);\n\nexport default CableCar;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,QAAQ;EAAEC,KAAA,EAAO;EAAMC,MAAQ;EAAMC,CAAG;EAAKC,CAAA,EAAG,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAL,GAAA,EAAK;AAAA,CAAU,GAC/E,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAU,GAC3C;AAaM,MAAAM,QAAA,GAAWC,gBAAiB,cAAaT,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}