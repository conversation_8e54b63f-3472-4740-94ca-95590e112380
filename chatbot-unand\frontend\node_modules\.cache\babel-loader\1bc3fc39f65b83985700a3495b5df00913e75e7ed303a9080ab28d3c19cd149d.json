{"ast": null, "code": "// src/use-effect-event.tsx\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nimport * as React from \"react\";\nvar useReactEffectEvent = React[\" useEffectEvent \".trim().toString()];\nvar useReactInsertionEffect = React[\" useInsertionEffect \".trim().toString()];\nfunction useEffectEvent(callback) {\n  if (typeof useReactEffectEvent === \"function\") {\n    return useReactEffectEvent(callback);\n  }\n  const ref = React.useRef(() => {\n    throw new Error(\"Cannot call an event handler while rendering.\");\n  });\n  if (typeof useReactInsertionEffect === \"function\") {\n    useReactInsertionEffect(() => {\n      ref.current = callback;\n    });\n  } else {\n    useLayoutEffect(() => {\n      ref.current = callback;\n    });\n  }\n  return React.useMemo(() => (...args) => ref.current?.(...args), []);\n}\nexport { useEffectEvent };", "map": {"version": 3, "names": ["useLayoutEffect", "React", "useReactEffectEvent", "trim", "toString", "useReactInsertionEffect", "useEffectEvent", "callback", "ref", "useRef", "Error", "current", "useMemo", "args"], "sources": ["D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\node_modules\\@radix-ui\\react-use-effect-event\\src\\use-effect-event.tsx"], "sourcesContent": ["/* eslint-disable react-hooks/rules-of-hooks */\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport * as React from 'react';\n\ntype AnyFunction = (...args: any[]) => any;\n\n// See https://github.com/webpack/webpack/issues/14814\nconst useReactEffectEvent = (React as any)[' useEffectEvent '.trim().toString()];\nconst useReactInsertionEffect = (React as any)[' useInsertionEffect '.trim().toString()];\n\n/**\n * Designed to approximate the behavior on `experimental_useEffectEvent` as best\n * as possible until its stable release, and back-fill it as a shim as needed.\n */\nexport function useEffectEvent<T extends AnyFunction>(callback?: T): T {\n  if (typeof useReactEffectEvent === 'function') {\n    return useReactEffectEvent(callback);\n  }\n\n  const ref = React.useRef<AnyFunction | undefined>(() => {\n    throw new Error('Cannot call an event handler while rendering.');\n  });\n  // See https://github.com/webpack/webpack/issues/14814\n  if (typeof useReactInsertionEffect === 'function') {\n    useReactInsertionEffect(() => {\n      ref.current = callback;\n    });\n  } else {\n    useLayoutEffect(() => {\n      ref.current = callback;\n    });\n  }\n\n  // https://github.com/facebook/react/issues/19240\n  return React.useMemo(() => ((...args) => ref.current?.(...args)) as T, []);\n}\n"], "mappings": ";AACA,SAASA,eAAA,QAAuB;AAChC,YAAYC,KAAA,MAAW;AAKvB,IAAMC,mBAAA,GAAuBD,KAAA,CAAc,mBAAmBE,IAAA,CAAK,EAAEC,QAAA,CAAS,CAAC;AAC/E,IAAMC,uBAAA,GAA2BJ,KAAA,CAAc,uBAAuBE,IAAA,CAAK,EAAEC,QAAA,CAAS,CAAC;AAMhF,SAASE,eAAsCC,QAAA,EAAiB;EACrE,IAAI,OAAOL,mBAAA,KAAwB,YAAY;IAC7C,OAAOA,mBAAA,CAAoBK,QAAQ;EACrC;EAEA,MAAMC,GAAA,GAAYP,KAAA,CAAAQ,MAAA,CAAgC,MAAM;IACtD,MAAM,IAAIC,KAAA,CAAM,+CAA+C;EACjE,CAAC;EAED,IAAI,OAAOL,uBAAA,KAA4B,YAAY;IACjDA,uBAAA,CAAwB,MAAM;MAC5BG,GAAA,CAAIG,OAAA,GAAUJ,QAAA;IAChB,CAAC;EACH,OAAO;IACLP,eAAA,CAAgB,MAAM;MACpBQ,GAAA,CAAIG,OAAA,GAAUJ,QAAA;IAChB,CAAC;EACH;EAGA,OAAaN,KAAA,CAAAW,OAAA,CAAQ,MAAO,IAAIC,IAAA,KAASL,GAAA,CAAIG,OAAA,GAAU,GAAGE,IAAI,GAAS,EAAE;AAC3E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}