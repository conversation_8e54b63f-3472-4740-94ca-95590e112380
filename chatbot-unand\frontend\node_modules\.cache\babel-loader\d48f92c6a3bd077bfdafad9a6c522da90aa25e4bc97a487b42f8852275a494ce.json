{"ast": null, "code": "// packages/react/context/src/create-context.tsx\nimport * as React from \"react\";\nimport { jsx } from \"react/jsx-runtime\";\nfunction createContext2(rootComponentName, defaultContext) {\n  const Context = React.createContext(defaultContext);\n  const Provider = props => {\n    const {\n      children,\n      ...context\n    } = props;\n    const value = React.useMemo(() => context, Object.values(context));\n    return /* @__PURE__ */jsx(Context.Provider, {\n      value,\n      children\n    });\n  };\n  Provider.displayName = rootComponentName + \"Provider\";\n  function useContext2(consumerName) {\n    const context = React.useContext(Context);\n    if (context) return context;\n    if (defaultContext !== void 0) return defaultContext;\n    throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n  }\n  return [Provider, useContext2];\n}\nfunction createContextScope(scopeName, createContextScopeDeps = []) {\n  let defaultContexts = [];\n  function createContext3(rootComponentName, defaultContext) {\n    const BaseContext = React.createContext(defaultContext);\n    const index = defaultContexts.length;\n    defaultContexts = [...defaultContexts, defaultContext];\n    const Provider = props => {\n      const {\n        scope,\n        children,\n        ...context\n      } = props;\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const value = React.useMemo(() => context, Object.values(context));\n      return /* @__PURE__ */jsx(Context.Provider, {\n        value,\n        children\n      });\n    };\n    Provider.displayName = rootComponentName + \"Provider\";\n    function useContext2(consumerName, scope) {\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const context = React.useContext(Context);\n      if (context) return context;\n      if (defaultContext !== void 0) return defaultContext;\n      throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n    return [Provider, useContext2];\n  }\n  const createScope = () => {\n    const scopeContexts = defaultContexts.map(defaultContext => {\n      return React.createContext(defaultContext);\n    });\n    return function useScope(scope) {\n      const contexts = scope?.[scopeName] || scopeContexts;\n      return React.useMemo(() => ({\n        [`__scope${scopeName}`]: {\n          ...scope,\n          [scopeName]: contexts\n        }\n      }), [scope, contexts]);\n    };\n  };\n  createScope.scopeName = scopeName;\n  return [createContext3, composeContextScopes(createScope, ...createContextScopeDeps)];\n}\nfunction composeContextScopes(...scopes) {\n  const baseScope = scopes[0];\n  if (scopes.length === 1) return baseScope;\n  const createScope = () => {\n    const scopeHooks = scopes.map(createScope2 => ({\n      useScope: createScope2(),\n      scopeName: createScope2.scopeName\n    }));\n    return function useComposedScopes(overrideScopes) {\n      const nextScopes = scopeHooks.reduce((nextScopes2, {\n        useScope,\n        scopeName\n      }) => {\n        const scopeProps = useScope(overrideScopes);\n        const currentScope = scopeProps[`__scope${scopeName}`];\n        return {\n          ...nextScopes2,\n          ...currentScope\n        };\n      }, {});\n      return React.useMemo(() => ({\n        [`__scope${baseScope.scopeName}`]: nextScopes\n      }), [nextScopes]);\n    };\n  };\n  createScope.scopeName = baseScope.scopeName;\n  return createScope;\n}\nexport { createContext2 as createContext, createContextScope };", "map": {"version": 3, "names": ["React", "jsx", "createContext2", "rootComponentName", "defaultContext", "Context", "createContext", "Provider", "props", "children", "context", "value", "useMemo", "Object", "values", "displayName", "useContext2", "consumerName", "useContext", "Error", "createContextScope", "scopeName", "createContextScopeDeps", "defaultContexts", "createContext3", "BaseContext", "index", "length", "scope", "createScope", "scopeContexts", "map", "useScope", "contexts", "composeContextScopes", "scopes", "baseScope", "scopeHooks", "createScope2", "useComposedScopes", "overrideScopes", "nextScopes", "reduce", "nextScopes2", "scopeProps", "currentScope"], "sources": ["D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\node_modules\\@radix-ui\\react-context\\src\\create-context.tsx"], "sourcesContent": ["import * as React from 'react';\n\nfunction createContext<ContextValueType extends object | null>(\n  rootComponentName: string,\n  defaultContext?: ContextValueType\n) {\n  const Context = React.createContext<ContextValueType | undefined>(defaultContext);\n\n  const Provider: React.FC<ContextValueType & { children: React.ReactNode }> = (props) => {\n    const { children, ...context } = props;\n    // Only re-memoize when prop values change\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    const value = React.useMemo(() => context, Object.values(context)) as ContextValueType;\n    return <Context.Provider value={value}>{children}</Context.Provider>;\n  };\n\n  Provider.displayName = rootComponentName + 'Provider';\n\n  function useContext(consumerName: string) {\n    const context = React.useContext(Context);\n    if (context) return context;\n    if (defaultContext !== undefined) return defaultContext;\n    // if a defaultContext wasn't specified, it's a required context.\n    throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n  }\n\n  return [Provider, useContext] as const;\n}\n\n/* -------------------------------------------------------------------------------------------------\n * createContextScope\n * -----------------------------------------------------------------------------------------------*/\n\ntype Scope<C = any> = { [scopeName: string]: React.Context<C>[] } | undefined;\ntype ScopeHook = (scope: Scope) => { [__scopeProp: string]: Scope };\ninterface CreateScope {\n  scopeName: string;\n  (): ScopeHook;\n}\n\nfunction createContextScope(scopeName: string, createContextScopeDeps: CreateScope[] = []) {\n  let defaultContexts: any[] = [];\n\n  /* -----------------------------------------------------------------------------------------------\n   * createContext\n   * ---------------------------------------------------------------------------------------------*/\n\n  function createContext<ContextValueType extends object | null>(\n    rootComponentName: string,\n    defaultContext?: ContextValueType\n  ) {\n    const BaseContext = React.createContext<ContextValueType | undefined>(defaultContext);\n    const index = defaultContexts.length;\n    defaultContexts = [...defaultContexts, defaultContext];\n\n    const Provider: React.FC<\n      ContextValueType & { scope: Scope<ContextValueType>; children: React.ReactNode }\n    > = (props) => {\n      const { scope, children, ...context } = props;\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      // Only re-memoize when prop values change\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n      const value = React.useMemo(() => context, Object.values(context)) as ContextValueType;\n      return <Context.Provider value={value}>{children}</Context.Provider>;\n    };\n\n    Provider.displayName = rootComponentName + 'Provider';\n\n    function useContext(consumerName: string, scope: Scope<ContextValueType | undefined>) {\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const context = React.useContext(Context);\n      if (context) return context;\n      if (defaultContext !== undefined) return defaultContext;\n      // if a defaultContext wasn't specified, it's a required context.\n      throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n\n    return [Provider, useContext] as const;\n  }\n\n  /* -----------------------------------------------------------------------------------------------\n   * createScope\n   * ---------------------------------------------------------------------------------------------*/\n\n  const createScope: CreateScope = () => {\n    const scopeContexts = defaultContexts.map((defaultContext) => {\n      return React.createContext(defaultContext);\n    });\n    return function useScope(scope: Scope) {\n      const contexts = scope?.[scopeName] || scopeContexts;\n      return React.useMemo(\n        () => ({ [`__scope${scopeName}`]: { ...scope, [scopeName]: contexts } }),\n        [scope, contexts]\n      );\n    };\n  };\n\n  createScope.scopeName = scopeName;\n  return [createContext, composeContextScopes(createScope, ...createContextScopeDeps)] as const;\n}\n\n/* -------------------------------------------------------------------------------------------------\n * composeContextScopes\n * -----------------------------------------------------------------------------------------------*/\n\nfunction composeContextScopes(...scopes: CreateScope[]) {\n  const baseScope = scopes[0];\n  if (scopes.length === 1) return baseScope;\n\n  const createScope: CreateScope = () => {\n    const scopeHooks = scopes.map((createScope) => ({\n      useScope: createScope(),\n      scopeName: createScope.scopeName,\n    }));\n\n    return function useComposedScopes(overrideScopes) {\n      const nextScopes = scopeHooks.reduce((nextScopes, { useScope, scopeName }) => {\n        // We are calling a hook inside a callback which React warns against to avoid inconsistent\n        // renders, however, scoping doesn't have render side effects so we ignore the rule.\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        const scopeProps = useScope(overrideScopes);\n        const currentScope = scopeProps[`__scope${scopeName}`];\n        return { ...nextScopes, ...currentScope };\n      }, {});\n\n      return React.useMemo(() => ({ [`__scope${baseScope.scopeName}`]: nextScopes }), [nextScopes]);\n    };\n  };\n\n  createScope.scopeName = baseScope.scopeName;\n  return createScope;\n}\n\n/* -----------------------------------------------------------------------------------------------*/\n\nexport { createContext, createContextScope };\nexport type { CreateScope, Scope };\n"], "mappings": ";AAAA,YAAYA,KAAA,MAAW;AAaZ,SAAAC,GAAA;AAXX,SAASC,eACPC,iBAAA,EACAC,cAAA,EACA;EACA,MAAMC,OAAA,GAAgBL,KAAA,CAAAM,aAAA,CAA4CF,cAAc;EAEhF,MAAMG,QAAA,GAAwEC,KAAA,IAAU;IACtF,MAAM;MAAEC,QAAA;MAAU,GAAGC;IAAQ,IAAIF,KAAA;IAGjC,MAAMG,KAAA,GAAcX,KAAA,CAAAY,OAAA,CAAQ,MAAMF,OAAA,EAASG,MAAA,CAAOC,MAAA,CAAOJ,OAAO,CAAC;IACjE,OAAO,eAAAT,GAAA,CAACI,OAAA,CAAQE,QAAA,EAAR;MAAiBI,KAAA;MAAeF;IAAA,CAAS;EACnD;EAEAF,QAAA,CAASQ,WAAA,GAAcZ,iBAAA,GAAoB;EAE3C,SAASa,YAAWC,YAAA,EAAsB;IACxC,MAAMP,OAAA,GAAgBV,KAAA,CAAAkB,UAAA,CAAWb,OAAO;IACxC,IAAIK,OAAA,EAAS,OAAOA,OAAA;IACpB,IAAIN,cAAA,KAAmB,QAAW,OAAOA,cAAA;IAEzC,MAAM,IAAIe,KAAA,CAAM,KAAKF,YAAY,4BAA4Bd,iBAAiB,IAAI;EACpF;EAEA,OAAO,CAACI,QAAA,EAAUS,WAAU;AAC9B;AAaA,SAASI,mBAAmBC,SAAA,EAAmBC,sBAAA,GAAwC,EAAC,EAAG;EACzF,IAAIC,eAAA,GAAyB,EAAC;EAM9B,SAASC,eACPrB,iBAAA,EACAC,cAAA,EACA;IACA,MAAMqB,WAAA,GAAoBzB,KAAA,CAAAM,aAAA,CAA4CF,cAAc;IACpF,MAAMsB,KAAA,GAAQH,eAAA,CAAgBI,MAAA;IAC9BJ,eAAA,GAAkB,CAAC,GAAGA,eAAA,EAAiBnB,cAAc;IAErD,MAAMG,QAAA,GAEDC,KAAA,IAAU;MACb,MAAM;QAAEoB,KAAA;QAAOnB,QAAA;QAAU,GAAGC;MAAQ,IAAIF,KAAA;MACxC,MAAMH,OAAA,GAAUuB,KAAA,GAAQP,SAAS,IAAIK,KAAK,KAAKD,WAAA;MAG/C,MAAMd,KAAA,GAAcX,KAAA,CAAAY,OAAA,CAAQ,MAAMF,OAAA,EAASG,MAAA,CAAOC,MAAA,CAAOJ,OAAO,CAAC;MACjE,OAAO,eAAAT,GAAA,CAACI,OAAA,CAAQE,QAAA,EAAR;QAAiBI,KAAA;QAAeF;MAAA,CAAS;IACnD;IAEAF,QAAA,CAASQ,WAAA,GAAcZ,iBAAA,GAAoB;IAE3C,SAASa,YAAWC,YAAA,EAAsBW,KAAA,EAA4C;MACpF,MAAMvB,OAAA,GAAUuB,KAAA,GAAQP,SAAS,IAAIK,KAAK,KAAKD,WAAA;MAC/C,MAAMf,OAAA,GAAgBV,KAAA,CAAAkB,UAAA,CAAWb,OAAO;MACxC,IAAIK,OAAA,EAAS,OAAOA,OAAA;MACpB,IAAIN,cAAA,KAAmB,QAAW,OAAOA,cAAA;MAEzC,MAAM,IAAIe,KAAA,CAAM,KAAKF,YAAY,4BAA4Bd,iBAAiB,IAAI;IACpF;IAEA,OAAO,CAACI,QAAA,EAAUS,WAAU;EAC9B;EAMA,MAAMa,WAAA,GAA2BA,CAAA,KAAM;IACrC,MAAMC,aAAA,GAAgBP,eAAA,CAAgBQ,GAAA,CAAK3B,cAAA,IAAmB;MAC5D,OAAaJ,KAAA,CAAAM,aAAA,CAAcF,cAAc;IAC3C,CAAC;IACD,OAAO,SAAS4B,SAASJ,KAAA,EAAc;MACrC,MAAMK,QAAA,GAAWL,KAAA,GAAQP,SAAS,KAAKS,aAAA;MACvC,OAAa9B,KAAA,CAAAY,OAAA,CACX,OAAO;QAAE,CAAC,UAAUS,SAAS,EAAE,GAAG;UAAE,GAAGO,KAAA;UAAO,CAACP,SAAS,GAAGY;QAAS;MAAE,IACtE,CAACL,KAAA,EAAOK,QAAQ,CAClB;IACF;EACF;EAEAJ,WAAA,CAAYR,SAAA,GAAYA,SAAA;EACxB,OAAO,CAACG,cAAA,EAAeU,oBAAA,CAAqBL,WAAA,EAAa,GAAGP,sBAAsB,CAAC;AACrF;AAMA,SAASY,qBAAA,GAAwBC,MAAA,EAAuB;EACtD,MAAMC,SAAA,GAAYD,MAAA,CAAO,CAAC;EAC1B,IAAIA,MAAA,CAAOR,MAAA,KAAW,GAAG,OAAOS,SAAA;EAEhC,MAAMP,WAAA,GAA2BA,CAAA,KAAM;IACrC,MAAMQ,UAAA,GAAaF,MAAA,CAAOJ,GAAA,CAAKO,YAAA,KAAiB;MAC9CN,QAAA,EAAUM,YAAA,CAAY;MACtBjB,SAAA,EAAWiB,YAAA,CAAYjB;IACzB,EAAE;IAEF,OAAO,SAASkB,kBAAkBC,cAAA,EAAgB;MAChD,MAAMC,UAAA,GAAaJ,UAAA,CAAWK,MAAA,CAAO,CAACC,WAAA,EAAY;QAAEX,QAAA;QAAUX;MAAU,MAAM;QAI5E,MAAMuB,UAAA,GAAaZ,QAAA,CAASQ,cAAc;QAC1C,MAAMK,YAAA,GAAeD,UAAA,CAAW,UAAUvB,SAAS,EAAE;QACrD,OAAO;UAAE,GAAGsB,WAAA;UAAY,GAAGE;QAAa;MAC1C,GAAG,CAAC,CAAC;MAEL,OAAa7C,KAAA,CAAAY,OAAA,CAAQ,OAAO;QAAE,CAAC,UAAUwB,SAAA,CAAUf,SAAS,EAAE,GAAGoB;MAAW,IAAI,CAACA,UAAU,CAAC;IAC9F;EACF;EAEAZ,WAAA,CAAYR,SAAA,GAAYe,SAAA,CAAUf,SAAA;EAClC,OAAOQ,WAAA;AACT", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}