{"name": "@babel/plugin-transform-regenerator", "author": "The Babel Team (https://babel.dev/team)", "description": "Explode async and generator functions into a state machine.", "version": "7.27.5", "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-regenerator", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-regenerator"}, "main": "./lib/index.js", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "license": "MIT", "publishConfig": {"access": "public"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.27.4", "@babel/helper-check-duplicate-nodes": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1", "@babel/plugin-proposal-function-sent": "^7.27.1", "@babel/plugin-transform-arrow-functions": "^7.27.1", "@babel/plugin-transform-block-scoping": "^7.27.5", "@babel/plugin-transform-classes": "^7.27.1", "@babel/plugin-transform-for-of": "^7.27.1", "@babel/plugin-transform-modules-commonjs": "^7.27.1", "@babel/plugin-transform-parameters": "^7.27.1", "@babel/plugin-transform-runtime": "^7.27.4", "babel-plugin-polyfill-regenerator": "^0.6.1", "mocha": "^10.0.0", "recast": "^0.23.3", "uglify-js": "^3.14.0"}, "engines": {"node": ">=6.9.0"}, "type": "commonjs"}