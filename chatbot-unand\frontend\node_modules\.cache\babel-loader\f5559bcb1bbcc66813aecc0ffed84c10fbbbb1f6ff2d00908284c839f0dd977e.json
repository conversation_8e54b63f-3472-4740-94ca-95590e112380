{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12.1\",\n  cy: \"12.1\",\n  r: \"1\",\n  key: \"18d7e5\"\n}]];\nconst Dot = createLucideIcon(\"dot\", __iconNode);\nexport { __iconNode, Dot as default };", "map": {"version": 3, "names": ["__iconNode", "cx", "cy", "r", "key", "Dot", "createLucideIcon"], "sources": ["D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\node_modules\\lucide-react\\src\\icons\\dot.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['circle', { cx: '12.1', cy: '12.1', r: '1', key: '18d7e5' }]];\n\n/**\n * @component @name Dot\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMi4xIiBjeT0iMTIuMSIgcj0iMSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/dot\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Dot = createLucideIcon('dot', __iconNode);\n\nexport default Dot;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAAC,CAAC,UAAU;EAAEC,EAAI;EAAQC,EAAI;EAAQC,CAAG;EAAKC,GAAK;AAAA,CAAU,CAAC;AAa5F,MAAAC,GAAA,GAAMC,gBAAiB,QAAON,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}