{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website kp\\\\chatbot-unand\\\\frontend\\\\src\\\\App.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport ChatWindow from \"./ChatWindow\";\nimport ChatSidebar from \"./ChatSidebar\";\nimport TelegramButton from \"./TelegramButton\";\nimport ThemeToggle from \"./components/ThemeToggle\";\nimport Login from \"./components/Login\";\nimport UserProfile from \"./components/UserProfile\";\nimport { ThemeProvider } from \"./contexts/ThemeContext\";\nimport { AuthProvider, useAuth } from \"./contexts/AuthContext\";\nimport { getSessionMessages } from \"./api\";\nimport \"./index.css\"; // Pastikan Tailwind CSS diimpor\n\n// Main App Component with Authentication\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MainApp = () => {\n  _s();\n  const {\n    user,\n    loading\n  } = useAuth();\n  const [currentSessionId, setCurrentSessionId] = useState(null);\n  const [isSidebarOpen, setIsSidebarOpen] = useState(false);\n  const [messages, setMessages] = useState([{\n    id: 1,\n    text: \"Halo! Saya adalah Chatbot UNAND. Saya siap membantu Anda dengan pertanyaan seputar peraturan kampus dan pemerintah. Silakan ajukan pertanyaan Anda!\",\n    isBot: true,\n    timestamp: new Date(),\n    is_greeting: true\n  }]);\n  const handleSessionSelect = async sessionId => {\n    try {\n      setCurrentSessionId(sessionId);\n      const sessionMessages = await getSessionMessages(sessionId);\n\n      // Convert API messages to frontend format\n      const convertedMessages = sessionMessages.map(msg => ({\n        id: msg.id,\n        text: msg.content,\n        isBot: msg.message_type === \"bot\",\n        timestamp: new Date(msg.timestamp),\n        sources: msg.sources,\n        sources_count: msg.sources ? msg.sources.length : 0,\n        summary: msg.summary,\n        suggestions: msg.suggestions\n      }));\n      setMessages(convertedMessages);\n      // Close sidebar on mobile after selecting session\n      if (window.innerWidth < 1024) {\n        setIsSidebarOpen(false);\n      }\n    } catch (error) {\n      console.error(\"Error loading session messages:\", error);\n      alert(\"Gagal memuat riwayat percakapan\");\n    }\n  };\n  const handleNewChat = () => {\n    setCurrentSessionId(null);\n    setMessages([{\n      id: 1,\n      text: \"Halo! Saya adalah Chatbot UNAND. Saya siap membantu Anda dengan pertanyaan seputar peraturan kampus dan pemerintah. Silakan ajukan pertanyaan Anda!\",\n      isBot: true,\n      timestamp: new Date(),\n      is_greeting: true\n    }]);\n    // Close sidebar on mobile after new chat\n    if (window.innerWidth < 1024) {\n      setIsSidebarOpen(false);\n    }\n  };\n  const toggleSidebar = () => {\n    setIsSidebarOpen(!isSidebarOpen);\n  };\n\n  // Handle loading state\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-r from-green-50 to-yellow-50 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 dark:text-gray-400\",\n          children: \"Memuat...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Handle unauthenticated state\n  if (!user) {\n    return /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(ThemeProvider, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"h-screen bg-gradient-to-r from-green-50 to-yellow-50 dark:from-gray-900 dark:to-gray-800 flex overflow-hidden relative\",\n      children: [isSidebarOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden\",\n        onClick: () => setIsSidebarOpen(false)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `fixed lg:static lg:translate-x-0 transition-transform duration-300 ease-in-out z-50 w-80 ${isSidebarOpen ? \"translate-x-0\" : \"-translate-x-full\"}`,\n        children: /*#__PURE__*/_jsxDEV(ChatSidebar, {\n          currentSessionId: currentSessionId,\n          onSessionSelect: handleSessionSelect,\n          onNewChat: handleNewChat,\n          onClose: () => setIsSidebarOpen(false)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 flex flex-col min-w-0\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"border-b-2 border-green-600 dark:border-green-400 p-3 lg:p-6 shadow-lg flex-shrink-0 bg-transparent\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"lg:hidden grid grid-cols-3 items-center mb-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-start\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: toggleSidebar,\n                className: \"p-2 rounded-lg bg-green-600 dark:bg-green-500 text-white hover:bg-green-700 dark:hover:bg-green-600 transition-colors\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-6 h-6\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M4 6h16M4 12h16M4 18h16\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 137,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 131,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-center gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: \"/lambang-unand.jpg\",\n                alt: \"Logo UNAND\",\n                className: \"w-8 h-8 object-contain rounded border border-green-600 dark:border-green-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-lg font-bold text-green-700 dark:text-green-300\",\n                children: \"TANYO UNAND\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-end items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(UserProfile, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(ThemeToggle, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hidden lg:grid lg:grid-cols-3 items-center h-19 w-full\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-shrink-0\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"/lambang-unand.jpg\",\n                  alt: \"Logo Universitas Andalas\",\n                  className: \"w-14 h-14 object-contain rounded-lg shadow-md border-2 border-green-600 dark:border-green-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 171,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col justify-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-xl font-bold text-green-700 dark:text-green-300 leading-tight\",\n                  children: \"TANYO UNAND\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 178,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-green-600 dark:text-green-400 text-sm font-medium leading-tight\",\n                  children: \"Tampaik batanyo Seputar Universitas Andalas\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 181,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-yellow-700 dark:text-yellow-400 text-xs font-bold tracking-wider uppercase mt-1\",\n                  children: \"\\\"UNTUK KEDJAJAAN BANGSA\\\"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 184,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-center\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-end space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(UserProfile, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(ThemeToggle, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 min-h-0\",\n          children: /*#__PURE__*/_jsxDEV(ChatWindow, {\n            messages: messages,\n            setMessages: setMessages,\n            currentSessionId: currentSessionId,\n            setCurrentSessionId: setCurrentSessionId\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TelegramButton, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 95,\n    columnNumber: 5\n  }, this);\n};\n\n// App wrapper with providers\n_s(MainApp, \"vSfhjJzOjjx4yGbIHgsJ5L4XzCM=\", false, function () {\n  return [useAuth];\n});\n_c = MainApp;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(AuthProvider, {\n    children: /*#__PURE__*/_jsxDEV(MainApp, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 223,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 222,\n    columnNumber: 5\n  }, this);\n}\n_c2 = App;\nexport default App;\nvar _c, _c2;\n$RefreshReg$(_c, \"MainApp\");\n$RefreshReg$(_c2, \"App\");", "map": {"version": 3, "names": ["React", "useState", "ChatWindow", "ChatSidebar", "TelegramButton", "ThemeToggle", "<PERSON><PERSON>", "UserProfile", "ThemeProvider", "<PERSON>th<PERSON><PERSON><PERSON>", "useAuth", "getSessionMessages", "jsxDEV", "_jsxDEV", "MainApp", "_s", "user", "loading", "currentSessionId", "setCurrentSessionId", "isSidebarOpen", "setIsSidebarOpen", "messages", "setMessages", "id", "text", "isBot", "timestamp", "Date", "is_greeting", "handleSessionSelect", "sessionId", "sessionMessages", "convertedMessages", "map", "msg", "content", "message_type", "sources", "sources_count", "length", "summary", "suggestions", "window", "innerWidth", "error", "console", "alert", "handleNewChat", "toggleSidebar", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onSessionSelect", "onNewChat", "onClose", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "src", "alt", "_c", "App", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/website kp/chatbot-unand/frontend/src/App.jsx"], "sourcesContent": ["import React, { useState } from \"react\";\nimport ChatWindow from \"./ChatWindow\";\nimport ChatSidebar from \"./ChatSidebar\";\nimport TelegramButton from \"./TelegramButton\";\nimport ThemeToggle from \"./components/ThemeToggle\";\nimport Login from \"./components/Login\";\nimport UserProfile from \"./components/UserProfile\";\nimport { ThemeProvider } from \"./contexts/ThemeContext\";\nimport { AuthProvider, useAuth } from \"./contexts/AuthContext\";\nimport { getSessionMessages } from \"./api\";\nimport \"./index.css\"; // Pastikan Tailwind CSS diimpor\n\n// Main App Component with Authentication\nconst MainApp = () => {\n  const { user, loading } = useAuth();\n  const [currentSessionId, setCurrentSessionId] = useState(null);\n  const [isSidebarOpen, setIsSidebarOpen] = useState(false);\n  const [messages, setMessages] = useState([\n    {\n      id: 1,\n      text: \"<PERSON><PERSON>! <PERSON>a adalah Chatbot UNAND. Saya siap membantu Anda dengan pertanyaan seputar peraturan kampus dan pemerintah. Silakan ajukan pertanyaan Anda!\",\n      isBot: true,\n      timestamp: new Date(),\n      is_greeting: true,\n    },\n  ]);\n\n  const handleSessionSelect = async (sessionId) => {\n    try {\n      setCurrentSessionId(sessionId);\n      const sessionMessages = await getSessionMessages(sessionId);\n\n      // Convert API messages to frontend format\n      const convertedMessages = sessionMessages.map((msg) => ({\n        id: msg.id,\n        text: msg.content,\n        isBot: msg.message_type === \"bot\",\n        timestamp: new Date(msg.timestamp),\n        sources: msg.sources,\n        sources_count: msg.sources ? msg.sources.length : 0,\n        summary: msg.summary,\n        suggestions: msg.suggestions,\n      }));\n\n      setMessages(convertedMessages);\n      // Close sidebar on mobile after selecting session\n      if (window.innerWidth < 1024) {\n        setIsSidebarOpen(false);\n      }\n    } catch (error) {\n      console.error(\"Error loading session messages:\", error);\n      alert(\"Gagal memuat riwayat percakapan\");\n    }\n  };\n\n  const handleNewChat = () => {\n    setCurrentSessionId(null);\n    setMessages([\n      {\n        id: 1,\n        text: \"Halo! Saya adalah Chatbot UNAND. Saya siap membantu Anda dengan pertanyaan seputar peraturan kampus dan pemerintah. Silakan ajukan pertanyaan Anda!\",\n        isBot: true,\n        timestamp: new Date(),\n        is_greeting: true,\n      },\n    ]);\n    // Close sidebar on mobile after new chat\n    if (window.innerWidth < 1024) {\n      setIsSidebarOpen(false);\n    }\n  };\n\n  const toggleSidebar = () => {\n    setIsSidebarOpen(!isSidebarOpen);\n  };\n\n  // Handle loading state\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-r from-green-50 to-yellow-50 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600 dark:text-gray-400\">Memuat...</p>\n        </div>\n      </div>\n    );\n  }\n\n  // Handle unauthenticated state\n  if (!user) {\n    return <Login />;\n  }\n\n  return (\n    <ThemeProvider>\n      <div className=\"h-screen bg-gradient-to-r from-green-50 to-yellow-50 dark:from-gray-900 dark:to-gray-800 flex overflow-hidden relative\">\n        {/* Mobile Overlay */}\n        {isSidebarOpen && (\n          <div\n            className=\"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden\"\n            onClick={() => setIsSidebarOpen(false)}\n          />\n        )}\n\n        {/* Sidebar */}\n        <div\n          className={`fixed lg:static lg:translate-x-0 transition-transform duration-300 ease-in-out z-50 w-80 ${\n            isSidebarOpen ? \"translate-x-0\" : \"-translate-x-full\"\n          }`}\n        >\n          <ChatSidebar\n            currentSessionId={currentSessionId}\n            onSessionSelect={handleSessionSelect}\n            onNewChat={handleNewChat}\n            onClose={() => setIsSidebarOpen(false)}\n          />\n        </div>\n\n        {/* Main Content Area */}\n        <div className=\"flex-1 flex flex-col min-w-0\">\n          {/* Fixed Header */}\n          <div className=\"border-b-2 border-green-600 dark:border-green-400 p-3 lg:p-6 shadow-lg flex-shrink-0 bg-transparent\">\n            {/* Mobile Header */}\n            <div className=\"lg:hidden grid grid-cols-3 items-center mb-2\">\n              {/* Left: Menu Button */}\n              <div className=\"flex justify-start\">\n                <button\n                  onClick={toggleSidebar}\n                  className=\"p-2 rounded-lg bg-green-600 dark:bg-green-500 text-white hover:bg-green-700 dark:hover:bg-green-600 transition-colors\"\n                >\n                  <svg\n                    className=\"w-6 h-6\"\n                    fill=\"none\"\n                    stroke=\"currentColor\"\n                    viewBox=\"0 0 24 24\"\n                  >\n                    <path\n                      strokeLinecap=\"round\"\n                      strokeLinejoin=\"round\"\n                      strokeWidth={2}\n                      d=\"M4 6h16M4 12h16M4 18h16\"\n                    />\n                  </svg>\n                </button>\n              </div>\n\n              {/* Center: Logo and Title */}\n              <div className=\"flex items-center justify-center gap-2\">\n                <img\n                  src=\"/lambang-unand.jpg\"\n                  alt=\"Logo UNAND\"\n                  className=\"w-8 h-8 object-contain rounded border border-green-600 dark:border-green-400\"\n                />\n                <h1 className=\"text-lg font-bold text-green-700 dark:text-green-300\">\n                  TANYO UNAND\n                </h1>\n              </div>\n\n              {/* Right: User Profile and Theme Toggle */}\n              <div className=\"flex justify-end items-center space-x-2\">\n                <UserProfile />\n                <ThemeToggle />\n              </div>\n            </div>\n\n            {/* Desktop Header */}\n            <div className=\"hidden lg:grid lg:grid-cols-3 items-center h-19 w-full\">\n              {/* Left Section: Logo and Title */}\n              <div className=\"flex items-center gap-4\">\n                <div className=\"flex-shrink-0\">\n                  <img\n                    src=\"/lambang-unand.jpg\"\n                    alt=\"Logo Universitas Andalas\"\n                    className=\"w-14 h-14 object-contain rounded-lg shadow-md border-2 border-green-600 dark:border-green-400\"\n                  />\n                </div>\n                <div className=\"flex flex-col justify-center\">\n                  <h1 className=\"text-xl font-bold text-green-700 dark:text-green-300 leading-tight\">\n                    TANYO UNAND\n                  </h1>\n                  <p className=\"text-green-600 dark:text-green-400 text-sm font-medium leading-tight\">\n                    Tampaik batanyo Seputar Universitas Andalas\n                  </p>\n                  <p className=\"text-yellow-700 dark:text-yellow-400 text-xs font-bold tracking-wider uppercase mt-1\">\n                    \"UNTUK KEDJAJAAN BANGSA\"\n                  </p>\n                </div>\n              </div>\n\n              {/* Center Section: Empty for balance */}\n              <div className=\"flex items-center justify-center\"></div>\n\n              {/* Right Section: User Profile and Theme Toggle */}\n              <div className=\"flex items-center justify-end space-x-4\">\n                <UserProfile />\n                <ThemeToggle />\n              </div>\n            </div>\n          </div>\n\n          {/* Chat Window */}\n          <div className=\"flex-1 min-h-0\">\n            <ChatWindow\n              messages={messages}\n              setMessages={setMessages}\n              currentSessionId={currentSessionId}\n              setCurrentSessionId={setCurrentSessionId}\n            />\n          </div>\n        </div>\n\n        {/* Telegram Button */}\n        <TelegramButton />\n      </div>\n    </ThemeProvider>\n  );\n};\n\n// App wrapper with providers\nfunction App() {\n  return (\n    <AuthProvider>\n      <MainApp />\n    </AuthProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,KAAK,MAAM,oBAAoB;AACtC,OAAOC,WAAW,MAAM,0BAA0B;AAClD,SAASC,aAAa,QAAQ,yBAAyB;AACvD,SAASC,YAAY,EAAEC,OAAO,QAAQ,wBAAwB;AAC9D,SAASC,kBAAkB,QAAQ,OAAO;AAC1C,OAAO,aAAa,CAAC,CAAC;;AAEtB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpB,MAAM;IAAEC,IAAI;IAAEC;EAAQ,CAAC,GAAGP,OAAO,CAAC,CAAC;EACnC,MAAM,CAACQ,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACmB,aAAa,EAAEC,gBAAgB,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACqB,QAAQ,EAAEC,WAAW,CAAC,GAAGtB,QAAQ,CAAC,CACvC;IACEuB,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,qJAAqJ;IAC3JC,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;IACrBC,WAAW,EAAE;EACf,CAAC,CACF,CAAC;EAEF,MAAMC,mBAAmB,GAAG,MAAOC,SAAS,IAAK;IAC/C,IAAI;MACFZ,mBAAmB,CAACY,SAAS,CAAC;MAC9B,MAAMC,eAAe,GAAG,MAAMrB,kBAAkB,CAACoB,SAAS,CAAC;;MAE3D;MACA,MAAME,iBAAiB,GAAGD,eAAe,CAACE,GAAG,CAAEC,GAAG,KAAM;QACtDX,EAAE,EAAEW,GAAG,CAACX,EAAE;QACVC,IAAI,EAAEU,GAAG,CAACC,OAAO;QACjBV,KAAK,EAAES,GAAG,CAACE,YAAY,KAAK,KAAK;QACjCV,SAAS,EAAE,IAAIC,IAAI,CAACO,GAAG,CAACR,SAAS,CAAC;QAClCW,OAAO,EAAEH,GAAG,CAACG,OAAO;QACpBC,aAAa,EAAEJ,GAAG,CAACG,OAAO,GAAGH,GAAG,CAACG,OAAO,CAACE,MAAM,GAAG,CAAC;QACnDC,OAAO,EAAEN,GAAG,CAACM,OAAO;QACpBC,WAAW,EAAEP,GAAG,CAACO;MACnB,CAAC,CAAC,CAAC;MAEHnB,WAAW,CAACU,iBAAiB,CAAC;MAC9B;MACA,IAAIU,MAAM,CAACC,UAAU,GAAG,IAAI,EAAE;QAC5BvB,gBAAgB,CAAC,KAAK,CAAC;MACzB;IACF,CAAC,CAAC,OAAOwB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvDE,KAAK,CAAC,iCAAiC,CAAC;IAC1C;EACF,CAAC;EAED,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1B7B,mBAAmB,CAAC,IAAI,CAAC;IACzBI,WAAW,CAAC,CACV;MACEC,EAAE,EAAE,CAAC;MACLC,IAAI,EAAE,qJAAqJ;MAC3JC,KAAK,EAAE,IAAI;MACXC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;MACrBC,WAAW,EAAE;IACf,CAAC,CACF,CAAC;IACF;IACA,IAAIc,MAAM,CAACC,UAAU,GAAG,IAAI,EAAE;MAC5BvB,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;EAED,MAAM4B,aAAa,GAAGA,CAAA,KAAM;IAC1B5B,gBAAgB,CAAC,CAACD,aAAa,CAAC;EAClC,CAAC;;EAED;EACA,IAAIH,OAAO,EAAE;IACX,oBACEJ,OAAA;MAAKqC,SAAS,EAAC,+HAA+H;MAAAC,QAAA,eAC5ItC,OAAA;QAAKqC,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BtC,OAAA;UAAKqC,SAAS,EAAC;QAA8E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACpG1C,OAAA;UAAGqC,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAI,CAACvC,IAAI,EAAE;IACT,oBAAOH,OAAA,CAACP,KAAK;MAAA8C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAClB;EAEA,oBACE1C,OAAA,CAACL,aAAa;IAAA2C,QAAA,eACZtC,OAAA;MAAKqC,SAAS,EAAC,wHAAwH;MAAAC,QAAA,GAEpI/B,aAAa,iBACZP,OAAA;QACEqC,SAAS,EAAC,qDAAqD;QAC/DM,OAAO,EAAEA,CAAA,KAAMnC,gBAAgB,CAAC,KAAK;MAAE;QAAA+B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CACF,eAGD1C,OAAA;QACEqC,SAAS,EAAE,4FACT9B,aAAa,GAAG,eAAe,GAAG,mBAAmB,EACpD;QAAA+B,QAAA,eAEHtC,OAAA,CAACV,WAAW;UACVe,gBAAgB,EAAEA,gBAAiB;UACnCuC,eAAe,EAAE3B,mBAAoB;UACrC4B,SAAS,EAAEV,aAAc;UACzBW,OAAO,EAAEA,CAAA,KAAMtC,gBAAgB,CAAC,KAAK;QAAE;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGN1C,OAAA;QAAKqC,SAAS,EAAC,8BAA8B;QAAAC,QAAA,gBAE3CtC,OAAA;UAAKqC,SAAS,EAAC,qGAAqG;UAAAC,QAAA,gBAElHtC,OAAA;YAAKqC,SAAS,EAAC,8CAA8C;YAAAC,QAAA,gBAE3DtC,OAAA;cAAKqC,SAAS,EAAC,oBAAoB;cAAAC,QAAA,eACjCtC,OAAA;gBACE2C,OAAO,EAAEP,aAAc;gBACvBC,SAAS,EAAC,uHAAuH;gBAAAC,QAAA,eAEjItC,OAAA;kBACEqC,SAAS,EAAC,SAAS;kBACnBU,IAAI,EAAC,MAAM;kBACXC,MAAM,EAAC,cAAc;kBACrBC,OAAO,EAAC,WAAW;kBAAAX,QAAA,eAEnBtC,OAAA;oBACEkD,aAAa,EAAC,OAAO;oBACrBC,cAAc,EAAC,OAAO;oBACtBC,WAAW,EAAE,CAAE;oBACfC,CAAC,EAAC;kBAAyB;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGN1C,OAAA;cAAKqC,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrDtC,OAAA;gBACEsD,GAAG,EAAC,oBAAoB;gBACxBC,GAAG,EAAC,YAAY;gBAChBlB,SAAS,EAAC;cAA8E;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzF,CAAC,eACF1C,OAAA;gBAAIqC,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,EAAC;cAErE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAGN1C,OAAA;cAAKqC,SAAS,EAAC,yCAAyC;cAAAC,QAAA,gBACtDtC,OAAA,CAACN,WAAW;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACf1C,OAAA,CAACR,WAAW;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN1C,OAAA;YAAKqC,SAAS,EAAC,wDAAwD;YAAAC,QAAA,gBAErEtC,OAAA;cAAKqC,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACtCtC,OAAA;gBAAKqC,SAAS,EAAC,eAAe;gBAAAC,QAAA,eAC5BtC,OAAA;kBACEsD,GAAG,EAAC,oBAAoB;kBACxBC,GAAG,EAAC,0BAA0B;kBAC9BlB,SAAS,EAAC;gBAA+F;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1G;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN1C,OAAA;gBAAKqC,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,gBAC3CtC,OAAA;kBAAIqC,SAAS,EAAC,oEAAoE;kBAAAC,QAAA,EAAC;gBAEnF;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL1C,OAAA;kBAAGqC,SAAS,EAAC,sEAAsE;kBAAAC,QAAA,EAAC;gBAEpF;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJ1C,OAAA;kBAAGqC,SAAS,EAAC,sFAAsF;kBAAAC,QAAA,EAAC;gBAEpG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN1C,OAAA;cAAKqC,SAAS,EAAC;YAAkC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAGxD1C,OAAA;cAAKqC,SAAS,EAAC,yCAAyC;cAAAC,QAAA,gBACtDtC,OAAA,CAACN,WAAW;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACf1C,OAAA,CAACR,WAAW;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN1C,OAAA;UAAKqC,SAAS,EAAC,gBAAgB;UAAAC,QAAA,eAC7BtC,OAAA,CAACX,UAAU;YACToB,QAAQ,EAAEA,QAAS;YACnBC,WAAW,EAAEA,WAAY;YACzBL,gBAAgB,EAAEA,gBAAiB;YACnCC,mBAAmB,EAAEA;UAAoB;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN1C,OAAA,CAACT,cAAc;QAAAgD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB,CAAC;;AAED;AAAAxC,EAAA,CA7MMD,OAAO;EAAA,QACeJ,OAAO;AAAA;AAAA2D,EAAA,GAD7BvD,OAAO;AA8Mb,SAASwD,GAAGA,CAAA,EAAG;EACb,oBACEzD,OAAA,CAACJ,YAAY;IAAA0C,QAAA,eACXtC,OAAA,CAACC,OAAO;MAAAsC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEnB;AAACgB,GAAA,GANQD,GAAG;AAQZ,eAAeA,GAAG;AAAC,IAAAD,EAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAH,EAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}