{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M4 13c3.5-2 8-2 10 2a5.5 5.5 0 0 1 8 5\",\n  key: \"1cscit\"\n}], [\"path\", {\n  d: \"M5.15 17.89c5.52-1.52 8.65-6.89 7-12C11.55 4 11.5 2 13 2c3.22 0 5 5.5 5 8 0 6.5-4.2 12-10.49 12C5.11 22 2 22 2 20c0-1.5 1.14-1.55 3.15-2.11Z\",\n  key: \"1y1nbv\"\n}]];\nconst Banana = createLucideIcon(\"banana\", __iconNode);\nexport { __iconNode, Banana as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "Banana", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\node_modules\\lucide-react\\src\\icons\\banana.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M4 13c3.5-2 8-2 10 2a5.5 5.5 0 0 1 8 5', key: '1cscit' }],\n  [\n    'path',\n    {\n      d: 'M5.15 17.89c5.52-1.52 8.65-6.89 7-12C11.55 4 11.5 2 13 2c3.22 0 5 5.5 5 8 0 6.5-4.2 12-10.49 12C5.11 22 2 22 2 20c0-1.5 1.14-1.55 3.15-2.11Z',\n      key: '1y1nbv',\n    },\n  ],\n];\n\n/**\n * @component @name Banana\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNCAxM2MzLjUtMiA4LTIgMTAgMmE1LjUgNS41IDAgMCAxIDggNSIgLz4KICA8cGF0aCBkPSJNNS4xNSAxNy44OWM1LjUyLTEuNTIgOC42NS02Ljg5IDctMTJDMTEuNTUgNCAxMS41IDIgMTMgMmMzLjIyIDAgNSA1LjUgNSA4IDAgNi41LTQuMiAxMi0xMC40OSAxMkM1LjExIDIyIDIgMjIgMiAyMGMwLTEuNSAxLjE0LTEuNTUgMy4xNS0yLjExWiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/banana\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Banana = createLucideIcon('banana', __iconNode);\n\nexport default Banana;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,wCAA0C;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvE,CACE,QACA;EACED,CAAG;EACHC,GAAK;AAAA,EACP,CAEJ;AAaM,MAAAC,MAAA,GAASC,gBAAiB,WAAUJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}