{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website kp\\\\chatbot-unand\\\\frontend\\\\src\\\\Message.js\";\nimport React from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Message = ({\n  text,\n  sender\n}) => {\n  const messageClass = sender === \"user\" ? \"bg-blue-500 text-white self-end\" : \"bg-gray-200 text-gray-800 self-start\";\n  const containerClass = sender === \"user\" ? \"justify-end\" : \"justify-start\";\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `flex ${containerClass} mb-2`,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `rounded-lg py-2 px-4 max-w-[70%] shadow ${messageClass}`,\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 5\n  }, this);\n};\n_c = Message;\nexport default Message;\nvar _c;\n$RefreshReg$(_c, \"Message\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Message", "text", "sender", "messageClass", "containerClass", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/website kp/chatbot-unand/frontend/src/Message.js"], "sourcesContent": ["import React from \"react\";\n\nconst Message = ({ text, sender }) => {\n  const messageClass =\n    sender === \"user\"\n      ? \"bg-blue-500 text-white self-end\"\n      : \"bg-gray-200 text-gray-800 self-start\";\n  const containerClass = sender === \"user\" ? \"justify-end\" : \"justify-start\";\n\n  return (\n    <div className={`flex ${containerClass} mb-2`}>\n      <div\n        className={`rounded-lg py-2 px-4 max-w-[70%] shadow ${messageClass}`}\n      >\n        <p>{text}</p>\n      </div>\n    </div>\n  );\n};\n\nexport default Message;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,OAAO,GAAGA,CAAC;EAAEC,IAAI;EAAEC;AAAO,CAAC,KAAK;EACpC,MAAMC,YAAY,GAChBD,MAAM,KAAK,MAAM,GACb,iCAAiC,GACjC,sCAAsC;EAC5C,MAAME,cAAc,GAAGF,MAAM,KAAK,MAAM,GAAG,aAAa,GAAG,eAAe;EAE1E,oBACEH,OAAA;IAAKM,SAAS,EAAE,QAAQD,cAAc,OAAQ;IAAAE,QAAA,eAC5CP,OAAA;MACEM,SAAS,EAAE,2CAA2CF,YAAY,EAAG;MAAAG,QAAA,eAErEP,OAAA;QAAAO,QAAA,EAAIL;MAAI;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACC,EAAA,GAhBIX,OAAO;AAkBb,eAAeA,OAAO;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}