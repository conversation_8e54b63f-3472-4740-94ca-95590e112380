{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 18a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1H5a3 3 0 0 1-3-3 1 1 0 0 1 1-1z\",\n  key: \"ioqxb1\"\n}], [\"path\", {\n  d: \"M13 10H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a1 1 0 0 1 1 1v6a1 1 0 0 1-1 1l-.81 3.242a1 1 0 0 1-.97.758H8\",\n  key: \"1rs59n\"\n}], [\"path\", {\n  d: \"M14 4h3a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1h-3\",\n  key: \"105ega\"\n}], [\"path\", {\n  d: \"M18 6h4\",\n  key: \"66u95g\"\n}], [\"path\", {\n  d: \"m5 10-2 8\",\n  key: \"xt2lic\"\n}], [\"path\", {\n  d: \"m7 18 2-8\",\n  key: \"1bzku2\"\n}]];\nconst Drill = createLucideIcon(\"drill\", __iconNode);\nexport { __iconNode, Drill as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "Drill", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\node_modules\\lucide-react\\src\\icons\\drill.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    { d: 'M10 18a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1H5a3 3 0 0 1-3-3 1 1 0 0 1 1-1z', key: 'ioqxb1' },\n  ],\n  [\n    'path',\n    {\n      d: 'M13 10H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a1 1 0 0 1 1 1v6a1 1 0 0 1-1 1l-.81 3.242a1 1 0 0 1-.97.758H8',\n      key: '1rs59n',\n    },\n  ],\n  ['path', { d: 'M14 4h3a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1h-3', key: '105ega' }],\n  ['path', { d: 'M18 6h4', key: '66u95g' }],\n  ['path', { d: 'm5 10-2 8', key: 'xt2lic' }],\n  ['path', { d: 'm7 18 2-8', key: '1bzku2' }],\n];\n\n/**\n * @component @name Drill\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAgMThhMSAxIDAgMCAxIDEgMXYyYTEgMSAwIDAgMS0xIDFINWEzIDMgMCAwIDEtMy0zIDEgMSAwIDAgMSAxLTF6IiAvPgogIDxwYXRoIGQ9Ik0xMyAxMEg0YTIgMiAwIDAgMS0yLTJWNGEyIDIgMCAwIDEgMi0yaDlhMSAxIDAgMCAxIDEgMXY2YTEgMSAwIDAgMS0xIDFsLS44MSAzLjI0MmExIDEgMCAwIDEtLjk3Ljc1OEg4IiAvPgogIDxwYXRoIGQ9Ik0xNCA0aDNhMSAxIDAgMCAxIDEgMXYyYTEgMSAwIDAgMS0xIDFoLTMiIC8+CiAgPHBhdGggZD0iTTE4IDZoNCIgLz4KICA8cGF0aCBkPSJtNSAxMC0yIDgiIC8+CiAgPHBhdGggZD0ibTcgMTggMi04IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/drill\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Drill = createLucideIcon('drill', __iconNode);\n\nexport default Drill;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CACE,QACA;EAAEC,CAAA,EAAG,qEAAuE;EAAAC,GAAA,EAAK;AAAS,EAC5F,EACA,CACE,QACA;EACED,CAAG;EACHC,GAAK;AAAA,EAET,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,0CAA4C;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzE,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAU,GAC5C;AAaM,MAAAC,KAAA,GAAQC,gBAAiB,UAASJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}