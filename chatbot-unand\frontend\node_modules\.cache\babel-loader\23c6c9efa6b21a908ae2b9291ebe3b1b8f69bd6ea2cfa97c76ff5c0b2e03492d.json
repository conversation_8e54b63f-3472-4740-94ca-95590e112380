{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website kp\\\\chatbot-unand\\\\frontend\\\\src\\\\ChatSidebar.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { getSessions, deleteSession, updateSession } from \"./api\";\nimport { useAuth } from \"./contexts/AuthContext\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ChatSidebar = ({\n  currentSessionId,\n  onSessionSelect,\n  onNewChat,\n  onClose\n}) => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [sessions, setSessions] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [editingSession, setEditingSession] = useState(null);\n  const [editTitle, setEditTitle] = useState(\"\");\n\n  // Load sessions when component mounts or user changes\n  useEffect(() => {\n    console.log(\"ChatSidebar: useEffect triggered, user:\", (user === null || user === void 0 ? void 0 : user.email) || \"guest\");\n    loadSessions();\n  }, [user]); // Only depend on user, not empty array\n\n  const loadSessions = async () => {\n    try {\n      setLoading(true);\n      console.log(\"ChatSidebar: Loading sessions for user:\", (user === null || user === void 0 ? void 0 : user.email) || \"guest\");\n      const sessionsData = await getSessions();\n      console.log(\"ChatSidebar: Loaded sessions:\", sessionsData.length, \"sessions\");\n      console.log(\"ChatSidebar: Sessions data:\", sessionsData);\n      setSessions(sessionsData);\n\n      // Debug: Log state after setting\n      setTimeout(() => {\n        console.log(\"ChatSidebar: State after setSessions:\", sessionsData.length);\n      }, 100);\n    } catch (error) {\n      console.error(\"Error loading sessions:\", error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleDeleteSession = async (sessionId, e) => {\n    e.stopPropagation();\n    if (window.confirm(\"Apakah Anda yakin ingin menghapus percakapan ini?\")) {\n      try {\n        await deleteSession(sessionId);\n        setSessions(sessions.filter(s => s.session_id !== sessionId));\n        if (currentSessionId === sessionId) {\n          onNewChat();\n        }\n      } catch (error) {\n        console.error(\"Error deleting session:\", error);\n        alert(\"Gagal menghapus percakapan\");\n      }\n    }\n  };\n  const handleEditSession = (session, e) => {\n    e.stopPropagation();\n    setEditingSession(session.session_id);\n    setEditTitle(session.title);\n  };\n  const handleSaveEdit = async sessionId => {\n    try {\n      await updateSession(sessionId, editTitle);\n      setSessions(sessions.map(s => s.session_id === sessionId ? {\n        ...s,\n        title: editTitle\n      } : s));\n      setEditingSession(null);\n    } catch (error) {\n      console.error(\"Error updating session:\", error);\n      alert(\"Gagal mengubah judul percakapan\");\n    }\n  };\n  const handleCancelEdit = () => {\n    setEditingSession(null);\n    setEditTitle(\"\");\n  };\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffTime = Math.abs(now - date);\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    if (diffDays === 1) {\n      return \"Hari ini\";\n    } else if (diffDays === 2) {\n      return \"Kemarin\";\n    } else if (diffDays <= 7) {\n      return `${diffDays - 1} hari lalu`;\n    } else {\n      return date.toLocaleDateString(\"id-ID\", {\n        day: \"numeric\",\n        month: \"short\",\n        year: \"numeric\"\n      });\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"w-80 bg-gradient-to-r from-green-50 to-yellow-50 dark:from-gray-800 dark:to-gray-700 lg:bg-transparent border-r-2 border-green-600 dark:border-green-400 flex flex-col h-screen\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"border-b-2 border-green-600 dark:border-green-400 p-3 lg:p-6 flex-shrink-0 bg-transparent\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"lg:hidden flex items-center gap-5 mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onClose,\n          className: \"p-1 rounded-lg bg-green-600 dark:bg-green-500 text-white hover:bg-green-700 dark:hover:bg-green-600 transition-colors gap-6\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-5 h-5\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M6 18L18 6M6 6l12 12\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-8 h-8 bg-gradient-to-br from-green-600 to-yellow-600 rounded-full flex items-center justify-center \",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-4 h-4 text-white\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-lg font-semibold text-green-800 dark:text-green-200\",\n            children: \"Riwayat Chat\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this), user && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-green-600 dark:text-green-400 mt-1\",\n            children: user.name || user.email\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hidden lg:flex lg:items-center h-5 w-full \",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-12 h-12 bg-gradient-to-br from-green-600 to-yellow-600 rounded-full flex items-center justify-center shadow-md\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-6 h-6 text-white\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-bold text-green-700 dark:text-green-300\",\n              children: \"Riwayat Chat\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 15\n            }, this), user && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-green-600 dark:text-green-400 mt-1\",\n              children: user.name || user.email\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-3 lg:mt-4 \",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onNewChat,\n          className: \"w-full bg-gradient-to-r from-green-600 to-green-700 dark:from-green-500 dark:to-green-600 text-white px-4 py-2 rounded-lg hover:from-green-700 hover:to-green-800 dark:hover:from-green-600 dark:hover:to-green-700 transition-all duration-200 shadow-md hover:shadow-lg font-medium flex items-center justify-center gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-5 h-5\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M12 4v16m8-8H4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 13\n          }, this), \"Chat Baru\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 overflow-y-auto\",\n      children: [console.log(\"ChatSidebar RENDER: sessions.length =\", sessions.length), console.log(\"ChatSidebar RENDER: loading =\", loading), console.log(\"ChatSidebar RENDER: user =\", user === null || user === void 0 ? void 0 : user.email), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 text-center text-green-600 dark:text-green-400\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 dark:border-green-400 mx-auto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-2\",\n          children: \"Memuat riwayat...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 11\n      }, this) : sessions.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 text-center text-green-600 dark:text-green-400\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Belum ada percakapan\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm\",\n          children: \"Mulai chat baru untuk melihat riwayat\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-2\",\n        children: sessions.map(session => /*#__PURE__*/_jsxDEV(\"div\", {\n          onClick: () => onSessionSelect(session.session_id),\n          className: `p-3 rounded-lg cursor-pointer transition-colors mb-2 group ${currentSessionId === session.session_id ? \"bg-gradient-to-r from-green-100 to-yellow-100 dark:from-green-800 dark:to-yellow-800 border-2 border-green-500 dark:border-green-400 shadow-md\" : \"hover:bg-gradient-to-r hover:from-green-50 hover:to-yellow-50 dark:hover:from-gray-700 dark:hover:to-gray-600 border border-transparent hover:border-green-300 dark:hover:border-green-500\"}`,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1 min-w-0\",\n              children: editingSession === session.session_id ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-2\",\n                onClick: e => e.stopPropagation(),\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: editTitle,\n                  onChange: e => setEditTitle(e.target.value),\n                  className: \"flex-1 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                  onKeyPress: e => {\n                    if (e.key === \"Enter\") {\n                      handleSaveEdit(session.session_id);\n                    } else if (e.key === \"Escape\") {\n                      handleCancelEdit();\n                    }\n                  },\n                  autoFocus: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 263,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleSaveEdit(session.session_id),\n                  className: \"text-green-600 hover:text-green-700\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-4 h-4\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M5 13l4 4L19 7\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 287,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 281,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleCancelEdit,\n                  className: \"text-gray-600 hover:text-gray-700\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-4 h-4\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M6 18L18 6M6 6l12 12\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 305,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 299,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 295,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 23\n              }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"font-medium text-green-800 dark:text-green-200 truncate text-sm\",\n                  children: session.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 316,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-green-600 dark:text-green-400 mt-1\",\n                  children: formatDate(session.updated_at)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 319,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 19\n            }, this), editingSession !== session.session_id && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: e => handleEditSession(session, e),\n                className: \"p-1 text-gray-400 hover:text-gray-600 rounded\",\n                title: \"Edit judul\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-4 h-4\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 339,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 333,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 328,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: e => handleDeleteSession(session.session_id, e),\n                className: \"p-1 text-gray-400 hover:text-red-600 rounded\",\n                title: \"Hapus percakapan\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-4 h-4\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 360,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 354,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 17\n          }, this)\n        }, session.session_id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 228,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 120,\n    columnNumber: 5\n  }, this);\n};\n_s(ChatSidebar, \"gV8zyZuBiZQOh0rUWltsnQZy0BI=\", false, function () {\n  return [useAuth];\n});\n_c = ChatSidebar;\nexport default ChatSidebar;\nvar _c;\n$RefreshReg$(_c, \"ChatSidebar\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "getSessions", "deleteSession", "updateSession", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ChatSidebar", "currentSessionId", "onSessionSelect", "onNewChat", "onClose", "_s", "user", "sessions", "setSessions", "loading", "setLoading", "editingSession", "setEditingSession", "editTitle", "setEditTitle", "console", "log", "email", "loadSessions", "sessionsData", "length", "setTimeout", "error", "handleDeleteSession", "sessionId", "e", "stopPropagation", "window", "confirm", "filter", "s", "session_id", "alert", "handleEditSession", "session", "title", "handleSaveEdit", "map", "handleCancelEdit", "formatDate", "dateString", "date", "Date", "now", "diffTime", "Math", "abs", "diffDays", "ceil", "toLocaleDateString", "day", "month", "year", "className", "children", "onClick", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "type", "value", "onChange", "target", "onKeyPress", "key", "autoFocus", "updated_at", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/website kp/chatbot-unand/frontend/src/ChatSidebar.jsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\nimport { getSessions, deleteSession, updateSession } from \"./api\";\nimport { useAuth } from \"./contexts/AuthContext\";\n\nconst ChatSidebar = ({\n  currentSessionId,\n  onSessionSelect,\n  onNewChat,\n  onClose,\n}) => {\n  const { user } = useAuth();\n  const [sessions, setSessions] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [editingSession, setEditingSession] = useState(null);\n  const [editTitle, setEditTitle] = useState(\"\");\n\n  // Load sessions when component mounts or user changes\n  useEffect(() => {\n    console.log(\n      \"ChatSidebar: useEffect triggered, user:\",\n      user?.email || \"guest\"\n    );\n    loadSessions();\n  }, [user]); // Only depend on user, not empty array\n\n  const loadSessions = async () => {\n    try {\n      setLoading(true);\n      console.log(\n        \"ChatSidebar: Loading sessions for user:\",\n        user?.email || \"guest\"\n      );\n      const sessionsData = await getSessions();\n      console.log(\n        \"ChatSidebar: Loaded sessions:\",\n        sessionsData.length,\n        \"sessions\"\n      );\n      console.log(\"ChatSidebar: Sessions data:\", sessionsData);\n      setSessions(sessionsData);\n\n      // Debug: Log state after setting\n      setTimeout(() => {\n        console.log(\n          \"ChatSidebar: State after setSessions:\",\n          sessionsData.length\n        );\n      }, 100);\n    } catch (error) {\n      console.error(\"Error loading sessions:\", error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDeleteSession = async (sessionId, e) => {\n    e.stopPropagation();\n    if (window.confirm(\"Apakah Anda yakin ingin menghapus percakapan ini?\")) {\n      try {\n        await deleteSession(sessionId);\n        setSessions(sessions.filter((s) => s.session_id !== sessionId));\n        if (currentSessionId === sessionId) {\n          onNewChat();\n        }\n      } catch (error) {\n        console.error(\"Error deleting session:\", error);\n        alert(\"Gagal menghapus percakapan\");\n      }\n    }\n  };\n\n  const handleEditSession = (session, e) => {\n    e.stopPropagation();\n    setEditingSession(session.session_id);\n    setEditTitle(session.title);\n  };\n\n  const handleSaveEdit = async (sessionId) => {\n    try {\n      await updateSession(sessionId, editTitle);\n      setSessions(\n        sessions.map((s) =>\n          s.session_id === sessionId ? { ...s, title: editTitle } : s\n        )\n      );\n      setEditingSession(null);\n    } catch (error) {\n      console.error(\"Error updating session:\", error);\n      alert(\"Gagal mengubah judul percakapan\");\n    }\n  };\n\n  const handleCancelEdit = () => {\n    setEditingSession(null);\n    setEditTitle(\"\");\n  };\n\n  const formatDate = (dateString) => {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffTime = Math.abs(now - date);\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n\n    if (diffDays === 1) {\n      return \"Hari ini\";\n    } else if (diffDays === 2) {\n      return \"Kemarin\";\n    } else if (diffDays <= 7) {\n      return `${diffDays - 1} hari lalu`;\n    } else {\n      return date.toLocaleDateString(\"id-ID\", {\n        day: \"numeric\",\n        month: \"short\",\n        year: \"numeric\",\n      });\n    }\n  };\n\n  return (\n    <div className=\"w-80 bg-gradient-to-r from-green-50 to-yellow-50 dark:from-gray-800 dark:to-gray-700 lg:bg-transparent border-r-2 border-green-600 dark:border-green-400 flex flex-col h-screen\">\n      {/* Header */}\n      <div className=\"border-b-2 border-green-600 dark:border-green-400 p-3 lg:p-6 flex-shrink-0 bg-transparent\">\n        {/* Mobile Header */}\n        <div className=\"lg:hidden flex items-center gap-5 mb-3\">\n          {/* Close button for mobile */}\n          <button\n            onClick={onClose}\n            className=\"p-1 rounded-lg bg-green-600 dark:bg-green-500 text-white hover:bg-green-700 dark:hover:bg-green-600 transition-colors gap-6\"\n          >\n            <svg\n              className=\"w-5 h-5\"\n              fill=\"none\"\n              stroke=\"currentColor\"\n              viewBox=\"0 0 24 24\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                strokeWidth={2}\n                d=\"M6 18L18 6M6 6l12 12\"\n              />\n            </svg>\n          </button>\n\n          <div className=\"w-8 h-8 bg-gradient-to-br from-green-600 to-yellow-600 rounded-full flex items-center justify-center \">\n            <svg\n              className=\"w-4 h-4 text-white\"\n              fill=\"none\"\n              stroke=\"currentColor\"\n              viewBox=\"0 0 24 24\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                strokeWidth={2}\n                d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n              />\n            </svg>\n          </div>\n          <div className=\"flex-1\">\n            <h2 className=\"text-lg font-semibold text-green-800 dark:text-green-200\">\n              Riwayat Chat\n            </h2>\n            {user && (\n              <p className=\"text-sm text-green-600 dark:text-green-400 mt-1\">\n                {user.name || user.email}\n              </p>\n            )}\n          </div>\n        </div>\n\n        {/* Desktop Header - Exact same structure as main header */}\n        <div className=\"hidden lg:flex lg:items-center h-5 w-full \">\n          <div className=\"flex items-center gap-3\">\n            <div className=\"w-12 h-12 bg-gradient-to-br from-green-600 to-yellow-600 rounded-full flex items-center justify-center shadow-md\">\n              <svg\n                className=\"w-6 h-6 text-white\"\n                fill=\"none\"\n                stroke=\"currentColor\"\n                viewBox=\"0 0 24 24\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                />\n              </svg>\n            </div>\n            <div className=\"flex-1\">\n              <h2 className=\"text-xl font-bold text-green-700 dark:text-green-300\">\n                Riwayat Chat\n              </h2>\n              {user && (\n                <p className=\"text-sm text-green-600 dark:text-green-400 mt-1\">\n                  {user.name || user.email}\n                </p>\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* New Chat Button */}\n        <div className=\"mt-3 lg:mt-4 \">\n          <button\n            onClick={onNewChat}\n            className=\"w-full bg-gradient-to-r from-green-600 to-green-700 dark:from-green-500 dark:to-green-600 text-white px-4 py-2 rounded-lg hover:from-green-700 hover:to-green-800 dark:hover:from-green-600 dark:hover:to-green-700 transition-all duration-200 shadow-md hover:shadow-lg font-medium flex items-center justify-center gap-2\"\n          >\n            <svg\n              className=\"w-5 h-5\"\n              fill=\"none\"\n              stroke=\"currentColor\"\n              viewBox=\"0 0 24 24\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                strokeWidth={2}\n                d=\"M12 4v16m8-8H4\"\n              />\n            </svg>\n            Chat Baru\n          </button>\n        </div>\n      </div>\n\n      {/* Sessions List - Scrollable */}\n      <div className=\"flex-1 overflow-y-auto\">\n        {/* Debug: Log current sessions state */}\n        {console.log(\"ChatSidebar RENDER: sessions.length =\", sessions.length)}\n        {console.log(\"ChatSidebar RENDER: loading =\", loading)}\n        {console.log(\"ChatSidebar RENDER: user =\", user?.email)}\n\n        {loading ? (\n          <div className=\"p-4 text-center text-green-600 dark:text-green-400\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 dark:border-green-400 mx-auto\"></div>\n            <p className=\"mt-2\">Memuat riwayat...</p>\n          </div>\n        ) : sessions.length === 0 ? (\n          <div className=\"p-4 text-center text-green-600 dark:text-green-400\">\n            <p>Belum ada percakapan</p>\n            <p className=\"text-sm\">Mulai chat baru untuk melihat riwayat</p>\n          </div>\n        ) : (\n          <div className=\"p-2\">\n            {sessions.map((session) => (\n              <div\n                key={session.session_id}\n                onClick={() => onSessionSelect(session.session_id)}\n                className={`p-3 rounded-lg cursor-pointer transition-colors mb-2 group ${\n                  currentSessionId === session.session_id\n                    ? \"bg-gradient-to-r from-green-100 to-yellow-100 dark:from-green-800 dark:to-yellow-800 border-2 border-green-500 dark:border-green-400 shadow-md\"\n                    : \"hover:bg-gradient-to-r hover:from-green-50 hover:to-yellow-50 dark:hover:from-gray-700 dark:hover:to-gray-600 border border-transparent hover:border-green-300 dark:hover:border-green-500\"\n                }`}\n              >\n                <div className=\"flex items-start justify-between\">\n                  <div className=\"flex-1 min-w-0\">\n                    {editingSession === session.session_id ? (\n                      <div\n                        className=\"flex items-center gap-2\"\n                        onClick={(e) => e.stopPropagation()}\n                      >\n                        <input\n                          type=\"text\"\n                          value={editTitle}\n                          onChange={(e) => setEditTitle(e.target.value)}\n                          className=\"flex-1 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                          onKeyPress={(e) => {\n                            if (e.key === \"Enter\") {\n                              handleSaveEdit(session.session_id);\n                            } else if (e.key === \"Escape\") {\n                              handleCancelEdit();\n                            }\n                          }}\n                          autoFocus\n                        />\n                        <button\n                          onClick={() => handleSaveEdit(session.session_id)}\n                          className=\"text-green-600 hover:text-green-700\"\n                        >\n                          <svg\n                            className=\"w-4 h-4\"\n                            fill=\"none\"\n                            stroke=\"currentColor\"\n                            viewBox=\"0 0 24 24\"\n                          >\n                            <path\n                              strokeLinecap=\"round\"\n                              strokeLinejoin=\"round\"\n                              strokeWidth={2}\n                              d=\"M5 13l4 4L19 7\"\n                            />\n                          </svg>\n                        </button>\n                        <button\n                          onClick={handleCancelEdit}\n                          className=\"text-gray-600 hover:text-gray-700\"\n                        >\n                          <svg\n                            className=\"w-4 h-4\"\n                            fill=\"none\"\n                            stroke=\"currentColor\"\n                            viewBox=\"0 0 24 24\"\n                          >\n                            <path\n                              strokeLinecap=\"round\"\n                              strokeLinejoin=\"round\"\n                              strokeWidth={2}\n                              d=\"M6 18L18 6M6 6l12 12\"\n                            />\n                          </svg>\n                        </button>\n                      </div>\n                    ) : (\n                      <>\n                        <h3 className=\"font-medium text-green-800 dark:text-green-200 truncate text-sm\">\n                          {session.title}\n                        </h3>\n                        <p className=\"text-xs text-green-600 dark:text-green-400 mt-1\">\n                          {formatDate(session.updated_at)}\n                        </p>\n                      </>\n                    )}\n                  </div>\n\n                  {editingSession !== session.session_id && (\n                    <div className=\"flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity\">\n                      <button\n                        onClick={(e) => handleEditSession(session, e)}\n                        className=\"p-1 text-gray-400 hover:text-gray-600 rounded\"\n                        title=\"Edit judul\"\n                      >\n                        <svg\n                          className=\"w-4 h-4\"\n                          fill=\"none\"\n                          stroke=\"currentColor\"\n                          viewBox=\"0 0 24 24\"\n                        >\n                          <path\n                            strokeLinecap=\"round\"\n                            strokeLinejoin=\"round\"\n                            strokeWidth={2}\n                            d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                          />\n                        </svg>\n                      </button>\n                      <button\n                        onClick={(e) =>\n                          handleDeleteSession(session.session_id, e)\n                        }\n                        className=\"p-1 text-gray-400 hover:text-red-600 rounded\"\n                        title=\"Hapus percakapan\"\n                      >\n                        <svg\n                          className=\"w-4 h-4\"\n                          fill=\"none\"\n                          stroke=\"currentColor\"\n                          viewBox=\"0 0 24 24\"\n                        >\n                          <path\n                            strokeLinecap=\"round\"\n                            strokeLinejoin=\"round\"\n                            strokeWidth={2}\n                            d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                          />\n                        </svg>\n                      </button>\n                    </div>\n                  )}\n                </div>\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default ChatSidebar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,aAAa,EAAEC,aAAa,QAAQ,OAAO;AACjE,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEjD,MAAMC,WAAW,GAAGA,CAAC;EACnBC,gBAAgB;EAChBC,eAAe;EACfC,SAAS;EACTC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM;IAAEC;EAAK,CAAC,GAAGX,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACY,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACqB,cAAc,EAAEC,iBAAiB,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACuB,SAAS,EAAEC,YAAY,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;;EAE9C;EACAC,SAAS,CAAC,MAAM;IACdwB,OAAO,CAACC,GAAG,CACT,yCAAyC,EACzC,CAAAV,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEW,KAAK,KAAI,OACjB,CAAC;IACDC,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAACZ,IAAI,CAAC,CAAC,CAAC,CAAC;;EAEZ,MAAMY,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFR,UAAU,CAAC,IAAI,CAAC;MAChBK,OAAO,CAACC,GAAG,CACT,yCAAyC,EACzC,CAAAV,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEW,KAAK,KAAI,OACjB,CAAC;MACD,MAAME,YAAY,GAAG,MAAM3B,WAAW,CAAC,CAAC;MACxCuB,OAAO,CAACC,GAAG,CACT,+BAA+B,EAC/BG,YAAY,CAACC,MAAM,EACnB,UACF,CAAC;MACDL,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEG,YAAY,CAAC;MACxDX,WAAW,CAACW,YAAY,CAAC;;MAEzB;MACAE,UAAU,CAAC,MAAM;QACfN,OAAO,CAACC,GAAG,CACT,uCAAuC,EACvCG,YAAY,CAACC,MACf,CAAC;MACH,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD,CAAC,SAAS;MACRZ,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMa,mBAAmB,GAAG,MAAAA,CAAOC,SAAS,EAAEC,CAAC,KAAK;IAClDA,CAAC,CAACC,eAAe,CAAC,CAAC;IACnB,IAAIC,MAAM,CAACC,OAAO,CAAC,mDAAmD,CAAC,EAAE;MACvE,IAAI;QACF,MAAMnC,aAAa,CAAC+B,SAAS,CAAC;QAC9BhB,WAAW,CAACD,QAAQ,CAACsB,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAACC,UAAU,KAAKP,SAAS,CAAC,CAAC;QAC/D,IAAIvB,gBAAgB,KAAKuB,SAAS,EAAE;UAClCrB,SAAS,CAAC,CAAC;QACb;MACF,CAAC,CAAC,OAAOmB,KAAK,EAAE;QACdP,OAAO,CAACO,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/CU,KAAK,CAAC,4BAA4B,CAAC;MACrC;IACF;EACF,CAAC;EAED,MAAMC,iBAAiB,GAAGA,CAACC,OAAO,EAAET,CAAC,KAAK;IACxCA,CAAC,CAACC,eAAe,CAAC,CAAC;IACnBd,iBAAiB,CAACsB,OAAO,CAACH,UAAU,CAAC;IACrCjB,YAAY,CAACoB,OAAO,CAACC,KAAK,CAAC;EAC7B,CAAC;EAED,MAAMC,cAAc,GAAG,MAAOZ,SAAS,IAAK;IAC1C,IAAI;MACF,MAAM9B,aAAa,CAAC8B,SAAS,EAAEX,SAAS,CAAC;MACzCL,WAAW,CACTD,QAAQ,CAAC8B,GAAG,CAAEP,CAAC,IACbA,CAAC,CAACC,UAAU,KAAKP,SAAS,GAAG;QAAE,GAAGM,CAAC;QAAEK,KAAK,EAAEtB;MAAU,CAAC,GAAGiB,CAC5D,CACF,CAAC;MACDlB,iBAAiB,CAAC,IAAI,CAAC;IACzB,CAAC,CAAC,OAAOU,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CU,KAAK,CAAC,iCAAiC,CAAC;IAC1C;EACF,CAAC;EAED,MAAMM,gBAAgB,GAAGA,CAAA,KAAM;IAC7B1B,iBAAiB,CAAC,IAAI,CAAC;IACvBE,YAAY,CAAC,EAAE,CAAC;EAClB,CAAC;EAED,MAAMyB,UAAU,GAAIC,UAAU,IAAK;IACjC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,MAAMG,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;IACtB,MAAME,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACH,GAAG,GAAGF,IAAI,CAAC;IACrC,MAAMM,QAAQ,GAAGF,IAAI,CAACG,IAAI,CAACJ,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAE5D,IAAIG,QAAQ,KAAK,CAAC,EAAE;MAClB,OAAO,UAAU;IACnB,CAAC,MAAM,IAAIA,QAAQ,KAAK,CAAC,EAAE;MACzB,OAAO,SAAS;IAClB,CAAC,MAAM,IAAIA,QAAQ,IAAI,CAAC,EAAE;MACxB,OAAO,GAAGA,QAAQ,GAAG,CAAC,YAAY;IACpC,CAAC,MAAM;MACL,OAAON,IAAI,CAACQ,kBAAkB,CAAC,OAAO,EAAE;QACtCC,GAAG,EAAE,SAAS;QACdC,KAAK,EAAE,OAAO;QACdC,IAAI,EAAE;MACR,CAAC,CAAC;IACJ;EACF,CAAC;EAED,oBACEvD,OAAA;IAAKwD,SAAS,EAAC,iLAAiL;IAAAC,QAAA,gBAE9LzD,OAAA;MAAKwD,SAAS,EAAC,2FAA2F;MAAAC,QAAA,gBAExGzD,OAAA;QAAKwD,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBAErDzD,OAAA;UACE0D,OAAO,EAAEnD,OAAQ;UACjBiD,SAAS,EAAC,6HAA6H;UAAAC,QAAA,eAEvIzD,OAAA;YACEwD,SAAS,EAAC,SAAS;YACnBG,IAAI,EAAC,MAAM;YACXC,MAAM,EAAC,cAAc;YACrBC,OAAO,EAAC,WAAW;YAAAJ,QAAA,eAEnBzD,OAAA;cACE8D,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC,OAAO;cACtBC,WAAW,EAAE,CAAE;cACfC,CAAC,EAAC;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAETrE,OAAA;UAAKwD,SAAS,EAAC,uGAAuG;UAAAC,QAAA,eACpHzD,OAAA;YACEwD,SAAS,EAAC,oBAAoB;YAC9BG,IAAI,EAAC,MAAM;YACXC,MAAM,EAAC,cAAc;YACrBC,OAAO,EAAC,WAAW;YAAAJ,QAAA,eAEnBzD,OAAA;cACE8D,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC,OAAO;cACtBC,WAAW,EAAE,CAAE;cACfC,CAAC,EAAC;YAA6C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNrE,OAAA;UAAKwD,SAAS,EAAC,QAAQ;UAAAC,QAAA,gBACrBzD,OAAA;YAAIwD,SAAS,EAAC,0DAA0D;YAAAC,QAAA,EAAC;UAEzE;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACJ5D,IAAI,iBACHT,OAAA;YAAGwD,SAAS,EAAC,iDAAiD;YAAAC,QAAA,EAC3DhD,IAAI,CAAC6D,IAAI,IAAI7D,IAAI,CAACW;UAAK;YAAA8C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CACJ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNrE,OAAA;QAAKwD,SAAS,EAAC,4CAA4C;QAAAC,QAAA,eACzDzD,OAAA;UAAKwD,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACtCzD,OAAA;YAAKwD,SAAS,EAAC,kHAAkH;YAAAC,QAAA,eAC/HzD,OAAA;cACEwD,SAAS,EAAC,oBAAoB;cAC9BG,IAAI,EAAC,MAAM;cACXC,MAAM,EAAC,cAAc;cACrBC,OAAO,EAAC,WAAW;cAAAJ,QAAA,eAEnBzD,OAAA;gBACE8D,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC,OAAO;gBACtBC,WAAW,EAAE,CAAE;gBACfC,CAAC,EAAC;cAA6C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNrE,OAAA;YAAKwD,SAAS,EAAC,QAAQ;YAAAC,QAAA,gBACrBzD,OAAA;cAAIwD,SAAS,EAAC,sDAAsD;cAAAC,QAAA,EAAC;YAErE;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACJ5D,IAAI,iBACHT,OAAA;cAAGwD,SAAS,EAAC,iDAAiD;cAAAC,QAAA,EAC3DhD,IAAI,CAAC6D,IAAI,IAAI7D,IAAI,CAACW;YAAK;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CACJ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNrE,OAAA;QAAKwD,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC5BzD,OAAA;UACE0D,OAAO,EAAEpD,SAAU;UACnBkD,SAAS,EAAC,8TAA8T;UAAAC,QAAA,gBAExUzD,OAAA;YACEwD,SAAS,EAAC,SAAS;YACnBG,IAAI,EAAC,MAAM;YACXC,MAAM,EAAC,cAAc;YACrBC,OAAO,EAAC,WAAW;YAAAJ,QAAA,eAEnBzD,OAAA;cACE8D,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC,OAAO;cACtBC,WAAW,EAAE,CAAE;cACfC,CAAC,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,aAER;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrE,OAAA;MAAKwD,SAAS,EAAC,wBAAwB;MAAAC,QAAA,GAEpCvC,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAET,QAAQ,CAACa,MAAM,CAAC,EACrEL,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEP,OAAO,CAAC,EACrDM,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEV,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEW,KAAK,CAAC,EAEtDR,OAAO,gBACNZ,OAAA;QAAKwD,SAAS,EAAC,oDAAoD;QAAAC,QAAA,gBACjEzD,OAAA;UAAKwD,SAAS,EAAC;QAA6F;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnHrE,OAAA;UAAGwD,SAAS,EAAC,MAAM;UAAAC,QAAA,EAAC;QAAiB;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,GACJ3D,QAAQ,CAACa,MAAM,KAAK,CAAC,gBACvBvB,OAAA;QAAKwD,SAAS,EAAC,oDAAoD;QAAAC,QAAA,gBACjEzD,OAAA;UAAAyD,QAAA,EAAG;QAAoB;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC3BrE,OAAA;UAAGwD,SAAS,EAAC,SAAS;UAAAC,QAAA,EAAC;QAAqC;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D,CAAC,gBAENrE,OAAA;QAAKwD,SAAS,EAAC,KAAK;QAAAC,QAAA,EACjB/C,QAAQ,CAAC8B,GAAG,CAAEH,OAAO,iBACpBrC,OAAA;UAEE0D,OAAO,EAAEA,CAAA,KAAMrD,eAAe,CAACgC,OAAO,CAACH,UAAU,CAAE;UACnDsB,SAAS,EAAE,8DACTpD,gBAAgB,KAAKiC,OAAO,CAACH,UAAU,GACnC,gJAAgJ,GAChJ,4LAA4L,EAC/L;UAAAuB,QAAA,eAEHzD,OAAA;YAAKwD,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/CzD,OAAA;cAAKwD,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAC5B3C,cAAc,KAAKuB,OAAO,CAACH,UAAU,gBACpClC,OAAA;gBACEwD,SAAS,EAAC,yBAAyB;gBACnCE,OAAO,EAAG9B,CAAC,IAAKA,CAAC,CAACC,eAAe,CAAC,CAAE;gBAAA4B,QAAA,gBAEpCzD,OAAA;kBACEuE,IAAI,EAAC,MAAM;kBACXC,KAAK,EAAExD,SAAU;kBACjByD,QAAQ,EAAG7C,CAAC,IAAKX,YAAY,CAACW,CAAC,CAAC8C,MAAM,CAACF,KAAK,CAAE;kBAC9ChB,SAAS,EAAC,6GAA6G;kBACvHmB,UAAU,EAAG/C,CAAC,IAAK;oBACjB,IAAIA,CAAC,CAACgD,GAAG,KAAK,OAAO,EAAE;sBACrBrC,cAAc,CAACF,OAAO,CAACH,UAAU,CAAC;oBACpC,CAAC,MAAM,IAAIN,CAAC,CAACgD,GAAG,KAAK,QAAQ,EAAE;sBAC7BnC,gBAAgB,CAAC,CAAC;oBACpB;kBACF,CAAE;kBACFoC,SAAS;gBAAA;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACFrE,OAAA;kBACE0D,OAAO,EAAEA,CAAA,KAAMnB,cAAc,CAACF,OAAO,CAACH,UAAU,CAAE;kBAClDsB,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,eAE/CzD,OAAA;oBACEwD,SAAS,EAAC,SAAS;oBACnBG,IAAI,EAAC,MAAM;oBACXC,MAAM,EAAC,cAAc;oBACrBC,OAAO,EAAC,WAAW;oBAAAJ,QAAA,eAEnBzD,OAAA;sBACE8D,aAAa,EAAC,OAAO;sBACrBC,cAAc,EAAC,OAAO;sBACtBC,WAAW,EAAE,CAAE;sBACfC,CAAC,EAAC;oBAAgB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACTrE,OAAA;kBACE0D,OAAO,EAAEjB,gBAAiB;kBAC1Be,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,eAE7CzD,OAAA;oBACEwD,SAAS,EAAC,SAAS;oBACnBG,IAAI,EAAC,MAAM;oBACXC,MAAM,EAAC,cAAc;oBACrBC,OAAO,EAAC,WAAW;oBAAAJ,QAAA,eAEnBzD,OAAA;sBACE8D,aAAa,EAAC,OAAO;sBACrBC,cAAc,EAAC,OAAO;sBACtBC,WAAW,EAAE,CAAE;sBACfC,CAAC,EAAC;oBAAsB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,gBAENrE,OAAA,CAAAE,SAAA;gBAAAuD,QAAA,gBACEzD,OAAA;kBAAIwD,SAAS,EAAC,iEAAiE;kBAAAC,QAAA,EAC5EpB,OAAO,CAACC;gBAAK;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,eACLrE,OAAA;kBAAGwD,SAAS,EAAC,iDAAiD;kBAAAC,QAAA,EAC3Df,UAAU,CAACL,OAAO,CAACyC,UAAU;gBAAC;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B,CAAC;cAAA,eACJ;YACH;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,EAELvD,cAAc,KAAKuB,OAAO,CAACH,UAAU,iBACpClC,OAAA;cAAKwD,SAAS,EAAC,8EAA8E;cAAAC,QAAA,gBAC3FzD,OAAA;gBACE0D,OAAO,EAAG9B,CAAC,IAAKQ,iBAAiB,CAACC,OAAO,EAAET,CAAC,CAAE;gBAC9C4B,SAAS,EAAC,+CAA+C;gBACzDlB,KAAK,EAAC,YAAY;gBAAAmB,QAAA,eAElBzD,OAAA;kBACEwD,SAAS,EAAC,SAAS;kBACnBG,IAAI,EAAC,MAAM;kBACXC,MAAM,EAAC,cAAc;kBACrBC,OAAO,EAAC,WAAW;kBAAAJ,QAAA,eAEnBzD,OAAA;oBACE8D,aAAa,EAAC,OAAO;oBACrBC,cAAc,EAAC,OAAO;oBACtBC,WAAW,EAAE,CAAE;oBACfC,CAAC,EAAC;kBAAwH;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3H;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACTrE,OAAA;gBACE0D,OAAO,EAAG9B,CAAC,IACTF,mBAAmB,CAACW,OAAO,CAACH,UAAU,EAAEN,CAAC,CAC1C;gBACD4B,SAAS,EAAC,8CAA8C;gBACxDlB,KAAK,EAAC,kBAAkB;gBAAAmB,QAAA,eAExBzD,OAAA;kBACEwD,SAAS,EAAC,SAAS;kBACnBG,IAAI,EAAC,MAAM;kBACXC,MAAM,EAAC,cAAc;kBACrBC,OAAO,EAAC,WAAW;kBAAAJ,QAAA,eAEnBzD,OAAA;oBACE8D,aAAa,EAAC,OAAO;oBACrBC,cAAc,EAAC,OAAO;oBACtBC,WAAW,EAAE,CAAE;oBACfC,CAAC,EAAC;kBAA8H;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC,GA1HDhC,OAAO,CAACH,UAAU;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA2HpB,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC7D,EAAA,CArXIL,WAAW;EAAA,QAMEL,OAAO;AAAA;AAAAiF,EAAA,GANpB5E,WAAW;AAuXjB,eAAeA,WAAW;AAAC,IAAA4E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}