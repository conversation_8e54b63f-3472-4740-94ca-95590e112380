{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website kp\\\\chatbot-unand\\\\frontend\\\\src\\\\components\\\\ui\\\\switch.jsx\";\nimport * as React from \"react\";\nimport * as SwitchPrimitives from \"@radix-ui/react-switch\";\nimport { cn } from \"../../lib/utils\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Switch = /*#__PURE__*/React.forwardRef(_c = ({\n  className,\n  ...props\n}, ref) => /*#__PURE__*/_jsxDEV(SwitchPrimitives.Root, {\n  className: cn(\"peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input\", className),\n  ...props,\n  ref: ref,\n  children: /*#__PURE__*/_jsxDEV(SwitchPrimitives.Thumb, {\n    className: cn(\"pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0\")\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 7,\n  columnNumber: 3\n}, this));\n_c2 = Switch;\nSwitch.displayName = SwitchPrimitives.Root.displayName;\nexport { Switch };\nvar _c, _c2;\n$RefreshReg$(_c, \"Switch$React.forwardRef\");\n$RefreshReg$(_c2, \"Switch\");", "map": {"version": 3, "names": ["React", "SwitchPrimitives", "cn", "jsxDEV", "_jsxDEV", "Switch", "forwardRef", "_c", "className", "props", "ref", "Root", "children", "Thumb", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c2", "displayName", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/website kp/chatbot-unand/frontend/src/components/ui/switch.jsx"], "sourcesContent": ["import * as React from \"react\";\r\nimport * as SwitchPrimitives from \"@radix-ui/react-switch\";\r\n\r\nimport { cn } from \"../../lib/utils\";\r\n\r\nconst Switch = React.forwardRef(({ className, ...props }, ref) => (\r\n  <SwitchPrimitives.Root\r\n    className={cn(\r\n      \"peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input\",\r\n      className\r\n    )}\r\n    {...props}\r\n    ref={ref}\r\n  >\r\n    <SwitchPrimitives.Thumb\r\n      className={cn(\r\n        \"pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0\"\r\n      )}\r\n    />\r\n  </SwitchPrimitives.Root>\r\n));\r\nSwitch.displayName = SwitchPrimitives.Root.displayName;\r\n\r\nexport { Switch };\r\n"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,gBAAgB,MAAM,wBAAwB;AAE1D,SAASC,EAAE,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,MAAMC,MAAM,gBAAGL,KAAK,CAACM,UAAU,CAAAC,EAAA,GAACA,CAAC;EAAEC,SAAS;EAAE,GAAGC;AAAM,CAAC,EAAEC,GAAG,kBAC3DN,OAAA,CAACH,gBAAgB,CAACU,IAAI;EACpBH,SAAS,EAAEN,EAAE,CACX,oXAAoX,EACpXM,SACF,CAAE;EAAA,GACEC,KAAK;EACTC,GAAG,EAAEA,GAAI;EAAAE,QAAA,eAETR,OAAA,CAACH,gBAAgB,CAACY,KAAK;IACrBL,SAAS,EAAEN,EAAE,CACX,4KACF;EAAE;IAAAY,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACmB,CACxB,CAAC;AAACC,GAAA,GAfGb,MAAM;AAgBZA,MAAM,CAACc,WAAW,GAAGlB,gBAAgB,CAACU,IAAI,CAACQ,WAAW;AAEtD,SAASd,MAAM;AAAG,IAAAE,EAAA,EAAAW,GAAA;AAAAE,YAAA,CAAAb,EAAA;AAAAa,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}