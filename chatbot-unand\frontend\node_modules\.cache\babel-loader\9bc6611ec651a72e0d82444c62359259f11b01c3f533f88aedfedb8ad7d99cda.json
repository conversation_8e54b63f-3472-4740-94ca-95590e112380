{"ast": null, "code": "var _jsxFileName = \"D:\\\\KAMPUS\\\\Magang TA\\\\MAGANG\\\\Project\\\\website kp\\\\chatbot-unand\\\\frontend\\\\src\\\\pages\\\\ChatWindow.jsx\",\n  _s = $RefreshSig$();\nimport { useState, useEffect, useRef } from \"react\";\nimport Message from \"../Message\";\nimport ChatInput from \"../ChatInput\";\nimport { sendMessageToChatbot } from \"../api\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ChatWindow = ({\n  messages,\n  setMessages,\n  currentSessionId,\n  setCurrentSessionId\n}) => {\n  _s();\n  const [isLoading, setIsLoading] = useState(false);\n  const messagesEndRef = useRef(null);\n  const scrollToBottom = () => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: \"smooth\"\n    });\n  };\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n  const handleSendMessage = async text => {\n    const userMessage = {\n      id: Date.now(),\n      text: text,\n      isBot: false,\n      timestamp: new Date()\n    };\n    setMessages(prevMessages => [...prevMessages, userMessage]);\n    setIsLoading(true);\n    try {\n      const response = await sendMessageToChatbot(text, currentSessionId);\n\n      // Update session ID if this is a new chat\n      if (!currentSessionId && response.session_id) {\n        setCurrentSessionId(response.session_id);\n      }\n      const botMessage = {\n        id: Date.now() + 1,\n        text: response.response,\n        isBot: true,\n        timestamp: new Date(),\n        sources: response.sources,\n        sources_count: response.sources_count,\n        summary: response.summary,\n        suggestions: response.suggestions,\n        is_greeting: response.is_greeting\n      };\n      setMessages(prevMessages => [...prevMessages, botMessage]);\n    } catch (error) {\n      const errorMessage = {\n        id: Date.now() + 1,\n        text: `Maaf, terjadi kesalahan saat menghubungi chatbot: ${error.message}`,\n        isBot: true,\n        timestamp: new Date(),\n        isError: true\n      };\n      setMessages(prevMessages => [...prevMessages, errorMessage]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col h-full bg-transparent font-sans\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 overflow-y-auto scrollbar-hide\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4 p-4 min-h-full flex flex-col\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 space-y-4\",\n          children: [messages.map(message => /*#__PURE__*/_jsxDEV(Message, {\n            message: message\n          }, message.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 15\n          }, this)), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-start\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-r from-green-100 to-yellow-100 dark:from-gray-700 dark:to-gray-600 border border-green-300 dark:border-gray-500 rounded-lg p-3 max-w-xs shadow-md\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex space-x-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-2 h-2 bg-green-600 dark:bg-green-400 rounded-full animate-bounce\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 82,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-2 h-2 bg-yellow-600 dark:bg-yellow-400 rounded-full animate-bounce\",\n                  style: {\n                    animationDelay: \"0.1s\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 83,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-2 h-2 bg-green-600 dark:bg-green-400 rounded-full animate-bounce\",\n                  style: {\n                    animationDelay: \"0.2s\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 87,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 81,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: messagesEndRef\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ChatInput, {\n      onSendMessage: handleSendMessage,\n      isLoading: isLoading\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 68,\n    columnNumber: 5\n  }, this);\n};\n_s(ChatWindow, \"cV5d+GtYuCNywgELhTIY1bCQiv0=\");\n_c = ChatWindow;\nexport default ChatWindow;\nvar _c;\n$RefreshReg$(_c, \"ChatWindow\");", "map": {"version": 3, "names": ["useState", "useEffect", "useRef", "Message", "ChatInput", "sendMessageToChatbot", "jsxDEV", "_jsxDEV", "ChatWindow", "messages", "setMessages", "currentSessionId", "setCurrentSessionId", "_s", "isLoading", "setIsLoading", "messagesEndRef", "scrollToBottom", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "handleSendMessage", "text", "userMessage", "id", "Date", "now", "isBot", "timestamp", "prevMessages", "response", "session_id", "botMessage", "sources", "sources_count", "summary", "suggestions", "is_greeting", "error", "errorMessage", "message", "isError", "className", "children", "map", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "animationDelay", "ref", "onSendMessage", "_c", "$RefreshReg$"], "sources": ["D:/KAMPUS/Magang TA/MAGANG/Project/website kp/chatbot-unand/frontend/src/pages/ChatWindow.jsx"], "sourcesContent": ["import { useState, useEffect, useRef } from \"react\";\nimport Message from \"../Message\";\nimport ChatInput from \"../ChatInput\";\nimport { sendMessageToChatbot } from \"../api\";\n\nconst ChatWindow = ({\n  messages,\n  setMessages,\n  currentSessionId,\n  setCurrentSessionId,\n}) => {\n  const [isLoading, setIsLoading] = useState(false);\n  const messagesEndRef = useRef(null);\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: \"smooth\" });\n  };\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  const handleSendMessage = async (text) => {\n    const userMessage = {\n      id: Date.now(),\n      text: text,\n      isBot: false,\n      timestamp: new Date(),\n    };\n    setMessages((prevMessages) => [...prevMessages, userMessage]);\n    setIsLoading(true);\n\n    try {\n      const response = await sendMessageToChatbot(text, currentSessionId);\n\n      // Update session ID if this is a new chat\n      if (!currentSessionId && response.session_id) {\n        setCurrentSessionId(response.session_id);\n      }\n\n      const botMessage = {\n        id: Date.now() + 1,\n        text: response.response,\n        isBot: true,\n        timestamp: new Date(),\n        sources: response.sources,\n        sources_count: response.sources_count,\n        summary: response.summary,\n        suggestions: response.suggestions,\n        is_greeting: response.is_greeting,\n      };\n      setMessages((prevMessages) => [...prevMessages, botMessage]);\n    } catch (error) {\n      const errorMessage = {\n        id: Date.now() + 1,\n        text: `Maaf, terjadi kesalahan saat menghubungi chatbot: ${error.message}`,\n        isBot: true,\n        timestamp: new Date(),\n        isError: true,\n      };\n      setMessages((prevMessages) => [...prevMessages, errorMessage]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"flex flex-col h-full bg-transparent font-sans\">\n      {/* Messages Area - Scrollable */}\n      <div className=\"flex-1 overflow-y-auto scrollbar-hide\">\n        <div className=\"space-y-4 p-4 min-h-full flex flex-col\">\n          {/* Messages container that grows to fill space */}\n          <div className=\"flex-1 space-y-4\">\n            {messages.map((message) => (\n              <Message key={message.id} message={message} />\n            ))}\n\n            {isLoading && (\n              <div className=\"flex justify-start\">\n                <div className=\"bg-gradient-to-r from-green-100 to-yellow-100 dark:from-gray-700 dark:to-gray-600 border border-green-300 dark:border-gray-500 rounded-lg p-3 max-w-xs shadow-md\">\n                  <div className=\"flex space-x-1\">\n                    <div className=\"w-2 h-2 bg-green-600 dark:bg-green-400 rounded-full animate-bounce\"></div>\n                    <div\n                      className=\"w-2 h-2 bg-yellow-600 dark:bg-yellow-400 rounded-full animate-bounce\"\n                      style={{ animationDelay: \"0.1s\" }}\n                    ></div>\n                    <div\n                      className=\"w-2 h-2 bg-green-600 dark:bg-green-400 rounded-full animate-bounce\"\n                      style={{ animationDelay: \"0.2s\" }}\n                    ></div>\n                  </div>\n                </div>\n              </div>\n            )}\n          </div>\n          <div ref={messagesEndRef} />\n        </div>\n      </div>\n\n      {/* Input Area - Fixed at bottom */}\n      <ChatInput onSendMessage={handleSendMessage} isLoading={isLoading} />\n    </div>\n  );\n};\n\nexport default ChatWindow;\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACnD,OAAOC,OAAO,MAAM,YAAY;AAChC,OAAOC,SAAS,MAAM,cAAc;AACpC,SAASC,oBAAoB,QAAQ,QAAQ;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,MAAMC,UAAU,GAAGA,CAAC;EAClBC,QAAQ;EACRC,WAAW;EACXC,gBAAgB;EAChBC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAMgB,cAAc,GAAGd,MAAM,CAAC,IAAI,CAAC;EAEnC,MAAMe,cAAc,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAC3B,CAAAA,qBAAA,GAAAF,cAAc,CAACG,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC;EAEDpB,SAAS,CAAC,MAAM;IACdgB,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACR,QAAQ,CAAC,CAAC;EAEd,MAAMa,iBAAiB,GAAG,MAAOC,IAAI,IAAK;IACxC,MAAMC,WAAW,GAAG;MAClBC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;MACdJ,IAAI,EAAEA,IAAI;MACVK,KAAK,EAAE,KAAK;MACZC,SAAS,EAAE,IAAIH,IAAI,CAAC;IACtB,CAAC;IACDhB,WAAW,CAAEoB,YAAY,IAAK,CAAC,GAAGA,YAAY,EAAEN,WAAW,CAAC,CAAC;IAC7DT,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACF,MAAMgB,QAAQ,GAAG,MAAM1B,oBAAoB,CAACkB,IAAI,EAAEZ,gBAAgB,CAAC;;MAEnE;MACA,IAAI,CAACA,gBAAgB,IAAIoB,QAAQ,CAACC,UAAU,EAAE;QAC5CpB,mBAAmB,CAACmB,QAAQ,CAACC,UAAU,CAAC;MAC1C;MAEA,MAAMC,UAAU,GAAG;QACjBR,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC;QAClBJ,IAAI,EAAEQ,QAAQ,CAACA,QAAQ;QACvBH,KAAK,EAAE,IAAI;QACXC,SAAS,EAAE,IAAIH,IAAI,CAAC,CAAC;QACrBQ,OAAO,EAAEH,QAAQ,CAACG,OAAO;QACzBC,aAAa,EAAEJ,QAAQ,CAACI,aAAa;QACrCC,OAAO,EAAEL,QAAQ,CAACK,OAAO;QACzBC,WAAW,EAAEN,QAAQ,CAACM,WAAW;QACjCC,WAAW,EAAEP,QAAQ,CAACO;MACxB,CAAC;MACD5B,WAAW,CAAEoB,YAAY,IAAK,CAAC,GAAGA,YAAY,EAAEG,UAAU,CAAC,CAAC;IAC9D,CAAC,CAAC,OAAOM,KAAK,EAAE;MACd,MAAMC,YAAY,GAAG;QACnBf,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC;QAClBJ,IAAI,EAAE,qDAAqDgB,KAAK,CAACE,OAAO,EAAE;QAC1Eb,KAAK,EAAE,IAAI;QACXC,SAAS,EAAE,IAAIH,IAAI,CAAC,CAAC;QACrBgB,OAAO,EAAE;MACX,CAAC;MACDhC,WAAW,CAAEoB,YAAY,IAAK,CAAC,GAAGA,YAAY,EAAEU,YAAY,CAAC,CAAC;IAChE,CAAC,SAAS;MACRzB,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,oBACER,OAAA;IAAKoC,SAAS,EAAC,+CAA+C;IAAAC,QAAA,gBAE5DrC,OAAA;MAAKoC,SAAS,EAAC,uCAAuC;MAAAC,QAAA,eACpDrC,OAAA;QAAKoC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBAErDrC,OAAA;UAAKoC,SAAS,EAAC,kBAAkB;UAAAC,QAAA,GAC9BnC,QAAQ,CAACoC,GAAG,CAAEJ,OAAO,iBACpBlC,OAAA,CAACJ,OAAO;YAAkBsC,OAAO,EAAEA;UAAQ,GAA7BA,OAAO,CAAChB,EAAE;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAqB,CAC9C,CAAC,EAEDnC,SAAS,iBACRP,OAAA;YAAKoC,SAAS,EAAC,oBAAoB;YAAAC,QAAA,eACjCrC,OAAA;cAAKoC,SAAS,EAAC,kKAAkK;cAAAC,QAAA,eAC/KrC,OAAA;gBAAKoC,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BrC,OAAA;kBAAKoC,SAAS,EAAC;gBAAoE;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC1F1C,OAAA;kBACEoC,SAAS,EAAC,sEAAsE;kBAChFO,KAAK,EAAE;oBAAEC,cAAc,EAAE;kBAAO;gBAAE;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B,CAAC,eACP1C,OAAA;kBACEoC,SAAS,EAAC,oEAAoE;kBAC9EO,KAAK,EAAE;oBAAEC,cAAc,EAAE;kBAAO;gBAAE;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACN1C,OAAA;UAAK6C,GAAG,EAAEpC;QAAe;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN1C,OAAA,CAACH,SAAS;MAACiD,aAAa,EAAE/B,iBAAkB;MAACR,SAAS,EAAEA;IAAU;MAAAgC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAClE,CAAC;AAEV,CAAC;AAACpC,EAAA,CAlGIL,UAAU;AAAA8C,EAAA,GAAV9C,UAAU;AAoGhB,eAAeA,UAAU;AAAC,IAAA8C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}