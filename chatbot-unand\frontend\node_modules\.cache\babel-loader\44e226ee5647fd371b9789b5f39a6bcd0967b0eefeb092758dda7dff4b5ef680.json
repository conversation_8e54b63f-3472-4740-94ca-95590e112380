{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website kp\\\\chatbot-unand\\\\frontend\\\\src\\\\components\\\\SessionGuard.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from \"react\";\nimport { useNavigate, useLocation } from \"react-router-dom\";\nimport { useAuth } from \"../contexts/AuthContext\";\n\n// Session Guard Component to prevent cross-interface access\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SessionGuard = ({\n  children,\n  requiredSessionType\n}) => {\n  _s();\n  const {\n    user,\n    adminUser,\n    hasRequiredSession,\n    isWrongInterface,\n    loading\n  } = useAuth();\n  const navigate = useNavigate();\n  const location = useLocation();\n  useEffect(() => {\n    // Skip check during loading\n    if (loading) return;\n    const currentPath = location.pathname;\n    const isAdminPath = currentPath.startsWith(\"/admin\");\n    const isUserPath = !isAdminPath;\n    console.log(\"SessionGuard: Checking session access\", {\n      currentPath,\n      sessionType,\n      requiredSessionType,\n      isAdminPath,\n      isUserPath\n    });\n\n    // Check for session conflicts\n    if (isSessionConflict(requiredSessionType)) {\n      console.log(\"SessionGuard: Session conflict detected, clearing all sessions\");\n      clearAllSessions();\n\n      // Redirect to appropriate login\n      if (requiredSessionType === \"admin\") {\n        navigate(\"/admin/login\", {\n          replace: true\n        });\n      } else {\n        navigate(\"/\", {\n          replace: true\n        });\n      }\n      return;\n    }\n\n    // Prevent admin users from accessing user interface\n    if (sessionType === \"admin\" && isUserPath) {\n      console.log(\"SessionGuard: Admin trying to access user interface, redirecting\");\n      navigate(\"/admin/dashboard\", {\n        replace: true\n      });\n      return;\n    }\n\n    // Prevent regular users from accessing admin interface\n    if (sessionType === \"user\" && isAdminPath && currentPath !== \"/admin/login\") {\n      console.log(\"SessionGuard: User trying to access admin interface, redirecting\");\n      navigate(\"/\", {\n        replace: true\n      });\n      return;\n    }\n  }, [sessionType, requiredSessionType, location.pathname, navigate, isSessionConflict, clearAllSessions, loading]);\n\n  // Show loading during session check\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-r from-green-50 to-yellow-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Memeriksa sesi...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 7\n    }, this);\n  }\n  return children;\n};\n_s(SessionGuard, \"5nd/XjAh93Wtrl82DNafPQ5LArQ=\", false, function () {\n  return [useAuth, useNavigate, useLocation];\n});\n_c = SessionGuard;\nexport default SessionGuard;\nvar _c;\n$RefreshReg$(_c, \"SessionGuard\");", "map": {"version": 3, "names": ["React", "useEffect", "useNavigate", "useLocation", "useAuth", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON>", "children", "requiredSessionType", "_s", "user", "adminUser", "hasRequiredSession", "isWrongInterface", "loading", "navigate", "location", "currentPath", "pathname", "isAdminPath", "startsWith", "isUserPath", "console", "log", "sessionType", "isSessionConflict", "clearAllSessions", "replace", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/website kp/chatbot-unand/frontend/src/components/SessionGuard.jsx"], "sourcesContent": ["import React, { useEffect } from \"react\";\nimport { useNavigate, useLocation } from \"react-router-dom\";\nimport { useAuth } from \"../contexts/AuthContext\";\n\n// Session Guard Component to prevent cross-interface access\nconst SessionGuard = ({ children, requiredSessionType }) => {\n  const { user, adminUser, hasRequiredSession, isWrongInterface, loading } =\n    useAuth();\n  const navigate = useNavigate();\n  const location = useLocation();\n\n  useEffect(() => {\n    // Skip check during loading\n    if (loading) return;\n\n    const currentPath = location.pathname;\n    const isAdminPath = currentPath.startsWith(\"/admin\");\n    const isUserPath = !isAdminPath;\n\n    console.log(\"SessionGuard: Checking session access\", {\n      currentPath,\n      sessionType,\n      requiredSessionType,\n      isAdminPath,\n      isUserPath,\n    });\n\n    // Check for session conflicts\n    if (isSessionConflict(requiredSessionType)) {\n      console.log(\n        \"SessionGuard: Session conflict detected, clearing all sessions\"\n      );\n      clearAllSessions();\n\n      // Redirect to appropriate login\n      if (requiredSessionType === \"admin\") {\n        navigate(\"/admin/login\", { replace: true });\n      } else {\n        navigate(\"/\", { replace: true });\n      }\n      return;\n    }\n\n    // Prevent admin users from accessing user interface\n    if (sessionType === \"admin\" && isUserPath) {\n      console.log(\n        \"SessionGuard: Admin trying to access user interface, redirecting\"\n      );\n      navigate(\"/admin/dashboard\", { replace: true });\n      return;\n    }\n\n    // Prevent regular users from accessing admin interface\n    if (\n      sessionType === \"user\" &&\n      isAdminPath &&\n      currentPath !== \"/admin/login\"\n    ) {\n      console.log(\n        \"SessionGuard: User trying to access admin interface, redirecting\"\n      );\n      navigate(\"/\", { replace: true });\n      return;\n    }\n  }, [\n    sessionType,\n    requiredSessionType,\n    location.pathname,\n    navigate,\n    isSessionConflict,\n    clearAllSessions,\n    loading,\n  ]);\n\n  // Show loading during session check\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-r from-green-50 to-yellow-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600\">Memeriksa sesi...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return children;\n};\n\nexport default SessionGuard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,OAAO,QAAQ,yBAAyB;;AAEjD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,YAAY,GAAGA,CAAC;EAAEC,QAAQ;EAAEC;AAAoB,CAAC,KAAK;EAAAC,EAAA;EAC1D,MAAM;IAAEC,IAAI;IAAEC,SAAS;IAAEC,kBAAkB;IAAEC,gBAAgB;IAAEC;EAAQ,CAAC,GACtEX,OAAO,CAAC,CAAC;EACX,MAAMY,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAC9B,MAAMe,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAE9BF,SAAS,CAAC,MAAM;IACd;IACA,IAAIc,OAAO,EAAE;IAEb,MAAMG,WAAW,GAAGD,QAAQ,CAACE,QAAQ;IACrC,MAAMC,WAAW,GAAGF,WAAW,CAACG,UAAU,CAAC,QAAQ,CAAC;IACpD,MAAMC,UAAU,GAAG,CAACF,WAAW;IAE/BG,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE;MACnDN,WAAW;MACXO,WAAW;MACXhB,mBAAmB;MACnBW,WAAW;MACXE;IACF,CAAC,CAAC;;IAEF;IACA,IAAII,iBAAiB,CAACjB,mBAAmB,CAAC,EAAE;MAC1Cc,OAAO,CAACC,GAAG,CACT,gEACF,CAAC;MACDG,gBAAgB,CAAC,CAAC;;MAElB;MACA,IAAIlB,mBAAmB,KAAK,OAAO,EAAE;QACnCO,QAAQ,CAAC,cAAc,EAAE;UAAEY,OAAO,EAAE;QAAK,CAAC,CAAC;MAC7C,CAAC,MAAM;QACLZ,QAAQ,CAAC,GAAG,EAAE;UAAEY,OAAO,EAAE;QAAK,CAAC,CAAC;MAClC;MACA;IACF;;IAEA;IACA,IAAIH,WAAW,KAAK,OAAO,IAAIH,UAAU,EAAE;MACzCC,OAAO,CAACC,GAAG,CACT,kEACF,CAAC;MACDR,QAAQ,CAAC,kBAAkB,EAAE;QAAEY,OAAO,EAAE;MAAK,CAAC,CAAC;MAC/C;IACF;;IAEA;IACA,IACEH,WAAW,KAAK,MAAM,IACtBL,WAAW,IACXF,WAAW,KAAK,cAAc,EAC9B;MACAK,OAAO,CAACC,GAAG,CACT,kEACF,CAAC;MACDR,QAAQ,CAAC,GAAG,EAAE;QAAEY,OAAO,EAAE;MAAK,CAAC,CAAC;MAChC;IACF;EACF,CAAC,EAAE,CACDH,WAAW,EACXhB,mBAAmB,EACnBQ,QAAQ,CAACE,QAAQ,EACjBH,QAAQ,EACRU,iBAAiB,EACjBC,gBAAgB,EAChBZ,OAAO,CACR,CAAC;;EAEF;EACA,IAAIA,OAAO,EAAE;IACX,oBACET,OAAA;MAAKuB,SAAS,EAAC,2FAA2F;MAAArB,QAAA,eACxGF,OAAA;QAAKuB,SAAS,EAAC,aAAa;QAAArB,QAAA,gBAC1BF,OAAA;UAAKuB,SAAS,EAAC;QAA8E;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACpG3B,OAAA;UAAGuB,SAAS,EAAC,eAAe;UAAArB,QAAA,EAAC;QAAiB;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,OAAOzB,QAAQ;AACjB,CAAC;AAACE,EAAA,CAlFIH,YAAY;EAAA,QAEdH,OAAO,EACQF,WAAW,EACXC,WAAW;AAAA;AAAA+B,EAAA,GAJxB3B,YAAY;AAoFlB,eAAeA,YAAY;AAAC,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}