{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website kp\\\\chatbot-unand\\\\frontend\\\\src\\\\contexts\\\\AuthContext.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from \"react\";\nimport { jwtDecode } from \"jwt-decode\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error(\"useAuth must be used within an AuthProvider\");\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [token, setToken] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Admin state\n  const [adminUser, setAdminUser] = useState(null);\n  const [adminToken, setAdminToken] = useState(null);\n\n  // Session type tracking\n  const [sessionType, setSessionType] = useState(null); // 'user' | 'admin' | null\n\n  // Check for existing token on mount\n  useEffect(() => {\n    const savedToken = localStorage.getItem(\"access_token\");\n    const savedUser = localStorage.getItem(\"user\");\n    const savedAdminToken = localStorage.getItem(\"admin_token\");\n    const savedAdminUser = localStorage.getItem(\"admin_user\");\n    const savedSessionType = localStorage.getItem(\"session_type\");\n\n    // Clear conflicting sessions - only one type allowed per browser\n    if (savedToken && savedAdminToken) {\n      console.log(\"AuthContext: Conflicting sessions detected, clearing all\");\n      localStorage.clear();\n      setLoading(false);\n      return;\n    }\n    if (savedToken && savedUser) {\n      try {\n        // Check if token is still valid\n        const decoded = jwtDecode(savedToken);\n        const currentTime = Date.now() / 1000;\n        if (decoded.exp > currentTime) {\n          setToken(savedToken);\n          setUser(JSON.parse(savedUser));\n          setSessionType(\"user\");\n          localStorage.setItem(\"session_type\", \"user\");\n        } else {\n          // Token expired, clear storage\n          localStorage.removeItem(\"access_token\");\n          localStorage.removeItem(\"user\");\n          localStorage.removeItem(\"session_type\");\n        }\n      } catch (error) {\n        console.error(\"Error decoding token:\", error);\n        localStorage.removeItem(\"access_token\");\n        localStorage.removeItem(\"user\");\n        localStorage.removeItem(\"session_type\");\n      }\n    }\n    if (savedAdminToken && savedAdminUser) {\n      try {\n        // Check if admin token is still valid\n        const decoded = jwtDecode(savedAdminToken);\n        const currentTime = Date.now() / 1000;\n        if (decoded.exp > currentTime) {\n          setAdminToken(savedAdminToken);\n          setAdminUser(JSON.parse(savedAdminUser));\n          setSessionType(\"admin\");\n          localStorage.setItem(\"session_type\", \"admin\");\n        } else {\n          // Admin token expired, clear storage\n          localStorage.removeItem(\"admin_token\");\n          localStorage.removeItem(\"admin_user\");\n          localStorage.removeItem(\"session_type\");\n        }\n      } catch (error) {\n        console.error(\"Error decoding admin token:\", error);\n        localStorage.removeItem(\"admin_token\");\n        localStorage.removeItem(\"admin_user\");\n        localStorage.removeItem(\"session_type\");\n      }\n    }\n    setLoading(false);\n  }, []);\n  const login = async googleToken => {\n    try {\n      console.log(\"AuthContext: Starting login process...\");\n\n      // Clear any existing authentication data first to avoid conflicts\n      forceLogout();\n\n      // Clear admin session if exists (session separation)\n      if (adminToken || adminUser) {\n        logoutAdmin();\n      }\n      const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || \"http://localhost:8000\";\n      console.log(\"AuthContext: Using API URL:\", API_BASE_URL);\n      const response = await fetch(`${API_BASE_URL}/auth/google`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          token: googleToken\n        })\n      });\n      console.log(\"AuthContext: Response status:\", response.status);\n      console.log(\"AuthContext: Response headers:\", response.headers);\n      if (!response.ok) {\n        const errorText = await response.text();\n        console.error(\"AuthContext: Response error:\", errorText);\n        console.error(\"AuthContext: Full response:\", response);\n        console.error(\"AuthContext: Response status:\", response.status);\n        console.error(\"AuthContext: Response statusText:\", response.statusText);\n\n        // Try to parse error as JSON if possible\n        let errorJson = null;\n        try {\n          errorJson = JSON.parse(errorText);\n          console.error(\"AuthContext: Parsed error JSON:\", errorJson);\n        } catch (e) {\n          console.error(\"AuthContext: Error text is not JSON:\", errorText);\n        }\n\n        // Handle specific token timing errors\n        if (errorJson && errorJson.detail && errorJson.detail.includes(\"Token used too early\")) {\n          console.log(\"AuthContext: Token timing issue detected, retrying in 2 seconds...\");\n          await new Promise(resolve => setTimeout(resolve, 2000));\n\n          // Retry the request\n          const retryResponse = await fetch(`${API_BASE_URL}/auth/google`, {\n            method: \"POST\",\n            headers: {\n              \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n              token: googleToken\n            })\n          });\n          if (retryResponse.ok) {\n            const retryData = await retryResponse.json();\n            console.log(\"AuthContext: Retry authentication successful\");\n\n            // Save to localStorage\n            localStorage.setItem(\"access_token\", retryData.access_token);\n            localStorage.setItem(\"user\", JSON.stringify(retryData.user));\n\n            // Update state\n            setToken(retryData.access_token);\n            setUser(retryData.user);\n            console.log(\"AuthContext: Login completed successfully after retry\");\n            return retryData;\n          }\n        }\n        throw new Error(`Authentication failed: ${response.status} - ${errorText}`);\n      }\n      const data = await response.json();\n      console.log(\"AuthContext: Response data:\", data);\n\n      // Save to localStorage\n      localStorage.setItem(\"access_token\", data.access_token);\n      localStorage.setItem(\"user\", JSON.stringify(data.user));\n      localStorage.setItem(\"session_type\", \"user\");\n\n      // Update state\n      setToken(data.access_token);\n      setUser(data.user);\n      setSessionType(\"user\");\n      console.log(\"AuthContext: Login completed successfully\");\n      return data;\n    } catch (error) {\n      console.error(\"AuthContext: Login error:\", error);\n      // Clear any partial authentication data on error\n      forceLogout();\n      throw error;\n    }\n  };\n  const logout = async () => {\n    console.log(\"AuthContext: Starting logout process...\");\n    try {\n      // Call backend logout endpoint if user is authenticated\n      if (token) {\n        const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || \"http://localhost:8000\";\n        console.log(\"AuthContext: Calling backend logout endpoint...\");\n        const response = await fetch(`${API_BASE_URL}/auth/logout`, {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${token}`\n          }\n        });\n        if (response.ok) {\n          const data = await response.json();\n          console.log(\"AuthContext: Backend logout successful:\", data);\n          console.log(`AuthContext: Deactivated ${data.sessions_deactivated} sessions`);\n        } else {\n          console.warn(\"AuthContext: Backend logout failed, but continuing with frontend logout\");\n        }\n      }\n    } catch (error) {\n      console.error(\"AuthContext: Error calling backend logout:\", error);\n      console.log(\"AuthContext: Continuing with frontend logout despite backend error\");\n    }\n\n    // Clear localStorage\n    localStorage.removeItem(\"access_token\");\n    localStorage.removeItem(\"user\");\n\n    // Clear state\n    setToken(null);\n    setUser(null);\n\n    // Sign out from Google more thoroughly\n    if (window.google && window.google.accounts) {\n      console.log(\"AuthContext: Signing out from Google...\");\n      try {\n        // Disable auto-select\n        window.google.accounts.id.disableAutoSelect();\n\n        // Revoke the current session\n        window.google.accounts.id.revoke(token, done => {\n          console.log(\"AuthContext: Google token revoked:\", done);\n        });\n\n        // Clear any Google session cookies\n        document.cookie = \"g_state=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.google.com;\";\n        document.cookie = \"g_csrf_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.google.com;\";\n      } catch (error) {\n        console.warn(\"AuthContext: Error during Google sign-out:\", error);\n      }\n    }\n    console.log(\"AuthContext: Logout completed\");\n  };\n  const forceLogout = () => {\n    console.log(\"AuthContext: Force logout - clearing all authentication data\");\n\n    // Clear localStorage immediately\n    localStorage.removeItem(\"access_token\");\n    localStorage.removeItem(\"user\");\n\n    // Clear state\n    setToken(null);\n    setUser(null);\n\n    // Clear Google session\n    if (window.google && window.google.accounts) {\n      try {\n        window.google.accounts.id.disableAutoSelect();\n      } catch (error) {\n        console.warn(\"AuthContext: Error during force Google sign-out:\", error);\n      }\n    }\n    console.log(\"AuthContext: Force logout completed\");\n  };\n  const isTokenExpired = token => {\n    if (!token) return true;\n    try {\n      // Decode JWT token to check expiry\n      const payload = JSON.parse(atob(token.split(\".\")[1]));\n      const currentTime = Math.floor(Date.now() / 1000);\n\n      // Check if token is expired (with 30 second buffer)\n      return payload.exp < currentTime + 30;\n    } catch (error) {\n      console.error(\"AuthContext: Error checking token expiry:\", error);\n      return true; // Assume expired if can't decode\n    }\n  };\n  const getAuthHeaders = () => {\n    if (token && !isTokenExpired(token)) {\n      return {\n        Authorization: `Bearer ${token}`,\n        \"Content-Type\": \"application/json\"\n      };\n    } else if (token && isTokenExpired(token)) {\n      // Token is expired, clear it\n      console.log(\"AuthContext: Token expired, clearing authentication\");\n      logout();\n      return {\n        \"Content-Type\": \"application/json\"\n      };\n    }\n    return {\n      \"Content-Type\": \"application/json\"\n    };\n  };\n\n  // Admin functions\n  const loginAdmin = async (email, password) => {\n    try {\n      console.log(\"AuthContext: Starting admin login process...\");\n      const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || \"http://localhost:8000\";\n      const response = await fetch(`${API_BASE_URL}/admin/login`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          email,\n          password\n        })\n      });\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || \"Admin login failed\");\n      }\n      const data = await response.json();\n      console.log(\"AuthContext: Admin login successful\");\n\n      // Save to localStorage\n      localStorage.setItem(\"admin_token\", data.access_token);\n      localStorage.setItem(\"admin_user\", JSON.stringify({\n        email: data.admin_email\n      }));\n\n      // Update state\n      setAdminToken(data.access_token);\n      setAdminUser({\n        email: data.admin_email\n      });\n      return data;\n    } catch (error) {\n      console.error(\"AuthContext: Admin login error:\", error);\n      throw error;\n    }\n  };\n  const logoutAdmin = () => {\n    console.log(\"AuthContext: Admin logout\");\n\n    // Clear localStorage\n    localStorage.removeItem(\"admin_token\");\n    localStorage.removeItem(\"admin_user\");\n\n    // Clear state\n    setAdminToken(null);\n    setAdminUser(null);\n  };\n  const getAdminAuthHeaders = () => {\n    if (adminToken && !isTokenExpired(adminToken)) {\n      return {\n        Authorization: `Bearer ${adminToken}`,\n        \"Content-Type\": \"application/json\"\n      };\n    } else if (adminToken && isTokenExpired(adminToken)) {\n      // Admin token is expired, clear it\n      console.log(\"AuthContext: Admin token expired, clearing authentication\");\n      logoutAdmin();\n      return {\n        \"Content-Type\": \"application/json\"\n      };\n    }\n    return {\n      \"Content-Type\": \"application/json\"\n    };\n  };\n  const value = {\n    user,\n    token,\n    loading,\n    login,\n    logout,\n    forceLogout,\n    getAuthHeaders,\n    isAuthenticated: !!user,\n    // Admin functions\n    adminUser,\n    adminToken,\n    loginAdmin,\n    logoutAdmin,\n    getAdminAuthHeaders,\n    isAdminAuthenticated: !!adminUser\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 422,\n    columnNumber: 10\n  }, this);\n};\n_s2(AuthProvider, \"JYAnmyL7JJZ9EhcolWjsFFfqCt4=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "jwtDecode", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "token", "setToken", "loading", "setLoading", "adminUser", "setAdminUser", "adminToken", "setAdminToken", "sessionType", "setSessionType", "savedToken", "localStorage", "getItem", "savedUser", "savedAdminToken", "savedAdminUser", "savedSessionType", "console", "log", "clear", "decoded", "currentTime", "Date", "now", "exp", "JSON", "parse", "setItem", "removeItem", "error", "login", "googleToken", "forceLogout", "logoutAdmin", "API_BASE_URL", "process", "env", "REACT_APP_API_BASE_URL", "response", "fetch", "method", "headers", "body", "stringify", "status", "ok", "errorText", "text", "statusText", "<PERSON><PERSON><PERSON>", "e", "detail", "includes", "Promise", "resolve", "setTimeout", "retryResponse", "retryData", "json", "access_token", "data", "logout", "Authorization", "sessions_deactivated", "warn", "window", "google", "accounts", "id", "disableAutoSelect", "revoke", "done", "document", "cookie", "isTokenExpired", "payload", "atob", "split", "Math", "floor", "getAuthHeaders", "loginAdmin", "email", "password", "errorData", "admin_email", "getAdminAuthHeaders", "value", "isAuthenticated", "isAdminAuthenticated", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/website kp/chatbot-unand/frontend/src/contexts/AuthContext.jsx"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from \"react\";\nimport { jwtDecode } from \"jwt-decode\";\n\nconst AuthContext = createContext();\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error(\"useAuth must be used within an AuthProvider\");\n  }\n  return context;\n};\n\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [token, setToken] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Admin state\n  const [adminUser, setAdminUser] = useState(null);\n  const [adminToken, setAdminToken] = useState(null);\n\n  // Session type tracking\n  const [sessionType, setSessionType] = useState(null); // 'user' | 'admin' | null\n\n  // Check for existing token on mount\n  useEffect(() => {\n    const savedToken = localStorage.getItem(\"access_token\");\n    const savedUser = localStorage.getItem(\"user\");\n    const savedAdminToken = localStorage.getItem(\"admin_token\");\n    const savedAdminUser = localStorage.getItem(\"admin_user\");\n    const savedSessionType = localStorage.getItem(\"session_type\");\n\n    // Clear conflicting sessions - only one type allowed per browser\n    if (savedToken && savedAdminToken) {\n      console.log(\"AuthContext: Conflicting sessions detected, clearing all\");\n      localStorage.clear();\n      setLoading(false);\n      return;\n    }\n\n    if (savedToken && savedUser) {\n      try {\n        // Check if token is still valid\n        const decoded = jwtDecode(savedToken);\n        const currentTime = Date.now() / 1000;\n\n        if (decoded.exp > currentTime) {\n          setToken(savedToken);\n          setUser(JSON.parse(savedUser));\n          setSessionType(\"user\");\n          localStorage.setItem(\"session_type\", \"user\");\n        } else {\n          // Token expired, clear storage\n          localStorage.removeItem(\"access_token\");\n          localStorage.removeItem(\"user\");\n          localStorage.removeItem(\"session_type\");\n        }\n      } catch (error) {\n        console.error(\"Error decoding token:\", error);\n        localStorage.removeItem(\"access_token\");\n        localStorage.removeItem(\"user\");\n        localStorage.removeItem(\"session_type\");\n      }\n    }\n\n    if (savedAdminToken && savedAdminUser) {\n      try {\n        // Check if admin token is still valid\n        const decoded = jwtDecode(savedAdminToken);\n        const currentTime = Date.now() / 1000;\n\n        if (decoded.exp > currentTime) {\n          setAdminToken(savedAdminToken);\n          setAdminUser(JSON.parse(savedAdminUser));\n          setSessionType(\"admin\");\n          localStorage.setItem(\"session_type\", \"admin\");\n        } else {\n          // Admin token expired, clear storage\n          localStorage.removeItem(\"admin_token\");\n          localStorage.removeItem(\"admin_user\");\n          localStorage.removeItem(\"session_type\");\n        }\n      } catch (error) {\n        console.error(\"Error decoding admin token:\", error);\n        localStorage.removeItem(\"admin_token\");\n        localStorage.removeItem(\"admin_user\");\n        localStorage.removeItem(\"session_type\");\n      }\n    }\n\n    setLoading(false);\n  }, []);\n\n  const login = async (googleToken) => {\n    try {\n      console.log(\"AuthContext: Starting login process...\");\n\n      // Clear any existing authentication data first to avoid conflicts\n      forceLogout();\n\n      // Clear admin session if exists (session separation)\n      if (adminToken || adminUser) {\n        logoutAdmin();\n      }\n\n      const API_BASE_URL =\n        process.env.REACT_APP_API_BASE_URL || \"http://localhost:8000\";\n      console.log(\"AuthContext: Using API URL:\", API_BASE_URL);\n      const response = await fetch(`${API_BASE_URL}/auth/google`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify({ token: googleToken }),\n      });\n\n      console.log(\"AuthContext: Response status:\", response.status);\n      console.log(\"AuthContext: Response headers:\", response.headers);\n\n      if (!response.ok) {\n        const errorText = await response.text();\n        console.error(\"AuthContext: Response error:\", errorText);\n        console.error(\"AuthContext: Full response:\", response);\n        console.error(\"AuthContext: Response status:\", response.status);\n        console.error(\"AuthContext: Response statusText:\", response.statusText);\n\n        // Try to parse error as JSON if possible\n        let errorJson = null;\n        try {\n          errorJson = JSON.parse(errorText);\n          console.error(\"AuthContext: Parsed error JSON:\", errorJson);\n        } catch (e) {\n          console.error(\"AuthContext: Error text is not JSON:\", errorText);\n        }\n\n        // Handle specific token timing errors\n        if (\n          errorJson &&\n          errorJson.detail &&\n          errorJson.detail.includes(\"Token used too early\")\n        ) {\n          console.log(\n            \"AuthContext: Token timing issue detected, retrying in 2 seconds...\"\n          );\n          await new Promise((resolve) => setTimeout(resolve, 2000));\n\n          // Retry the request\n          const retryResponse = await fetch(`${API_BASE_URL}/auth/google`, {\n            method: \"POST\",\n            headers: {\n              \"Content-Type\": \"application/json\",\n            },\n            body: JSON.stringify({ token: googleToken }),\n          });\n\n          if (retryResponse.ok) {\n            const retryData = await retryResponse.json();\n            console.log(\"AuthContext: Retry authentication successful\");\n\n            // Save to localStorage\n            localStorage.setItem(\"access_token\", retryData.access_token);\n            localStorage.setItem(\"user\", JSON.stringify(retryData.user));\n\n            // Update state\n            setToken(retryData.access_token);\n            setUser(retryData.user);\n\n            console.log(\n              \"AuthContext: Login completed successfully after retry\"\n            );\n            return retryData;\n          }\n        }\n\n        throw new Error(\n          `Authentication failed: ${response.status} - ${errorText}`\n        );\n      }\n\n      const data = await response.json();\n      console.log(\"AuthContext: Response data:\", data);\n\n      // Save to localStorage\n      localStorage.setItem(\"access_token\", data.access_token);\n      localStorage.setItem(\"user\", JSON.stringify(data.user));\n      localStorage.setItem(\"session_type\", \"user\");\n\n      // Update state\n      setToken(data.access_token);\n      setUser(data.user);\n      setSessionType(\"user\");\n\n      console.log(\"AuthContext: Login completed successfully\");\n      return data;\n    } catch (error) {\n      console.error(\"AuthContext: Login error:\", error);\n      // Clear any partial authentication data on error\n      forceLogout();\n      throw error;\n    }\n  };\n\n  const logout = async () => {\n    console.log(\"AuthContext: Starting logout process...\");\n\n    try {\n      // Call backend logout endpoint if user is authenticated\n      if (token) {\n        const API_BASE_URL =\n          process.env.REACT_APP_API_BASE_URL || \"http://localhost:8000\";\n        console.log(\"AuthContext: Calling backend logout endpoint...\");\n\n        const response = await fetch(`${API_BASE_URL}/auth/logout`, {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${token}`,\n          },\n        });\n\n        if (response.ok) {\n          const data = await response.json();\n          console.log(\"AuthContext: Backend logout successful:\", data);\n          console.log(\n            `AuthContext: Deactivated ${data.sessions_deactivated} sessions`\n          );\n        } else {\n          console.warn(\n            \"AuthContext: Backend logout failed, but continuing with frontend logout\"\n          );\n        }\n      }\n    } catch (error) {\n      console.error(\"AuthContext: Error calling backend logout:\", error);\n      console.log(\n        \"AuthContext: Continuing with frontend logout despite backend error\"\n      );\n    }\n\n    // Clear localStorage\n    localStorage.removeItem(\"access_token\");\n    localStorage.removeItem(\"user\");\n\n    // Clear state\n    setToken(null);\n    setUser(null);\n\n    // Sign out from Google more thoroughly\n    if (window.google && window.google.accounts) {\n      console.log(\"AuthContext: Signing out from Google...\");\n      try {\n        // Disable auto-select\n        window.google.accounts.id.disableAutoSelect();\n\n        // Revoke the current session\n        window.google.accounts.id.revoke(token, (done) => {\n          console.log(\"AuthContext: Google token revoked:\", done);\n        });\n\n        // Clear any Google session cookies\n        document.cookie =\n          \"g_state=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.google.com;\";\n        document.cookie =\n          \"g_csrf_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.google.com;\";\n      } catch (error) {\n        console.warn(\"AuthContext: Error during Google sign-out:\", error);\n      }\n    }\n\n    console.log(\"AuthContext: Logout completed\");\n  };\n\n  const forceLogout = () => {\n    console.log(\"AuthContext: Force logout - clearing all authentication data\");\n\n    // Clear localStorage immediately\n    localStorage.removeItem(\"access_token\");\n    localStorage.removeItem(\"user\");\n\n    // Clear state\n    setToken(null);\n    setUser(null);\n\n    // Clear Google session\n    if (window.google && window.google.accounts) {\n      try {\n        window.google.accounts.id.disableAutoSelect();\n      } catch (error) {\n        console.warn(\"AuthContext: Error during force Google sign-out:\", error);\n      }\n    }\n\n    console.log(\"AuthContext: Force logout completed\");\n  };\n\n  const isTokenExpired = (token) => {\n    if (!token) return true;\n\n    try {\n      // Decode JWT token to check expiry\n      const payload = JSON.parse(atob(token.split(\".\")[1]));\n      const currentTime = Math.floor(Date.now() / 1000);\n\n      // Check if token is expired (with 30 second buffer)\n      return payload.exp < currentTime + 30;\n    } catch (error) {\n      console.error(\"AuthContext: Error checking token expiry:\", error);\n      return true; // Assume expired if can't decode\n    }\n  };\n\n  const getAuthHeaders = () => {\n    if (token && !isTokenExpired(token)) {\n      return {\n        Authorization: `Bearer ${token}`,\n        \"Content-Type\": \"application/json\",\n      };\n    } else if (token && isTokenExpired(token)) {\n      // Token is expired, clear it\n      console.log(\"AuthContext: Token expired, clearing authentication\");\n      logout();\n      return {\n        \"Content-Type\": \"application/json\",\n      };\n    }\n    return {\n      \"Content-Type\": \"application/json\",\n    };\n  };\n\n  // Admin functions\n  const loginAdmin = async (email, password) => {\n    try {\n      console.log(\"AuthContext: Starting admin login process...\");\n\n      const API_BASE_URL =\n        process.env.REACT_APP_API_BASE_URL || \"http://localhost:8000\";\n      const response = await fetch(`${API_BASE_URL}/admin/login`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify({ email, password }),\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || \"Admin login failed\");\n      }\n\n      const data = await response.json();\n      console.log(\"AuthContext: Admin login successful\");\n\n      // Save to localStorage\n      localStorage.setItem(\"admin_token\", data.access_token);\n      localStorage.setItem(\n        \"admin_user\",\n        JSON.stringify({ email: data.admin_email })\n      );\n\n      // Update state\n      setAdminToken(data.access_token);\n      setAdminUser({ email: data.admin_email });\n\n      return data;\n    } catch (error) {\n      console.error(\"AuthContext: Admin login error:\", error);\n      throw error;\n    }\n  };\n\n  const logoutAdmin = () => {\n    console.log(\"AuthContext: Admin logout\");\n\n    // Clear localStorage\n    localStorage.removeItem(\"admin_token\");\n    localStorage.removeItem(\"admin_user\");\n\n    // Clear state\n    setAdminToken(null);\n    setAdminUser(null);\n  };\n\n  const getAdminAuthHeaders = () => {\n    if (adminToken && !isTokenExpired(adminToken)) {\n      return {\n        Authorization: `Bearer ${adminToken}`,\n        \"Content-Type\": \"application/json\",\n      };\n    } else if (adminToken && isTokenExpired(adminToken)) {\n      // Admin token is expired, clear it\n      console.log(\"AuthContext: Admin token expired, clearing authentication\");\n      logoutAdmin();\n      return {\n        \"Content-Type\": \"application/json\",\n      };\n    }\n    return {\n      \"Content-Type\": \"application/json\",\n    };\n  };\n\n  const value = {\n    user,\n    token,\n    loading,\n    login,\n    logout,\n    forceLogout,\n    getAuthHeaders,\n    isAuthenticated: !!user,\n    // Admin functions\n    adminUser,\n    adminToken,\n    loginAdmin,\n    logoutAdmin,\n    getAdminAuthHeaders,\n    isAdminAuthenticated: !!adminUser,\n  };\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,SAASC,SAAS,QAAQ,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,WAAW,gBAAGP,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMQ,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGT,UAAU,CAACM,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACe,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACA,MAAM,CAACmB,SAAS,EAAEC,YAAY,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACqB,UAAU,EAAEC,aAAa,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACA,MAAM,CAACuB,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;;EAEtD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMwB,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;IACvD,MAAMC,SAAS,GAAGF,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IAC9C,MAAME,eAAe,GAAGH,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IAC3D,MAAMG,cAAc,GAAGJ,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IACzD,MAAMI,gBAAgB,GAAGL,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;;IAE7D;IACA,IAAIF,UAAU,IAAII,eAAe,EAAE;MACjCG,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;MACvEP,YAAY,CAACQ,KAAK,CAAC,CAAC;MACpBhB,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;IAEA,IAAIO,UAAU,IAAIG,SAAS,EAAE;MAC3B,IAAI;QACF;QACA,MAAMO,OAAO,GAAGjC,SAAS,CAACuB,UAAU,CAAC;QACrC,MAAMW,WAAW,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI;QAErC,IAAIH,OAAO,CAACI,GAAG,GAAGH,WAAW,EAAE;UAC7BpB,QAAQ,CAACS,UAAU,CAAC;UACpBX,OAAO,CAAC0B,IAAI,CAACC,KAAK,CAACb,SAAS,CAAC,CAAC;UAC9BJ,cAAc,CAAC,MAAM,CAAC;UACtBE,YAAY,CAACgB,OAAO,CAAC,cAAc,EAAE,MAAM,CAAC;QAC9C,CAAC,MAAM;UACL;UACAhB,YAAY,CAACiB,UAAU,CAAC,cAAc,CAAC;UACvCjB,YAAY,CAACiB,UAAU,CAAC,MAAM,CAAC;UAC/BjB,YAAY,CAACiB,UAAU,CAAC,cAAc,CAAC;QACzC;MACF,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdZ,OAAO,CAACY,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7ClB,YAAY,CAACiB,UAAU,CAAC,cAAc,CAAC;QACvCjB,YAAY,CAACiB,UAAU,CAAC,MAAM,CAAC;QAC/BjB,YAAY,CAACiB,UAAU,CAAC,cAAc,CAAC;MACzC;IACF;IAEA,IAAId,eAAe,IAAIC,cAAc,EAAE;MACrC,IAAI;QACF;QACA,MAAMK,OAAO,GAAGjC,SAAS,CAAC2B,eAAe,CAAC;QAC1C,MAAMO,WAAW,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI;QAErC,IAAIH,OAAO,CAACI,GAAG,GAAGH,WAAW,EAAE;UAC7Bd,aAAa,CAACO,eAAe,CAAC;UAC9BT,YAAY,CAACoB,IAAI,CAACC,KAAK,CAACX,cAAc,CAAC,CAAC;UACxCN,cAAc,CAAC,OAAO,CAAC;UACvBE,YAAY,CAACgB,OAAO,CAAC,cAAc,EAAE,OAAO,CAAC;QAC/C,CAAC,MAAM;UACL;UACAhB,YAAY,CAACiB,UAAU,CAAC,aAAa,CAAC;UACtCjB,YAAY,CAACiB,UAAU,CAAC,YAAY,CAAC;UACrCjB,YAAY,CAACiB,UAAU,CAAC,cAAc,CAAC;QACzC;MACF,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdZ,OAAO,CAACY,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnDlB,YAAY,CAACiB,UAAU,CAAC,aAAa,CAAC;QACtCjB,YAAY,CAACiB,UAAU,CAAC,YAAY,CAAC;QACrCjB,YAAY,CAACiB,UAAU,CAAC,cAAc,CAAC;MACzC;IACF;IAEAzB,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM2B,KAAK,GAAG,MAAOC,WAAW,IAAK;IACnC,IAAI;MACFd,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;MAErD;MACAc,WAAW,CAAC,CAAC;;MAEb;MACA,IAAI1B,UAAU,IAAIF,SAAS,EAAE;QAC3B6B,WAAW,CAAC,CAAC;MACf;MAEA,MAAMC,YAAY,GAChBC,OAAO,CAACC,GAAG,CAACC,sBAAsB,IAAI,uBAAuB;MAC/DpB,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEgB,YAAY,CAAC;MACxD,MAAMI,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGL,YAAY,cAAc,EAAE;QAC1DM,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEjB,IAAI,CAACkB,SAAS,CAAC;UAAE3C,KAAK,EAAE+B;QAAY,CAAC;MAC7C,CAAC,CAAC;MAEFd,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEoB,QAAQ,CAACM,MAAM,CAAC;MAC7D3B,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEoB,QAAQ,CAACG,OAAO,CAAC;MAE/D,IAAI,CAACH,QAAQ,CAACO,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAMR,QAAQ,CAACS,IAAI,CAAC,CAAC;QACvC9B,OAAO,CAACY,KAAK,CAAC,8BAA8B,EAAEiB,SAAS,CAAC;QACxD7B,OAAO,CAACY,KAAK,CAAC,6BAA6B,EAAES,QAAQ,CAAC;QACtDrB,OAAO,CAACY,KAAK,CAAC,+BAA+B,EAAES,QAAQ,CAACM,MAAM,CAAC;QAC/D3B,OAAO,CAACY,KAAK,CAAC,mCAAmC,EAAES,QAAQ,CAACU,UAAU,CAAC;;QAEvE;QACA,IAAIC,SAAS,GAAG,IAAI;QACpB,IAAI;UACFA,SAAS,GAAGxB,IAAI,CAACC,KAAK,CAACoB,SAAS,CAAC;UACjC7B,OAAO,CAACY,KAAK,CAAC,iCAAiC,EAAEoB,SAAS,CAAC;QAC7D,CAAC,CAAC,OAAOC,CAAC,EAAE;UACVjC,OAAO,CAACY,KAAK,CAAC,sCAAsC,EAAEiB,SAAS,CAAC;QAClE;;QAEA;QACA,IACEG,SAAS,IACTA,SAAS,CAACE,MAAM,IAChBF,SAAS,CAACE,MAAM,CAACC,QAAQ,CAAC,sBAAsB,CAAC,EACjD;UACAnC,OAAO,CAACC,GAAG,CACT,oEACF,CAAC;UACD,MAAM,IAAImC,OAAO,CAAEC,OAAO,IAAKC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;UAEzD;UACA,MAAME,aAAa,GAAG,MAAMjB,KAAK,CAAC,GAAGL,YAAY,cAAc,EAAE;YAC/DM,MAAM,EAAE,MAAM;YACdC,OAAO,EAAE;cACP,cAAc,EAAE;YAClB,CAAC;YACDC,IAAI,EAAEjB,IAAI,CAACkB,SAAS,CAAC;cAAE3C,KAAK,EAAE+B;YAAY,CAAC;UAC7C,CAAC,CAAC;UAEF,IAAIyB,aAAa,CAACX,EAAE,EAAE;YACpB,MAAMY,SAAS,GAAG,MAAMD,aAAa,CAACE,IAAI,CAAC,CAAC;YAC5CzC,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;;YAE3D;YACAP,YAAY,CAACgB,OAAO,CAAC,cAAc,EAAE8B,SAAS,CAACE,YAAY,CAAC;YAC5DhD,YAAY,CAACgB,OAAO,CAAC,MAAM,EAAEF,IAAI,CAACkB,SAAS,CAACc,SAAS,CAAC3D,IAAI,CAAC,CAAC;;YAE5D;YACAG,QAAQ,CAACwD,SAAS,CAACE,YAAY,CAAC;YAChC5D,OAAO,CAAC0D,SAAS,CAAC3D,IAAI,CAAC;YAEvBmB,OAAO,CAACC,GAAG,CACT,uDACF,CAAC;YACD,OAAOuC,SAAS;UAClB;QACF;QAEA,MAAM,IAAI/D,KAAK,CACb,0BAA0B4C,QAAQ,CAACM,MAAM,MAAME,SAAS,EAC1D,CAAC;MACH;MAEA,MAAMc,IAAI,GAAG,MAAMtB,QAAQ,CAACoB,IAAI,CAAC,CAAC;MAClCzC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE0C,IAAI,CAAC;;MAEhD;MACAjD,YAAY,CAACgB,OAAO,CAAC,cAAc,EAAEiC,IAAI,CAACD,YAAY,CAAC;MACvDhD,YAAY,CAACgB,OAAO,CAAC,MAAM,EAAEF,IAAI,CAACkB,SAAS,CAACiB,IAAI,CAAC9D,IAAI,CAAC,CAAC;MACvDa,YAAY,CAACgB,OAAO,CAAC,cAAc,EAAE,MAAM,CAAC;;MAE5C;MACA1B,QAAQ,CAAC2D,IAAI,CAACD,YAAY,CAAC;MAC3B5D,OAAO,CAAC6D,IAAI,CAAC9D,IAAI,CAAC;MAClBW,cAAc,CAAC,MAAM,CAAC;MAEtBQ,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;MACxD,OAAO0C,IAAI;IACb,CAAC,CAAC,OAAO/B,KAAK,EAAE;MACdZ,OAAO,CAACY,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD;MACAG,WAAW,CAAC,CAAC;MACb,MAAMH,KAAK;IACb;EACF,CAAC;EAED,MAAMgC,MAAM,GAAG,MAAAA,CAAA,KAAY;IACzB5C,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;IAEtD,IAAI;MACF;MACA,IAAIlB,KAAK,EAAE;QACT,MAAMkC,YAAY,GAChBC,OAAO,CAACC,GAAG,CAACC,sBAAsB,IAAI,uBAAuB;QAC/DpB,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;QAE9D,MAAMoB,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGL,YAAY,cAAc,EAAE;UAC1DM,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClCqB,aAAa,EAAE,UAAU9D,KAAK;UAChC;QACF,CAAC,CAAC;QAEF,IAAIsC,QAAQ,CAACO,EAAE,EAAE;UACf,MAAMe,IAAI,GAAG,MAAMtB,QAAQ,CAACoB,IAAI,CAAC,CAAC;UAClCzC,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAE0C,IAAI,CAAC;UAC5D3C,OAAO,CAACC,GAAG,CACT,4BAA4B0C,IAAI,CAACG,oBAAoB,WACvD,CAAC;QACH,CAAC,MAAM;UACL9C,OAAO,CAAC+C,IAAI,CACV,yEACF,CAAC;QACH;MACF;IACF,CAAC,CAAC,OAAOnC,KAAK,EAAE;MACdZ,OAAO,CAACY,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;MAClEZ,OAAO,CAACC,GAAG,CACT,oEACF,CAAC;IACH;;IAEA;IACAP,YAAY,CAACiB,UAAU,CAAC,cAAc,CAAC;IACvCjB,YAAY,CAACiB,UAAU,CAAC,MAAM,CAAC;;IAE/B;IACA3B,QAAQ,CAAC,IAAI,CAAC;IACdF,OAAO,CAAC,IAAI,CAAC;;IAEb;IACA,IAAIkE,MAAM,CAACC,MAAM,IAAID,MAAM,CAACC,MAAM,CAACC,QAAQ,EAAE;MAC3ClD,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;MACtD,IAAI;QACF;QACA+C,MAAM,CAACC,MAAM,CAACC,QAAQ,CAACC,EAAE,CAACC,iBAAiB,CAAC,CAAC;;QAE7C;QACAJ,MAAM,CAACC,MAAM,CAACC,QAAQ,CAACC,EAAE,CAACE,MAAM,CAACtE,KAAK,EAAGuE,IAAI,IAAK;UAChDtD,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEqD,IAAI,CAAC;QACzD,CAAC,CAAC;;QAEF;QACAC,QAAQ,CAACC,MAAM,GACb,8EAA8E;QAChFD,QAAQ,CAACC,MAAM,GACb,mFAAmF;MACvF,CAAC,CAAC,OAAO5C,KAAK,EAAE;QACdZ,OAAO,CAAC+C,IAAI,CAAC,4CAA4C,EAAEnC,KAAK,CAAC;MACnE;IACF;IAEAZ,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;EAC9C,CAAC;EAED,MAAMc,WAAW,GAAGA,CAAA,KAAM;IACxBf,OAAO,CAACC,GAAG,CAAC,8DAA8D,CAAC;;IAE3E;IACAP,YAAY,CAACiB,UAAU,CAAC,cAAc,CAAC;IACvCjB,YAAY,CAACiB,UAAU,CAAC,MAAM,CAAC;;IAE/B;IACA3B,QAAQ,CAAC,IAAI,CAAC;IACdF,OAAO,CAAC,IAAI,CAAC;;IAEb;IACA,IAAIkE,MAAM,CAACC,MAAM,IAAID,MAAM,CAACC,MAAM,CAACC,QAAQ,EAAE;MAC3C,IAAI;QACFF,MAAM,CAACC,MAAM,CAACC,QAAQ,CAACC,EAAE,CAACC,iBAAiB,CAAC,CAAC;MAC/C,CAAC,CAAC,OAAOxC,KAAK,EAAE;QACdZ,OAAO,CAAC+C,IAAI,CAAC,kDAAkD,EAAEnC,KAAK,CAAC;MACzE;IACF;IAEAZ,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;EACpD,CAAC;EAED,MAAMwD,cAAc,GAAI1E,KAAK,IAAK;IAChC,IAAI,CAACA,KAAK,EAAE,OAAO,IAAI;IAEvB,IAAI;MACF;MACA,MAAM2E,OAAO,GAAGlD,IAAI,CAACC,KAAK,CAACkD,IAAI,CAAC5E,KAAK,CAAC6E,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrD,MAAMxD,WAAW,GAAGyD,IAAI,CAACC,KAAK,CAACzD,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;;MAEjD;MACA,OAAOoD,OAAO,CAACnD,GAAG,GAAGH,WAAW,GAAG,EAAE;IACvC,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdZ,OAAO,CAACY,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACjE,OAAO,IAAI,CAAC,CAAC;IACf;EACF,CAAC;EAED,MAAMmD,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIhF,KAAK,IAAI,CAAC0E,cAAc,CAAC1E,KAAK,CAAC,EAAE;MACnC,OAAO;QACL8D,aAAa,EAAE,UAAU9D,KAAK,EAAE;QAChC,cAAc,EAAE;MAClB,CAAC;IACH,CAAC,MAAM,IAAIA,KAAK,IAAI0E,cAAc,CAAC1E,KAAK,CAAC,EAAE;MACzC;MACAiB,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;MAClE2C,MAAM,CAAC,CAAC;MACR,OAAO;QACL,cAAc,EAAE;MAClB,CAAC;IACH;IACA,OAAO;MACL,cAAc,EAAE;IAClB,CAAC;EACH,CAAC;;EAED;EACA,MAAMoB,UAAU,GAAG,MAAAA,CAAOC,KAAK,EAAEC,QAAQ,KAAK;IAC5C,IAAI;MACFlE,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;MAE3D,MAAMgB,YAAY,GAChBC,OAAO,CAACC,GAAG,CAACC,sBAAsB,IAAI,uBAAuB;MAC/D,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGL,YAAY,cAAc,EAAE;QAC1DM,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEjB,IAAI,CAACkB,SAAS,CAAC;UAAEuC,KAAK;UAAEC;QAAS,CAAC;MAC1C,CAAC,CAAC;MAEF,IAAI,CAAC7C,QAAQ,CAACO,EAAE,EAAE;QAChB,MAAMuC,SAAS,GAAG,MAAM9C,QAAQ,CAACoB,IAAI,CAAC,CAAC;QACvC,MAAM,IAAIhE,KAAK,CAAC0F,SAAS,CAACjC,MAAM,IAAI,oBAAoB,CAAC;MAC3D;MAEA,MAAMS,IAAI,GAAG,MAAMtB,QAAQ,CAACoB,IAAI,CAAC,CAAC;MAClCzC,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;;MAElD;MACAP,YAAY,CAACgB,OAAO,CAAC,aAAa,EAAEiC,IAAI,CAACD,YAAY,CAAC;MACtDhD,YAAY,CAACgB,OAAO,CAClB,YAAY,EACZF,IAAI,CAACkB,SAAS,CAAC;QAAEuC,KAAK,EAAEtB,IAAI,CAACyB;MAAY,CAAC,CAC5C,CAAC;;MAED;MACA9E,aAAa,CAACqD,IAAI,CAACD,YAAY,CAAC;MAChCtD,YAAY,CAAC;QAAE6E,KAAK,EAAEtB,IAAI,CAACyB;MAAY,CAAC,CAAC;MAEzC,OAAOzB,IAAI;IACb,CAAC,CAAC,OAAO/B,KAAK,EAAE;MACdZ,OAAO,CAACY,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAMI,WAAW,GAAGA,CAAA,KAAM;IACxBhB,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;;IAExC;IACAP,YAAY,CAACiB,UAAU,CAAC,aAAa,CAAC;IACtCjB,YAAY,CAACiB,UAAU,CAAC,YAAY,CAAC;;IAErC;IACArB,aAAa,CAAC,IAAI,CAAC;IACnBF,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMiF,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAIhF,UAAU,IAAI,CAACoE,cAAc,CAACpE,UAAU,CAAC,EAAE;MAC7C,OAAO;QACLwD,aAAa,EAAE,UAAUxD,UAAU,EAAE;QACrC,cAAc,EAAE;MAClB,CAAC;IACH,CAAC,MAAM,IAAIA,UAAU,IAAIoE,cAAc,CAACpE,UAAU,CAAC,EAAE;MACnD;MACAW,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;MACxEe,WAAW,CAAC,CAAC;MACb,OAAO;QACL,cAAc,EAAE;MAClB,CAAC;IACH;IACA,OAAO;MACL,cAAc,EAAE;IAClB,CAAC;EACH,CAAC;EAED,MAAMsD,KAAK,GAAG;IACZzF,IAAI;IACJE,KAAK;IACLE,OAAO;IACP4B,KAAK;IACL+B,MAAM;IACN7B,WAAW;IACXgD,cAAc;IACdQ,eAAe,EAAE,CAAC,CAAC1F,IAAI;IACvB;IACAM,SAAS;IACTE,UAAU;IACV2E,UAAU;IACVhD,WAAW;IACXqD,mBAAmB;IACnBG,oBAAoB,EAAE,CAAC,CAACrF;EAC1B,CAAC;EAED,oBAAOf,OAAA,CAACC,WAAW,CAACoG,QAAQ;IAACH,KAAK,EAAEA,KAAM;IAAA3F,QAAA,EAAEA;EAAQ;IAAA+F,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAuB,CAAC;AAC9E,CAAC;AAACjG,GAAA,CAzZWF,YAAY;AAAAoG,EAAA,GAAZpG,YAAY;AAAA,IAAAoG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}