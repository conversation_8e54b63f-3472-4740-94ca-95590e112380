{"ast": null, "code": "// Auth Troubleshooting Utilities\n\nexport const clearAllAuthData = () => {\n  console.log(\"🔧 Clearing all authentication data...\");\n\n  // Clear localStorage\n  const localStorageKeys = Object.keys(localStorage);\n  localStorageKeys.forEach(key => {\n    if (key.includes(\"token\") || key.includes(\"user\") || key.includes(\"auth\") || key.includes(\"google\")) {\n      localStorage.removeItem(key);\n      console.log(`🗑️ Removed localStorage: ${key}`);\n    }\n  });\n\n  // Clear sessionStorage\n  const sessionStorageKeys = Object.keys(sessionStorage);\n  sessionStorageKeys.forEach(key => {\n    if (key.includes(\"token\") || key.includes(\"user\") || key.includes(\"auth\") || key.includes(\"google\")) {\n      sessionStorage.removeItem(key);\n      console.log(`🗑️ Removed sessionStorage: ${key}`);\n    }\n  });\n\n  // Clear all cookies\n  document.cookie.split(\";\").forEach(c => {\n    const eqPos = c.indexOf(\"=\");\n    const name = eqPos > -1 ? c.substring(0, eqPos).trim() : c.trim();\n\n    // Clear for different domains and paths\n    const domains = [\"\", \".google.com\", \".accounts.google.com\", \".googleapis.com\"];\n    const paths = [\"/\", \"/auth\"];\n    domains.forEach(domain => {\n      paths.forEach(path => {\n        document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=${path}${domain ? `;domain=${domain}` : \"\"}`;\n      });\n    });\n    console.log(`🍪 Cleared cookie: ${name}`);\n  });\n  console.log(\"✅ All authentication data cleared\");\n};\nexport const resetGoogleAuth = () => {\n  console.log(\"🔄 Resetting Google Authentication...\");\n  try {\n    if (window.google && window.google.accounts) {\n      // Disable auto-select\n      window.google.accounts.id.disableAutoSelect();\n      console.log(\"✅ Google auto-select disabled\");\n\n      // Cancel any pending prompts\n      if (window.google.accounts.id.cancel) {\n        window.google.accounts.id.cancel();\n        console.log(\"✅ Google prompts cancelled\");\n      }\n    }\n\n    // Clear Google-specific cookies\n    const googleCookies = [\"g_state\", \"g_csrf_token\", \"__Host-1PLSID\", \"__Host-3PLSID\", \"APISID\", \"HSID\", \"NID\", \"SAPISID\", \"SID\", \"SIDCC\", \"SSID\"];\n    googleCookies.forEach(cookieName => {\n      document.cookie = `${cookieName}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=.google.com`;\n      document.cookie = `${cookieName}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=.accounts.google.com`;\n      document.cookie = `${cookieName}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;\n    });\n    console.log(\"✅ Google cookies cleared\");\n  } catch (error) {\n    console.warn(\"⚠️ Error resetting Google auth:\", error);\n  }\n};\nexport const diagnoseAuthIssue = () => {\n  console.log(\"🔍 Diagnosing authentication issues...\");\n  const diagnosis = {\n    localStorage: {},\n    sessionStorage: {},\n    cookies: [],\n    googleAPI: false,\n    timestamp: new Date().toISOString()\n  };\n\n  // Check localStorage\n  Object.keys(localStorage).forEach(key => {\n    diagnosis.localStorage[key] = localStorage.getItem(key);\n  });\n\n  // Check sessionStorage\n  Object.keys(sessionStorage).forEach(key => {\n    diagnosis.sessionStorage[key] = sessionStorage.getItem(key);\n  });\n\n  // Check cookies\n  diagnosis.cookies = document.cookie.split(\";\").map(c => c.trim());\n\n  // Check Google API\n  diagnosis.googleAPI = !!(window.google && window.google.accounts);\n  console.log(\"📊 Authentication Diagnosis:\", diagnosis);\n  return diagnosis;\n};\nexport const forceReloadWithCleanState = () => {\n  console.log(\"🔄 Force reloading with clean state...\");\n\n  // Clear everything\n  clearAllAuthData();\n  resetGoogleAuth();\n\n  // Add a flag to prevent auto-login on reload\n  sessionStorage.setItem(\"auth_force_reload\", \"true\");\n\n  // Reload the page\n  setTimeout(() => {\n    window.location.reload(true);\n  }, 500);\n};\n\n// Export all functions as default\nexport default {\n  clearAllAuthData,\n  resetGoogleAuth,\n  diagnoseAuthIssue,\n  forceReloadWithCleanState\n};", "map": {"version": 3, "names": ["clearAllAuthData", "console", "log", "localStorageKeys", "Object", "keys", "localStorage", "for<PERSON>ach", "key", "includes", "removeItem", "sessionStorageKeys", "sessionStorage", "document", "cookie", "split", "c", "eqPos", "indexOf", "name", "substring", "trim", "domains", "paths", "domain", "path", "resetGoogleAuth", "window", "google", "accounts", "id", "disableAutoSelect", "cancel", "googleCookies", "cookieName", "error", "warn", "diagnoseAuthIssue", "diagnosis", "cookies", "googleAPI", "timestamp", "Date", "toISOString", "getItem", "map", "forceReloadWithCleanState", "setItem", "setTimeout", "location", "reload"], "sources": ["C:/Users/<USER>/Downloads/website kp/chatbot-unand/frontend/src/utils/authTroubleshooting.js"], "sourcesContent": ["// Auth Troubleshooting Utilities\n\nexport const clearAllAuthData = () => {\n  console.log(\"🔧 Clearing all authentication data...\");\n\n  // Clear localStorage\n  const localStorageKeys = Object.keys(localStorage);\n  localStorageKeys.forEach((key) => {\n    if (\n      key.includes(\"token\") ||\n      key.includes(\"user\") ||\n      key.includes(\"auth\") ||\n      key.includes(\"google\")\n    ) {\n      localStorage.removeItem(key);\n      console.log(`🗑️ Removed localStorage: ${key}`);\n    }\n  });\n\n  // Clear sessionStorage\n  const sessionStorageKeys = Object.keys(sessionStorage);\n  sessionStorageKeys.forEach((key) => {\n    if (\n      key.includes(\"token\") ||\n      key.includes(\"user\") ||\n      key.includes(\"auth\") ||\n      key.includes(\"google\")\n    ) {\n      sessionStorage.removeItem(key);\n      console.log(`🗑️ Removed sessionStorage: ${key}`);\n    }\n  });\n\n  // Clear all cookies\n  document.cookie.split(\";\").forEach((c) => {\n    const eqPos = c.indexOf(\"=\");\n    const name = eqPos > -1 ? c.substring(0, eqPos).trim() : c.trim();\n\n    // Clear for different domains and paths\n    const domains = [\n      \"\",\n      \".google.com\",\n      \".accounts.google.com\",\n      \".googleapis.com\",\n    ];\n    const paths = [\"/\", \"/auth\"];\n\n    domains.forEach((domain) => {\n      paths.forEach((path) => {\n        document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=${path}${\n          domain ? `;domain=${domain}` : \"\"\n        }`;\n      });\n    });\n\n    console.log(`🍪 Cleared cookie: ${name}`);\n  });\n\n  console.log(\"✅ All authentication data cleared\");\n};\n\nexport const resetGoogleAuth = () => {\n  console.log(\"🔄 Resetting Google Authentication...\");\n\n  try {\n    if (window.google && window.google.accounts) {\n      // Disable auto-select\n      window.google.accounts.id.disableAutoSelect();\n      console.log(\"✅ Google auto-select disabled\");\n\n      // Cancel any pending prompts\n      if (window.google.accounts.id.cancel) {\n        window.google.accounts.id.cancel();\n        console.log(\"✅ Google prompts cancelled\");\n      }\n    }\n\n    // Clear Google-specific cookies\n    const googleCookies = [\n      \"g_state\",\n      \"g_csrf_token\",\n      \"__Host-1PLSID\",\n      \"__Host-3PLSID\",\n      \"APISID\",\n      \"HSID\",\n      \"NID\",\n      \"SAPISID\",\n      \"SID\",\n      \"SIDCC\",\n      \"SSID\",\n    ];\n\n    googleCookies.forEach((cookieName) => {\n      document.cookie = `${cookieName}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=.google.com`;\n      document.cookie = `${cookieName}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=.accounts.google.com`;\n      document.cookie = `${cookieName}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;\n    });\n\n    console.log(\"✅ Google cookies cleared\");\n  } catch (error) {\n    console.warn(\"⚠️ Error resetting Google auth:\", error);\n  }\n};\n\nexport const diagnoseAuthIssue = () => {\n  console.log(\"🔍 Diagnosing authentication issues...\");\n\n  const diagnosis = {\n    localStorage: {},\n    sessionStorage: {},\n    cookies: [],\n    googleAPI: false,\n    timestamp: new Date().toISOString(),\n  };\n\n  // Check localStorage\n  Object.keys(localStorage).forEach((key) => {\n    diagnosis.localStorage[key] = localStorage.getItem(key);\n  });\n\n  // Check sessionStorage\n  Object.keys(sessionStorage).forEach((key) => {\n    diagnosis.sessionStorage[key] = sessionStorage.getItem(key);\n  });\n\n  // Check cookies\n  diagnosis.cookies = document.cookie.split(\";\").map((c) => c.trim());\n\n  // Check Google API\n  diagnosis.googleAPI = !!(window.google && window.google.accounts);\n\n  console.log(\"📊 Authentication Diagnosis:\", diagnosis);\n  return diagnosis;\n};\n\nexport const forceReloadWithCleanState = () => {\n  console.log(\"🔄 Force reloading with clean state...\");\n\n  // Clear everything\n  clearAllAuthData();\n  resetGoogleAuth();\n\n  // Add a flag to prevent auto-login on reload\n  sessionStorage.setItem(\"auth_force_reload\", \"true\");\n\n  // Reload the page\n  setTimeout(() => {\n    window.location.reload(true);\n  }, 500);\n};\n\n// Export all functions as default\nexport default {\n  clearAllAuthData,\n  resetGoogleAuth,\n  diagnoseAuthIssue,\n  forceReloadWithCleanState,\n};\n"], "mappings": "AAAA;;AAEA,OAAO,MAAMA,gBAAgB,GAAGA,CAAA,KAAM;EACpCC,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;EAErD;EACA,MAAMC,gBAAgB,GAAGC,MAAM,CAACC,IAAI,CAACC,YAAY,CAAC;EAClDH,gBAAgB,CAACI,OAAO,CAAEC,GAAG,IAAK;IAChC,IACEA,GAAG,CAACC,QAAQ,CAAC,OAAO,CAAC,IACrBD,GAAG,CAACC,QAAQ,CAAC,MAAM,CAAC,IACpBD,GAAG,CAACC,QAAQ,CAAC,MAAM,CAAC,IACpBD,GAAG,CAACC,QAAQ,CAAC,QAAQ,CAAC,EACtB;MACAH,YAAY,CAACI,UAAU,CAACF,GAAG,CAAC;MAC5BP,OAAO,CAACC,GAAG,CAAC,6BAA6BM,GAAG,EAAE,CAAC;IACjD;EACF,CAAC,CAAC;;EAEF;EACA,MAAMG,kBAAkB,GAAGP,MAAM,CAACC,IAAI,CAACO,cAAc,CAAC;EACtDD,kBAAkB,CAACJ,OAAO,CAAEC,GAAG,IAAK;IAClC,IACEA,GAAG,CAACC,QAAQ,CAAC,OAAO,CAAC,IACrBD,GAAG,CAACC,QAAQ,CAAC,MAAM,CAAC,IACpBD,GAAG,CAACC,QAAQ,CAAC,MAAM,CAAC,IACpBD,GAAG,CAACC,QAAQ,CAAC,QAAQ,CAAC,EACtB;MACAG,cAAc,CAACF,UAAU,CAACF,GAAG,CAAC;MAC9BP,OAAO,CAACC,GAAG,CAAC,+BAA+BM,GAAG,EAAE,CAAC;IACnD;EACF,CAAC,CAAC;;EAEF;EACAK,QAAQ,CAACC,MAAM,CAACC,KAAK,CAAC,GAAG,CAAC,CAACR,OAAO,CAAES,CAAC,IAAK;IACxC,MAAMC,KAAK,GAAGD,CAAC,CAACE,OAAO,CAAC,GAAG,CAAC;IAC5B,MAAMC,IAAI,GAAGF,KAAK,GAAG,CAAC,CAAC,GAAGD,CAAC,CAACI,SAAS,CAAC,CAAC,EAAEH,KAAK,CAAC,CAACI,IAAI,CAAC,CAAC,GAAGL,CAAC,CAACK,IAAI,CAAC,CAAC;;IAEjE;IACA,MAAMC,OAAO,GAAG,CACd,EAAE,EACF,aAAa,EACb,sBAAsB,EACtB,iBAAiB,CAClB;IACD,MAAMC,KAAK,GAAG,CAAC,GAAG,EAAE,OAAO,CAAC;IAE5BD,OAAO,CAACf,OAAO,CAAEiB,MAAM,IAAK;MAC1BD,KAAK,CAAChB,OAAO,CAAEkB,IAAI,IAAK;QACtBZ,QAAQ,CAACC,MAAM,GAAG,GAAGK,IAAI,gDAAgDM,IAAI,GAC3ED,MAAM,GAAG,WAAWA,MAAM,EAAE,GAAG,EAAE,EACjC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFvB,OAAO,CAACC,GAAG,CAAC,sBAAsBiB,IAAI,EAAE,CAAC;EAC3C,CAAC,CAAC;EAEFlB,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;AAClD,CAAC;AAED,OAAO,MAAMwB,eAAe,GAAGA,CAAA,KAAM;EACnCzB,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;EAEpD,IAAI;IACF,IAAIyB,MAAM,CAACC,MAAM,IAAID,MAAM,CAACC,MAAM,CAACC,QAAQ,EAAE;MAC3C;MACAF,MAAM,CAACC,MAAM,CAACC,QAAQ,CAACC,EAAE,CAACC,iBAAiB,CAAC,CAAC;MAC7C9B,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;;MAE5C;MACA,IAAIyB,MAAM,CAACC,MAAM,CAACC,QAAQ,CAACC,EAAE,CAACE,MAAM,EAAE;QACpCL,MAAM,CAACC,MAAM,CAACC,QAAQ,CAACC,EAAE,CAACE,MAAM,CAAC,CAAC;QAClC/B,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;MAC3C;IACF;;IAEA;IACA,MAAM+B,aAAa,GAAG,CACpB,SAAS,EACT,cAAc,EACd,eAAe,EACf,eAAe,EACf,QAAQ,EACR,MAAM,EACN,KAAK,EACL,SAAS,EACT,KAAK,EACL,OAAO,EACP,MAAM,CACP;IAEDA,aAAa,CAAC1B,OAAO,CAAE2B,UAAU,IAAK;MACpCrB,QAAQ,CAACC,MAAM,GAAG,GAAGoB,UAAU,mEAAmE;MAClGrB,QAAQ,CAACC,MAAM,GAAG,GAAGoB,UAAU,4EAA4E;MAC3GrB,QAAQ,CAACC,MAAM,GAAG,GAAGoB,UAAU,gDAAgD;IACjF,CAAC,CAAC;IAEFjC,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;EACzC,CAAC,CAAC,OAAOiC,KAAK,EAAE;IACdlC,OAAO,CAACmC,IAAI,CAAC,iCAAiC,EAAED,KAAK,CAAC;EACxD;AACF,CAAC;AAED,OAAO,MAAME,iBAAiB,GAAGA,CAAA,KAAM;EACrCpC,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;EAErD,MAAMoC,SAAS,GAAG;IAChBhC,YAAY,EAAE,CAAC,CAAC;IAChBM,cAAc,EAAE,CAAC,CAAC;IAClB2B,OAAO,EAAE,EAAE;IACXC,SAAS,EAAE,KAAK;IAChBC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;EACpC,CAAC;;EAED;EACAvC,MAAM,CAACC,IAAI,CAACC,YAAY,CAAC,CAACC,OAAO,CAAEC,GAAG,IAAK;IACzC8B,SAAS,CAAChC,YAAY,CAACE,GAAG,CAAC,GAAGF,YAAY,CAACsC,OAAO,CAACpC,GAAG,CAAC;EACzD,CAAC,CAAC;;EAEF;EACAJ,MAAM,CAACC,IAAI,CAACO,cAAc,CAAC,CAACL,OAAO,CAAEC,GAAG,IAAK;IAC3C8B,SAAS,CAAC1B,cAAc,CAACJ,GAAG,CAAC,GAAGI,cAAc,CAACgC,OAAO,CAACpC,GAAG,CAAC;EAC7D,CAAC,CAAC;;EAEF;EACA8B,SAAS,CAACC,OAAO,GAAG1B,QAAQ,CAACC,MAAM,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC8B,GAAG,CAAE7B,CAAC,IAAKA,CAAC,CAACK,IAAI,CAAC,CAAC,CAAC;;EAEnE;EACAiB,SAAS,CAACE,SAAS,GAAG,CAAC,EAAEb,MAAM,CAACC,MAAM,IAAID,MAAM,CAACC,MAAM,CAACC,QAAQ,CAAC;EAEjE5B,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEoC,SAAS,CAAC;EACtD,OAAOA,SAAS;AAClB,CAAC;AAED,OAAO,MAAMQ,yBAAyB,GAAGA,CAAA,KAAM;EAC7C7C,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;EAErD;EACAF,gBAAgB,CAAC,CAAC;EAClB0B,eAAe,CAAC,CAAC;;EAEjB;EACAd,cAAc,CAACmC,OAAO,CAAC,mBAAmB,EAAE,MAAM,CAAC;;EAEnD;EACAC,UAAU,CAAC,MAAM;IACfrB,MAAM,CAACsB,QAAQ,CAACC,MAAM,CAAC,IAAI,CAAC;EAC9B,CAAC,EAAE,GAAG,CAAC;AACT,CAAC;;AAED;AACA,eAAe;EACblD,gBAAgB;EAChB0B,eAAe;EACfW,iBAAiB;EACjBS;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}