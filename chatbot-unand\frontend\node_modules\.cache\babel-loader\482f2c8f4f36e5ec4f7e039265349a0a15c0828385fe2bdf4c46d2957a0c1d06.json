{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 17h.01\",\n  key: \"nbq80n\"\n}], [\"path\", {\n  d: \"M10 7v6\",\n  key: \"nne03l\"\n}], [\"path\", {\n  d: \"M14 6h2a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2h-2\",\n  key: \"1m83kb\"\n}], [\"path\", {\n  d: \"M22 14v-4\",\n  key: \"14q9d5\"\n}], [\"path\", {\n  d: \"M6 18H4a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h2\",\n  key: \"h8lgfh\"\n}]];\nconst BatteryWarning = createLucideIcon(\"battery-warning\", __iconNode);\nexport { __iconNode, BatteryWarning as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "BatteryWarning", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\node_modules\\lucide-react\\src\\icons\\battery-warning.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M10 17h.01', key: 'nbq80n' }],\n  ['path', { d: 'M10 7v6', key: 'nne03l' }],\n  ['path', { d: 'M14 6h2a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2h-2', key: '1m83kb' }],\n  ['path', { d: 'M22 14v-4', key: '14q9d5' }],\n  ['path', { d: 'M6 18H4a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h2', key: 'h8lgfh' }],\n];\n\n/**\n * @component @name BatteryWarning\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAgMTdoLjAxIiAvPgogIDxwYXRoIGQ9Ik0xMCA3djYiIC8+CiAgPHBhdGggZD0iTTE0IDZoMmEyIDIgMCAwIDEgMiAydjhhMiAyIDAgMCAxLTIgMmgtMiIgLz4KICA8cGF0aCBkPSJNMjIgMTR2LTQiIC8+CiAgPHBhdGggZD0iTTYgMThINGEyIDIgMCAwIDEtMi0yVjhhMiAyIDAgMCAxIDItMmgyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/battery-warning\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst BatteryWarning = createLucideIcon('battery-warning', __iconNode);\n\nexport default BatteryWarning;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,0CAA4C;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzE,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,yCAA2C;EAAAC,GAAA,EAAK;AAAU,GAC1E;AAaM,MAAAC,cAAA,GAAiBC,gBAAiB,oBAAmBJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}