#!/usr/bin/env python3
"""
Migration script to move from FAISS to Qdrant vector database
"""

import os
import json
import sys
from pathlib import Path
from dotenv import load_dotenv
import google.generativeai as genai
from qdrant_service_pure import QdrantService

# Load environment variables
load_dotenv()

# Configure Gemini
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
if not GEMINI_API_KEY:
    print("❌ GEMINI_API_KEY not found in environment variables")
    sys.exit(1)

genai.configure(api_key=GEMINI_API_KEY)

def load_faiss_data():
    """Load existing FAISS data"""
    vector_db_dir = Path(__file__).parent / "vector_db"
    doc_chunks_path = vector_db_dir / "doc_chunks.json"
    
    if not doc_chunks_path.exists():
        print("❌ FAISS doc_chunks.json not found")
        return []
    
    try:
        with open(doc_chunks_path, 'r', encoding='utf-8') as f:
            chunks = json.load(f)
        print(f"✅ Loaded {len(chunks)} chunks from FAISS")
        return chunks
    except Exception as e:
        print(f"❌ Error loading FAISS data: {e}")
        return []

def migrate_to_qdrant(chunks):
    """Migrate chunks to Qdrant"""
    try:
        # Initialize Qdrant service
        qdrant_service = QdrantService()
        
        # Clear existing data
        print("🧹 Clearing existing Qdrant collection...")
        qdrant_service.clear_collection()
        
        # Prepare documents for batch insertion
        documents = []
        for i, chunk in enumerate(chunks):
            doc = {
                "text": chunk["text"],
                "metadata": {
                    "filename": chunk.get("filename", ""),
                    "filepath": chunk.get("filepath", ""),
                    "chunk_index": i,
                    "total_chunks": len(chunks)
                }
            }
            documents.append(doc)
        
        # Batch insert documents
        print(f"📤 Migrating {len(documents)} documents to Qdrant...")
        
        # Process in batches to avoid memory issues
        batch_size = 50
        total_migrated = 0
        
        for i in range(0, len(documents), batch_size):
            batch = documents[i:i + batch_size]
            try:
                doc_ids = qdrant_service.add_documents_batch(batch)
                total_migrated += len(doc_ids)
                print(f"✅ Migrated batch {i//batch_size + 1}: {len(doc_ids)} documents")
            except Exception as e:
                print(f"❌ Error migrating batch {i//batch_size + 1}: {e}")
                continue
        
        print(f"🎉 Migration completed! Total migrated: {total_migrated}/{len(documents)}")
        
        # Get collection info
        info = qdrant_service.get_collection_info()
        print(f"📊 Qdrant collection info: {info}")
        
        return True
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        return False

def test_qdrant_search():
    """Test Qdrant search functionality"""
    try:
        print("\n🔍 Testing Qdrant search...")
        qdrant_service = QdrantService()
        
        # Test search
        test_queries = [
            "Apa itu UNAND?",
            "Standar pendidikan tinggi",
            "Peraturan rektor"
        ]
        
        for query in test_queries:
            results = qdrant_service.search(query, limit=3)
            print(f"\n📝 Query: '{query}'")
            print(f"   Found {len(results)} results")
            
            for i, result in enumerate(results[:2]):  # Show top 2 results
                print(f"   {i+1}. Score: {result['score']:.3f}")
                print(f"      File: {result['filename']}")
                print(f"      Text: {result['text'][:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Search test failed: {e}")
        return False

def main():
    """Main migration function"""
    print("🚀 Starting FAISS to Qdrant migration...")
    print("=" * 50)
    
    # Load FAISS data
    chunks = load_faiss_data()
    if not chunks:
        print("❌ No data to migrate")
        return
    
    # Migrate to Qdrant
    success = migrate_to_qdrant(chunks)
    if not success:
        print("❌ Migration failed")
        return
    
    # Test search functionality
    test_success = test_qdrant_search()
    if not test_success:
        print("⚠️ Search test failed, but migration completed")
    
    print("\n" + "=" * 50)
    print("✅ Migration process completed!")
    print("\n📋 Next steps:")
    print("1. Update main.py to use QdrantService instead of FAISS")
    print("2. Restart the backend server")
    print("3. Test the chatbot functionality")

if __name__ == "__main__":
    main()
