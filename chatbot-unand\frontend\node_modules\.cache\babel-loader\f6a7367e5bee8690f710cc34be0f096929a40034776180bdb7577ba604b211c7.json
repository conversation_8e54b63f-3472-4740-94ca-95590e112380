{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website kp\\\\chatbot-unand\\\\frontend\\\\src\\\\components\\\\UserProfile.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { useAuth } from \"../contexts/AuthContext\";\nimport { User, LogOut, ChevronDown } from \"lucide-react\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst UserProfile = () => {\n  _s();\n  const {\n    user,\n    logout\n  } = useAuth();\n  const [isOpen, setIsOpen] = useState(false);\n  if (!user) return null;\n  const handleLogout = async () => {\n    try {\n      await logout();\n      setIsOpen(false);\n    } catch (error) {\n      console.error(\"Error during logout:\", error);\n      // Still close the dropdown even if logout fails\n      setIsOpen(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"relative\",\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: () => setIsOpen(!isOpen),\n      className: \"flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n      children: [user.picture ? /*#__PURE__*/_jsxDEV(\"img\", {\n        src: user.picture,\n        alt: user.name,\n        className: \"w-8 h-8 rounded-full border-2 border-green-500\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center\",\n        children: /*#__PURE__*/_jsxDEV(User, {\n          className: \"w-4 h-4 text-white\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hidden md:block text-left\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm font-medium text-gray-900 dark:text-white\",\n          children: user.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xs text-gray-500 dark:text-gray-400\",\n          children: user.email\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ChevronDown, {\n        className: `w-4 h-4 text-gray-500 transition-transform ${isOpen ? \"rotate-180\" : \"\"}`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this), isOpen && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 z-10\",\n        onClick: () => setIsOpen(false)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute right-0 mt-2 w-64 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-20\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4 border-b border-gray-200 dark:border-gray-700\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [user.picture ? /*#__PURE__*/_jsxDEV(\"img\", {\n              src: user.picture,\n              alt: user.name,\n              className: \"w-12 h-12 rounded-full border-2 border-green-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-green-500 rounded-full flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(User, {\n                className: \"w-6 h-6 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 77,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1 min-w-0\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-900 dark:text-white truncate\",\n                children: user.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 81,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-500 dark:text-gray-400 truncate\",\n                children: user.email\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 84,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"py-2\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleLogout,\n            className: \"w-full flex items-center px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors\",\n            children: [/*#__PURE__*/_jsxDEV(LogOut, {\n              className: \"w-4 h-4 mr-3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 17\n            }, this), \"Keluar\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 23,\n    columnNumber: 5\n  }, this);\n};\n_s(UserProfile, \"aCT5CBLv1CC5S2TixZ20AzxBJgs=\", false, function () {\n  return [useAuth];\n});\n_c = UserProfile;\nexport default UserProfile;\nvar _c;\n$RefreshReg$(_c, \"UserProfile\");", "map": {"version": 3, "names": ["React", "useState", "useAuth", "User", "LogOut", "ChevronDown", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "UserProfile", "_s", "user", "logout", "isOpen", "setIsOpen", "handleLogout", "error", "console", "className", "children", "onClick", "picture", "src", "alt", "name", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "email", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/website kp/chatbot-unand/frontend/src/components/UserProfile.jsx"], "sourcesContent": ["import React, { useState } from \"react\";\nimport { useAuth } from \"../contexts/AuthContext\";\nimport { User, LogOut, ChevronDown } from \"lucide-react\";\n\nconst UserProfile = () => {\n  const { user, logout } = useAuth();\n  const [isOpen, setIsOpen] = useState(false);\n\n  if (!user) return null;\n\n  const handleLogout = async () => {\n    try {\n      await logout();\n      setIsOpen(false);\n    } catch (error) {\n      console.error(\"Error during logout:\", error);\n      // Still close the dropdown even if logout fails\n      setIsOpen(false);\n    }\n  };\n\n  return (\n    <div className=\"relative\">\n      {/* User Button */}\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n      >\n        {user.picture ? (\n          <img\n            src={user.picture}\n            alt={user.name}\n            className=\"w-8 h-8 rounded-full border-2 border-green-500\"\n          />\n        ) : (\n          <div className=\"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center\">\n            <User className=\"w-4 h-4 text-white\" />\n          </div>\n        )}\n        <div className=\"hidden md:block text-left\">\n          <p className=\"text-sm font-medium text-gray-900 dark:text-white\">\n            {user.name}\n          </p>\n          <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n            {user.email}\n          </p>\n        </div>\n        <ChevronDown\n          className={`w-4 h-4 text-gray-500 transition-transform ${\n            isOpen ? \"rotate-180\" : \"\"\n          }`}\n        />\n      </button>\n\n      {/* Dropdown Menu */}\n      {isOpen && (\n        <>\n          {/* Backdrop */}\n          <div\n            className=\"fixed inset-0 z-10\"\n            onClick={() => setIsOpen(false)}\n          />\n\n          {/* Menu */}\n          <div className=\"absolute right-0 mt-2 w-64 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-20\">\n            {/* User Info */}\n            <div className=\"p-4 border-b border-gray-200 dark:border-gray-700\">\n              <div className=\"flex items-center space-x-3\">\n                {user.picture ? (\n                  <img\n                    src={user.picture}\n                    alt={user.name}\n                    className=\"w-12 h-12 rounded-full border-2 border-green-500\"\n                  />\n                ) : (\n                  <div className=\"w-12 h-12 bg-green-500 rounded-full flex items-center justify-center\">\n                    <User className=\"w-6 h-6 text-white\" />\n                  </div>\n                )}\n                <div className=\"flex-1 min-w-0\">\n                  <p className=\"text-sm font-medium text-gray-900 dark:text-white truncate\">\n                    {user.name}\n                  </p>\n                  <p className=\"text-xs text-gray-500 dark:text-gray-400 truncate\">\n                    {user.email}\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            {/* Menu Items */}\n            <div className=\"py-2\">\n              <button\n                onClick={handleLogout}\n                className=\"w-full flex items-center px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors\"\n              >\n                <LogOut className=\"w-4 h-4 mr-3\" />\n                Keluar\n              </button>\n            </div>\n          </div>\n        </>\n      )}\n    </div>\n  );\n};\n\nexport default UserProfile;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,IAAI,EAAEC,MAAM,EAAEC,WAAW,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEzD,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM;IAAEC,IAAI;IAAEC;EAAO,CAAC,GAAGX,OAAO,CAAC,CAAC;EAClC,MAAM,CAACY,MAAM,EAAEC,SAAS,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAE3C,IAAI,CAACW,IAAI,EAAE,OAAO,IAAI;EAEtB,MAAMI,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMH,MAAM,CAAC,CAAC;MACdE,SAAS,CAAC,KAAK,CAAC;IAClB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C;MACAF,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;EAED,oBACER,OAAA;IAAKY,SAAS,EAAC,UAAU;IAAAC,QAAA,gBAEvBb,OAAA;MACEc,OAAO,EAAEA,CAAA,KAAMN,SAAS,CAAC,CAACD,MAAM,CAAE;MAClCK,SAAS,EAAC,uGAAuG;MAAAC,QAAA,GAEhHR,IAAI,CAACU,OAAO,gBACXf,OAAA;QACEgB,GAAG,EAAEX,IAAI,CAACU,OAAQ;QAClBE,GAAG,EAAEZ,IAAI,CAACa,IAAK;QACfN,SAAS,EAAC;MAAgD;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC,gBAEFtB,OAAA;QAAKY,SAAS,EAAC,oEAAoE;QAAAC,QAAA,eACjFb,OAAA,CAACJ,IAAI;UAACgB,SAAS,EAAC;QAAoB;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CACN,eACDtB,OAAA;QAAKY,SAAS,EAAC,2BAA2B;QAAAC,QAAA,gBACxCb,OAAA;UAAGY,SAAS,EAAC,mDAAmD;UAAAC,QAAA,EAC7DR,IAAI,CAACa;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACJtB,OAAA;UAAGY,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EACpDR,IAAI,CAACkB;QAAK;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACNtB,OAAA,CAACF,WAAW;QACVc,SAAS,EAAE,8CACTL,MAAM,GAAG,YAAY,GAAG,EAAE;MACzB;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,EAGRf,MAAM,iBACLP,OAAA,CAAAE,SAAA;MAAAW,QAAA,gBAEEb,OAAA;QACEY,SAAS,EAAC,oBAAoB;QAC9BE,OAAO,EAAEA,CAAA,KAAMN,SAAS,CAAC,KAAK;MAAE;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,eAGFtB,OAAA;QAAKY,SAAS,EAAC,4HAA4H;QAAAC,QAAA,gBAEzIb,OAAA;UAAKY,SAAS,EAAC,mDAAmD;UAAAC,QAAA,eAChEb,OAAA;YAAKY,SAAS,EAAC,6BAA6B;YAAAC,QAAA,GACzCR,IAAI,CAACU,OAAO,gBACXf,OAAA;cACEgB,GAAG,EAAEX,IAAI,CAACU,OAAQ;cAClBE,GAAG,EAAEZ,IAAI,CAACa,IAAK;cACfN,SAAS,EAAC;YAAkD;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC,gBAEFtB,OAAA;cAAKY,SAAS,EAAC,sEAAsE;cAAAC,QAAA,eACnFb,OAAA,CAACJ,IAAI;gBAACgB,SAAS,EAAC;cAAoB;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CACN,eACDtB,OAAA;cAAKY,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7Bb,OAAA;gBAAGY,SAAS,EAAC,4DAA4D;gBAAAC,QAAA,EACtER,IAAI,CAACa;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACJtB,OAAA;gBAAGY,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,EAC7DR,IAAI,CAACkB;cAAK;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNtB,OAAA;UAAKY,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBb,OAAA;YACEc,OAAO,EAAEL,YAAa;YACtBG,SAAS,EAAC,sIAAsI;YAAAC,QAAA,gBAEhJb,OAAA,CAACH,MAAM;cAACe,SAAS,EAAC;YAAc;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,UAErC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA,eACN,CACH;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAClB,EAAA,CArGID,WAAW;EAAA,QACUR,OAAO;AAAA;AAAA6B,EAAA,GAD5BrB,WAAW;AAuGjB,eAAeA,WAAW;AAAC,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}