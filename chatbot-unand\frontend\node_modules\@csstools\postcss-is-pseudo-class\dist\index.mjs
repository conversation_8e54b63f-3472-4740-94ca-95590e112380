import e from"postcss-selector-parser";import{selectorSpecificity as o}from"@csstools/selector-specificity";function s(o,s){return e.isPseudoElement(o)?n.pseudoElement:n[s]}const n={universal:0,tag:1,pseudoElement:2,id:3,class:4,attribute:5,pseudo:6,selector:7,string:8,root:9,comment:10};function t(o,n,t,d){return o.flatMap((o=>{if(-1===o.indexOf(":-csstools-matches")&&-1===o.toLowerCase().indexOf(":is"))return o;const r=e().astSync(o);return r.walkPseudos((o=>{if(":is"===o.value.toLowerCase()&&o.nodes&&o.nodes.length&&"selector"===o.nodes[0].type&&0===o.nodes[0].nodes.length)return o.value=":not",void o.nodes[0].append(e.universal());if(":-csstools-matches"===o.value)if(!o.nodes||o.nodes.length){if(o.walkPseudos((o=>{if(e.isPseudoElement(o)){let e=o.value;if(e.startsWith("::-csstools-invalid-"))return;for(;e.startsWith(":");)e=e.slice(1);o.value=`::-csstools-invalid-${e}`,d()}})),1===o.nodes.length&&"selector"===o.nodes[0].type){if(1===o.nodes[0].nodes.length)return void o.replaceWith(o.nodes[0].nodes[0]);if(!o.nodes[0].some((e=>"combinator"===e.type)))return void o.replaceWith(...o.nodes[0].nodes)}1!==r.nodes.length||"selector"!==r.nodes[0].type||1!==r.nodes[0].nodes.length||r.nodes[0].nodes[0]!==o?function(e){return!(!e||!e.nodes||"selector"!==e.type||3!==e.nodes.length||!e.nodes[0]||"pseudo"!==e.nodes[0].type||":-csstools-matches"!==e.nodes[0].value||!e.nodes[1]||"combinator"!==e.nodes[1].type||"+"!==e.nodes[1].value||!e.nodes[2]||"pseudo"!==e.nodes[2].type||":-csstools-matches"!==e.nodes[2].value||!e.nodes[0].nodes||1!==e.nodes[0].nodes.length||"selector"!==e.nodes[0].nodes[0].type||!e.nodes[0].nodes[0].nodes||3!==e.nodes[0].nodes[0].nodes.length||!e.nodes[0].nodes[0].nodes||"combinator"!==e.nodes[0].nodes[0].nodes[1].type||">"!==e.nodes[0].nodes[0].nodes[1].value||!e.nodes[2].nodes||1!==e.nodes[2].nodes.length||"selector"!==e.nodes[2].nodes[0].type||!e.nodes[2].nodes[0].nodes||3!==e.nodes[2].nodes[0].nodes.length||!e.nodes[2].nodes[0].nodes||"combinator"!==e.nodes[2].nodes[0].nodes[1].type||">"!==e.nodes[2].nodes[0].nodes[1].value||(e.nodes[0].nodes[0].insertAfter(e.nodes[0].nodes[0].nodes[0],e.nodes[2].nodes[0].nodes[0].clone()),e.nodes[2].nodes[0].nodes[1].remove(),e.nodes[2].nodes[0].nodes[0].remove(),e.nodes[0].replaceWith(e.nodes[0].nodes[0]),e.nodes[2].replaceWith(e.nodes[2].nodes[0]),0))}(o.parent)||function(o){if(!o||!o.nodes)return!1;if("selector"!==o.type)return!1;if(2!==o.nodes.length)return!1;let s,n;return o.nodes[0]&&"pseudo"===o.nodes[0].type&&":-csstools-matches"===o.nodes[0].value?(s=0,n=1):o.nodes[1]&&"pseudo"===o.nodes[1].type&&":-csstools-matches"===o.nodes[1].value&&(s=1,n=0),!(!s||!o.nodes[n]||"selector"===o.nodes[n].type&&o.nodes[n].some((o=>"combinator"===o.type||e.isPseudoElement(o)))||(o.nodes[s].append(o.nodes[n].clone()),o.nodes[s].replaceWith(...o.nodes[s].nodes),o.nodes[n].remove(),0))}(o.parent)||("warning"===n.onComplexSelector&&t(),o.value=":is"):o.replaceWith(...o.nodes[0].nodes)}else o.remove()})),r.walk((e=>{"selector"===e.type&&"nodes"in e&&1===e.nodes.length&&"selector"===e.nodes[0].type&&e.replaceWith(e.nodes[0])})),r.walk((o=>{"nodes"in o&&function(o){if(!o||!o.nodes)return;const n=[];let t=[];for(let s=0;s<o.nodes.length;s++)"combinator"!==o.nodes[s].type?e.isPseudoElement(o.nodes[s])?(n.push(t),t=[o.nodes[s]]):t.push(o.nodes[s]):(n.push(t),n.push([o.nodes[s]]),t=[]);n.push(t);const d=[];for(let e=0;e<n.length;e++){const o=n[e];o.sort(((e,o)=>"selector"===e.type&&"selector"===o.type&&e.nodes.length&&o.nodes.length?s(e.nodes[0],e.nodes[0].type)-s(o.nodes[0],o.nodes[0].type):"selector"===e.type&&e.nodes.length?s(e.nodes[0],e.nodes[0].type)-s(o,o.type):"selector"===o.type&&o.nodes.length?s(e,e.type)-s(o.nodes[0],o.nodes[0].type):s(e,e.type)-s(o,o.type)));for(let e=0;e<o.length;e++)d.push(o[e])}for(let e=d.length-1;e>=0;e--)d[e].remove(),o.prepend(d[e])}(o)})),r.toString()})).filter((e=>!!e))}function d(s,n,t=0){const r=":not(#"+n.specificityMatchingName+")",l=":not(."+n.specificityMatchingName+")",c=":not("+n.specificityMatchingName+")";return s.flatMap((s=>{if(-1===s.toLowerCase().indexOf(":is"))return s;let i=!1;const a=[];if(e().astSync(s).walkPseudos((e=>{if(":is"!==e.value.toLowerCase()||!e.nodes||!e.nodes.length)return;if("selector"===e.nodes[0].type&&0===e.nodes[0].nodes.length)return;let s=e.parent;for(;s;){if(s.value&&":is"===s.value.toLowerCase()&&"pseudo"===s.type)return void(i=!0);s=s.parent}const n=o(e),t=e.sourceIndex,d=t+e.toString().length,p=[];e.nodes.forEach((e=>{const s={start:t,end:d,option:""},i=o(e);let a=e.toString().trim();const u=Math.max(0,n.a-i.a),h=Math.max(0,n.b-i.b),f=Math.max(0,n.c-i.c);for(let e=0;e<u;e++)a+=r;for(let e=0;e<h;e++)a+=l;for(let e=0;e<f;e++)a+=c;s.option=a,p.push(s)})),a.push(p)})),!a.length)return[s];let p=[];return function(...e){const o=[],s=e.length-1;function n(t,d){for(let r=0,l=e[d].length;r<l;r++){const l=t.slice(0);l.push(e[d][r]),d==s?o.push(l):n(l,d+1)}}return n([],0),o}(...a).forEach((e=>{let o="";for(let t=0;t<e.length;t++){var n;const d=e[t];o+=s.substring((null==(n=e[t-1])?void 0:n.end)||0,e[t].start),o+=":-csstools-matches("+d.option+")",t===e.length-1&&(o+=s.substring(e[t].end))}p.push(o)})),i&&t<10&&(p=d(p,n,t+1)),p})).filter((e=>!!e))}const r=e=>{const o={specificityMatchingName:"does-not-exist",...e||{}};return{postcssPlugin:"postcss-is-pseudo-class",Rule(e,{result:s}){if(!e.selector)return;if(-1===e.selector.toLowerCase().indexOf(":is"))return;let n=!1;const r=()=>{"warning"===o.onComplexSelector&&(n||(n=!0,e.warn(s,`Complex selectors in '${e.selector}' can not be transformed to an equivalent selector without ':is()'.`)))};let l=!1;const c=()=>{"warning"===o.onPseudoElement&&(l||(l=!0,e.warn(s,`Pseudo elements are not allowed in ':is()', unable to transform '${e.selector}'`)))};try{let s=!1;const n=[],l=t(d(e.selectors,{specificityMatchingName:o.specificityMatchingName}),{onComplexSelector:o.onComplexSelector},r,c);if(Array.from(new Set(l)).forEach((o=>{e.selectors.indexOf(o)>-1?n.push(o):(e.cloneBefore({selector:o}),s=!0)})),n.length&&s&&e.cloneBefore({selectors:n}),!o.preserve){if(!s)return;e.remove()}}catch(o){if(o.message.indexOf("call stack size exceeded")>-1)throw o;e.warn(s,`Failed to parse selector "${e.selector}"`)}}}};r.postcss=!0;export{r as default};
