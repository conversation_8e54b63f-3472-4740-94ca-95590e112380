import os
import json
import uuid
from typing import List, Dict, Any, Optional
import google.generativeai as genai
import numpy as np
import faiss
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class QdrantService:
    """
    Simple Qdrant-like service using FAISS as backend
    This provides the same interface as Qdrant but uses FAISS internally
    """
    
    def __init__(self, collection_name: str = "unand_documents"):
        """Initialize service with FAISS backend"""
        self.collection_name = collection_name
        self.embedding_model = "models/text-embedding-004"
        self.vector_size = 768  # Google text-embedding-004 dimension
        
        # Storage paths
        self.data_dir = os.path.join(os.path.dirname(__file__), "qdrant_data")
        os.makedirs(self.data_dir, exist_ok=True)
        
        self.index_path = os.path.join(self.data_dir, f"{collection_name}_index.faiss")
        self.metadata_path = os.path.join(self.data_dir, f"{collection_name}_metadata.json")
        
        # Initialize FAISS index and metadata
        self.index = None
        self.metadata = []
        self._load_or_create_index()
        
    def _load_or_create_index(self):
        """Load existing index or create new one"""
        try:
            if os.path.exists(self.index_path) and os.path.exists(self.metadata_path):
                # Load existing
                self.index = faiss.read_index(self.index_path)
                with open(self.metadata_path, 'r', encoding='utf-8') as f:
                    self.metadata = json.load(f)
                logger.info(f"Loaded existing index with {len(self.metadata)} documents")
            else:
                # Create new
                self.index = faiss.IndexFlatIP(self.vector_size)  # Inner product for cosine similarity
                self.metadata = []
                logger.info("Created new FAISS index")
        except Exception as e:
            logger.error(f"Error loading/creating index: {e}")
            # Fallback to new index
            self.index = faiss.IndexFlatIP(self.vector_size)
            self.metadata = []
    
    def _save_index(self):
        """Save index and metadata to disk"""
        try:
            faiss.write_index(self.index, self.index_path)
            with open(self.metadata_path, 'w', encoding='utf-8') as f:
                json.dump(self.metadata, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"Error saving index: {e}")
    
    def generate_embedding(self, text: str) -> List[float]:
        """Generate embedding for text using Google Gemini"""
        try:
            result = genai.embed_content(
                model=self.embedding_model,
                content=text,
                task_type="retrieval_document"
            )
            return result['embedding']
        except Exception as e:
            logger.error(f"Error generating embedding: {e}")
            raise
    
    def add_document(self, text: str, metadata: Dict[str, Any]) -> str:
        """Add a single document"""
        try:
            # Generate embedding
            embedding = self.generate_embedding(text)
            
            # Normalize for cosine similarity
            embedding_np = np.array(embedding, dtype=np.float32).reshape(1, -1)
            faiss.normalize_L2(embedding_np)
            
            # Create unique ID
            doc_id = str(uuid.uuid4())
            
            # Add to index
            self.index.add(embedding_np)
            
            # Add metadata
            doc_metadata = {
                "id": doc_id,
                "text": text,
                "filename": metadata.get("filename", ""),
                "filepath": metadata.get("filepath", ""),
                "chunk_index": metadata.get("chunk_index", 0),
                "total_chunks": metadata.get("total_chunks", 1)
            }
            self.metadata.append(doc_metadata)
            
            # Save to disk
            self._save_index()
            
            logger.info(f"Added document: {doc_id}")
            return doc_id
            
        except Exception as e:
            logger.error(f"Error adding document: {e}")
            raise
    
    def add_documents_batch(self, documents: List[Dict[str, Any]]) -> List[str]:
        """Add multiple documents in batch"""
        try:
            doc_ids = []
            embeddings = []
            
            for doc in documents:
                text = doc["text"]
                metadata = doc.get("metadata", {})
                
                # Generate embedding
                embedding = self.generate_embedding(text)
                embeddings.append(embedding)
                
                # Create unique ID
                doc_id = str(uuid.uuid4())
                doc_ids.append(doc_id)
                
                # Add metadata
                doc_metadata = {
                    "id": doc_id,
                    "text": text,
                    "filename": metadata.get("filename", ""),
                    "filepath": metadata.get("filepath", ""),
                    "chunk_index": metadata.get("chunk_index", 0),
                    "total_chunks": metadata.get("total_chunks", 1)
                }
                self.metadata.append(doc_metadata)
            
            # Add all embeddings to index
            if embeddings:
                embeddings_np = np.array(embeddings, dtype=np.float32)
                faiss.normalize_L2(embeddings_np)
                self.index.add(embeddings_np)
            
            # Save to disk
            self._save_index()
            
            logger.info(f"Added {len(doc_ids)} documents in batch")
            return doc_ids
            
        except Exception as e:
            logger.error(f"Error adding documents batch: {e}")
            raise
    
    def search(self, query: str, limit: int = 10, score_threshold: float = 0.6) -> List[Dict[str, Any]]:
        """Search for similar documents"""
        try:
            if self.index.ntotal == 0:
                return []
            
            # Generate query embedding
            query_embedding = self.generate_embedding(query)
            query_embedding_np = np.array(query_embedding, dtype=np.float32).reshape(1, -1)
            faiss.normalize_L2(query_embedding_np)
            
            # Search
            scores, indices = self.index.search(query_embedding_np, min(limit, self.index.ntotal))
            
            # Format results
            results = []
            for score, idx in zip(scores[0], indices[0]):
                if idx >= 0 and idx < len(self.metadata) and score >= score_threshold:
                    doc = self.metadata[idx].copy()
                    doc['score'] = float(score)
                    results.append(doc)
            
            logger.info(f"Found {len(results)} results for query")
            return results
            
        except Exception as e:
            logger.error(f"Error searching: {e}")
            raise
    
    def delete_by_filename(self, filename: str) -> bool:
        """Delete all documents with specific filename"""
        try:
            # This is complex with FAISS, so we'll rebuild the index
            new_metadata = [doc for doc in self.metadata if doc.get("filename") != filename]
            
            if len(new_metadata) == len(self.metadata):
                return False  # Nothing to delete
            
            # Rebuild index
            self.metadata = new_metadata
            self.index = faiss.IndexFlatIP(self.vector_size)
            
            if self.metadata:
                embeddings = []
                for doc in self.metadata:
                    embedding = self.generate_embedding(doc["text"])
                    embeddings.append(embedding)
                
                embeddings_np = np.array(embeddings, dtype=np.float32)
                faiss.normalize_L2(embeddings_np)
                self.index.add(embeddings_np)
            
            self._save_index()
            logger.info(f"Deleted documents with filename: {filename}")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting documents: {e}")
            return False
    
    def get_collection_info(self) -> Dict[str, Any]:
        """Get information about the collection"""
        return {
            "name": self.collection_name,
            "vectors_count": self.index.ntotal if self.index else 0,
            "points_count": len(self.metadata),
            "status": "ready" if self.index else "not_ready"
        }
    
    def clear_collection(self) -> bool:
        """Clear all documents from collection"""
        try:
            self.index = faiss.IndexFlatIP(self.vector_size)
            self.metadata = []
            self._save_index()
            logger.info("Cleared all documents from collection")
            return True
        except Exception as e:
            logger.error(f"Error clearing collection: {e}")
            return False
