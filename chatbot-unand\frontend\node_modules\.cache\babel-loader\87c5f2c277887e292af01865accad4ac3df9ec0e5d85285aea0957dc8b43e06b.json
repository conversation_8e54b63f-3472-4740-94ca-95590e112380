{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"13\",\n  r: \"8\",\n  key: \"3y4lt7\"\n}], [\"path\", {\n  d: \"M5 3 2 6\",\n  key: \"18tl5t\"\n}], [\"path\", {\n  d: \"m22 6-3-3\",\n  key: \"1opdir\"\n}], [\"path\", {\n  d: \"M6.38 18.7 4 21\",\n  key: \"17xu3x\"\n}], [\"path\", {\n  d: \"M17.64 18.67 20 21\",\n  key: \"kv2oe2\"\n}], [\"path\", {\n  d: \"M12 10v6\",\n  key: \"1bos4e\"\n}], [\"path\", {\n  d: \"M9 13h6\",\n  key: \"1uhe8q\"\n}]];\nconst AlarmClockPlus = createLucideIcon(\"alarm-clock-plus\", __iconNode);\nexport { __iconNode, AlarmClockPlus as default };", "map": {"version": 3, "names": ["__iconNode", "cx", "cy", "r", "key", "d", "AlarmClockPlus", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\node_modules\\lucide-react\\src\\icons\\alarm-clock-plus.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '13', r: '8', key: '3y4lt7' }],\n  ['path', { d: 'M5 3 2 6', key: '18tl5t' }],\n  ['path', { d: 'm22 6-3-3', key: '1opdir' }],\n  ['path', { d: 'M6.38 18.7 4 21', key: '17xu3x' }],\n  ['path', { d: 'M17.64 18.67 20 21', key: 'kv2oe2' }],\n  ['path', { d: 'M12 10v6', key: '1bos4e' }],\n  ['path', { d: 'M9 13h6', key: '1uhe8q' }],\n];\n\n/**\n * @component @name AlarmClockPlus\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEzIiByPSI4IiAvPgogIDxwYXRoIGQ9Ik01IDMgMiA2IiAvPgogIDxwYXRoIGQ9Im0yMiA2LTMtMyIgLz4KICA8cGF0aCBkPSJNNi4zOCAxOC43IDQgMjEiIC8+CiAgPHBhdGggZD0iTTE3LjY0IDE4LjY3IDIwIDIxIiAvPgogIDxwYXRoIGQ9Ik0xMiAxMHY2IiAvPgogIDxwYXRoIGQ9Ik05IDEzaDYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/alarm-clock-plus\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst AlarmClockPlus = createLucideIcon('alarm-clock-plus', __iconNode);\n\nexport default AlarmClockPlus;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAD,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAEC,CAAA,EAAG,iBAAmB;EAAAD,GAAA,EAAK;AAAA,CAAU,GAChD,CAAC,MAAQ;EAAEC,CAAA,EAAG,oBAAsB;EAAAD,GAAA,EAAK;AAAA,CAAU,GACnD,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAD,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAD,GAAA,EAAK;AAAU,GAC1C;AAaM,MAAAE,cAAA,GAAiBC,gBAAiB,qBAAoBP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}