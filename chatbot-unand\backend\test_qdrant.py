#!/usr/bin/env python3
"""
Simple test script for Qdrant service
"""

import os
import sys
from dotenv import load_dotenv
import google.generativeai as genai

# Load environment variables
load_dotenv()

# Configure Gemini
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
if not GEMINI_API_KEY:
    print("❌ GEMINI_API_KEY not found in environment variables")
    sys.exit(1)

genai.configure(api_key=GEMINI_API_KEY)

try:
    print("🔧 Testing Qdrant service...")
    from qdrant_service_simple import QdrantService
    
    print("✅ QdrantService imported successfully")
    
    # Initialize service
    qs = QdrantService()
    print("✅ QdrantService initialized successfully")
    
    # Get collection info
    info = qs.get_collection_info()
    print(f"📊 Collection info: {info}")
    
    # Test adding a document
    test_doc = {
        "text": "Test document for Qdrant",
        "metadata": {
            "filename": "test.txt",
            "filepath": "/test/test.txt",
            "chunk_index": 0,
            "total_chunks": 1
        }
    }
    
    print("📝 Adding test document...")
    doc_id = qs.add_document(test_doc["text"], test_doc["metadata"])
    print(f"✅ Document added with ID: {doc_id}")
    
    # Test search
    print("🔍 Testing search...")
    results = qs.search("test document", limit=5)
    print(f"✅ Search completed. Found {len(results)} results")
    
    for i, result in enumerate(results):
        print(f"   {i+1}. Score: {result['score']:.3f}, Text: {result['text'][:50]}...")
    
    print("\n🎉 All tests passed!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
