{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website kp\\\\chatbot-unand\\\\frontend\\\\src\\\\ChatInput.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef } from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ChatInput = ({\n  onSendMessage,\n  isLoading,\n  onFileUpload\n}) => {\n  _s();\n  const [message, setMessage] = useState(\"\");\n  const fileInputRef = useRef(null);\n  const handleSubmit = e => {\n    e.preventDefault();\n    if (message.trim() && !isLoading) {\n      onSendMessage(message);\n      setMessage(\"\");\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"form\", {\n    onSubmit: handleSubmit,\n    className: \"flex p-4 border-t-2 border-green-600 bg-gradient-to-r from-green-100 to-yellow-100\",\n    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n      type: \"text\",\n      className: \"flex-grow rounded-full py-3 px-4 mr-3 bg-white border-2 border-green-300 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 shadow-md\",\n      placeholder: \"Tulis pesan Anda...\",\n      value: message,\n      onChange: e => setMessage(e.target.value),\n      disabled: isLoading\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      type: \"submit\",\n      className: \"bg-gradient-to-r from-green-600 to-green-700 text-white rounded-full p-3 hover:from-green-700 hover:to-green-800 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50 shadow-md hover:shadow-lg transition-all duration-200\",\n      disabled: isLoading,\n      children: isLoading ? /*#__PURE__*/_jsxDEV(\"svg\", {\n        className: \"animate-spin h-5 w-5 text-white\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n          className: \"opacity-25\",\n          cx: \"12\",\n          cy: \"12\",\n          r: \"10\",\n          stroke: \"currentColor\",\n          strokeWidth: \"4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n          className: \"opacity-75\",\n          fill: \"currentColor\",\n          d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        className: \"h-5 w-5\",\n        viewBox: \"0 0 20 20\",\n        fill: \"currentColor\",\n        children: /*#__PURE__*/_jsxDEV(\"path\", {\n          d: \"M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l4.453-1.483a1 1 0 00.67-.099l2.258 2.258a1 1 0 001.414 0l2.258-2.258a1 1 0 00.67.099l4.453 1.483a1 1 0 001.169-1.409l-7-14z\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 16,\n    columnNumber: 5\n  }, this);\n};\n_s(ChatInput, \"NsWI/hgCQtIQrekRItai4l5FBnw=\");\n_c = ChatInput;\nexport default ChatInput;\nvar _c;\n$RefreshReg$(_c, \"ChatInput\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "jsxDEV", "_jsxDEV", "ChatInput", "onSendMessage", "isLoading", "onFileUpload", "_s", "message", "setMessage", "fileInputRef", "handleSubmit", "e", "preventDefault", "trim", "onSubmit", "className", "children", "type", "placeholder", "value", "onChange", "target", "disabled", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "xmlns", "fill", "viewBox", "cx", "cy", "r", "stroke", "strokeWidth", "d", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/website kp/chatbot-unand/frontend/src/ChatInput.js"], "sourcesContent": ["import React, { useState, useRef } from \"react\";\n\nconst ChatInput = ({ onSendMessage, isLoading, onFileUpload }) => {\n  const [message, setMessage] = useState(\"\");\n  const fileInputRef = useRef(null);\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    if (message.trim() && !isLoading) {\n      onSendMessage(message);\n      setMessage(\"\");\n    }\n  };\n\n  return (\n    <form\n      onSubmit={handleSubmit}\n      className=\"flex p-4 border-t-2 border-green-600 bg-gradient-to-r from-green-100 to-yellow-100\"\n    >\n      <input\n        type=\"text\"\n        className=\"flex-grow rounded-full py-3 px-4 mr-3 bg-white border-2 border-green-300 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 shadow-md\"\n        placeholder=\"Tulis pesan Anda...\"\n        value={message}\n        onChange={(e) => setMessage(e.target.value)}\n        disabled={isLoading}\n      />\n      <button\n        type=\"submit\"\n        className=\"bg-gradient-to-r from-green-600 to-green-700 text-white rounded-full p-3 hover:from-green-700 hover:to-green-800 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50 shadow-md hover:shadow-lg transition-all duration-200\"\n        disabled={isLoading}\n      >\n        {isLoading ? (\n          <svg\n            className=\"animate-spin h-5 w-5 text-white\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            ></circle>\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            ></path>\n          </svg>\n        ) : (\n          <svg\n            xmlns=\"http://www.w3.org/2000/svg\"\n            className=\"h-5 w-5\"\n            viewBox=\"0 0 20 20\"\n            fill=\"currentColor\"\n          >\n            <path d=\"M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l4.453-1.483a1 1 0 00.67-.099l2.258 2.258a1 1 0 001.414 0l2.258-2.258a1 1 0 00.67.099l4.453 1.483a1 1 0 001.169-1.409l-7-14z\" />\n          </svg>\n        )}\n      </button>\n    </form>\n  );\n};\n\nexport default ChatInput;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,SAAS,GAAGA,CAAC;EAAEC,aAAa;EAAEC,SAAS;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EAChE,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAMW,YAAY,GAAGV,MAAM,CAAC,IAAI,CAAC;EAEjC,MAAMW,YAAY,GAAIC,CAAC,IAAK;IAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAIL,OAAO,CAACM,IAAI,CAAC,CAAC,IAAI,CAACT,SAAS,EAAE;MAChCD,aAAa,CAACI,OAAO,CAAC;MACtBC,UAAU,CAAC,EAAE,CAAC;IAChB;EACF,CAAC;EAED,oBACEP,OAAA;IACEa,QAAQ,EAAEJ,YAAa;IACvBK,SAAS,EAAC,oFAAoF;IAAAC,QAAA,gBAE9Ff,OAAA;MACEgB,IAAI,EAAC,MAAM;MACXF,SAAS,EAAC,gKAAgK;MAC1KG,WAAW,EAAC,qBAAqB;MACjCC,KAAK,EAAEZ,OAAQ;MACfa,QAAQ,EAAGT,CAAC,IAAKH,UAAU,CAACG,CAAC,CAACU,MAAM,CAACF,KAAK,CAAE;MAC5CG,QAAQ,EAAElB;IAAU;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB,CAAC,eACFzB,OAAA;MACEgB,IAAI,EAAC,QAAQ;MACbF,SAAS,EAAC,iPAAiP;MAC3PO,QAAQ,EAAElB,SAAU;MAAAY,QAAA,EAEnBZ,SAAS,gBACRH,OAAA;QACEc,SAAS,EAAC,iCAAiC;QAC3CY,KAAK,EAAC,4BAA4B;QAClCC,IAAI,EAAC,MAAM;QACXC,OAAO,EAAC,WAAW;QAAAb,QAAA,gBAEnBf,OAAA;UACEc,SAAS,EAAC,YAAY;UACtBe,EAAE,EAAC,IAAI;UACPC,EAAE,EAAC,IAAI;UACPC,CAAC,EAAC,IAAI;UACNC,MAAM,EAAC,cAAc;UACrBC,WAAW,EAAC;QAAG;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eACVzB,OAAA;UACEc,SAAS,EAAC,YAAY;UACtBa,IAAI,EAAC,cAAc;UACnBO,CAAC,EAAC;QAAiH;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9G,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,gBAENzB,OAAA;QACE0B,KAAK,EAAC,4BAA4B;QAClCZ,SAAS,EAAC,SAAS;QACnBc,OAAO,EAAC,WAAW;QACnBD,IAAI,EAAC,cAAc;QAAAZ,QAAA,eAEnBf,OAAA;UAAMkC,CAAC,EAAC;QAAsL;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9L;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEX,CAAC;AAACpB,EAAA,CAhEIJ,SAAS;AAAAkC,EAAA,GAATlC,SAAS;AAkEf,eAAeA,SAAS;AAAC,IAAAkC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}