{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website kp\\\\chatbot-unand\\\\frontend\\\\src\\\\ChatInput.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ChatInput = ({\n  onSendMessage,\n  isLoading\n}) => {\n  _s();\n  const [message, setMessage] = useState(\"\");\n  const handleSubmit = e => {\n    e.preventDefault();\n    if (message.trim() && !isLoading) {\n      onSendMessage(message);\n      setMessage(\"\");\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"form\", {\n    onSubmit: handleSubmit,\n    className: \"flex p-4 border-t border-gray-300\",\n    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n      type: \"text\",\n      className: \"flex-grow rounded-full py-2 px-4 mr-2 bg-gray-100 border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500\",\n      placeholder: \"Tulis pesan Anda...\",\n      value: message,\n      onChange: e => setMessage(e.target.value),\n      disabled: isLoading\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      type: \"submit\",\n      className: \"bg-blue-600 text-white rounded-full p-3 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50\",\n      disabled: isLoading,\n      children: isLoading ? /*#__PURE__*/_jsxDEV(\"svg\", {\n        className: \"animate-spin h-5 w-5 text-white\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n          className: \"opacity-25\",\n          cx: \"12\",\n          cy: \"12\",\n          r: \"10\",\n          stroke: \"currentColor\",\n          strokeWidth: \"4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n          className: \"opacity-75\",\n          fill: \"currentColor\",\n          d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        className: \"h-5 w-5\",\n        viewBox: \"0 0 20 20\",\n        fill: \"currentColor\",\n        children: /*#__PURE__*/_jsxDEV(\"path\", {\n          d: \"M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l4.453-1.483a1 1 0 00.67-.099l2.258 2.258a1 1 0 001.414 0l2.258-2.258a1 1 0 00.67.099l4.453 1.483a1 1 0 001.169-1.409l-7-14z\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 5\n  }, this);\n};\n_s(ChatInput, \"EiOGSxO4GWQlH0sM782nQ9JwuAs=\");\n_c = ChatInput;\nexport default ChatInput;\nvar _c;\n$RefreshReg$(_c, \"ChatInput\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "ChatInput", "onSendMessage", "isLoading", "_s", "message", "setMessage", "handleSubmit", "e", "preventDefault", "trim", "onSubmit", "className", "children", "type", "placeholder", "value", "onChange", "target", "disabled", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "xmlns", "fill", "viewBox", "cx", "cy", "r", "stroke", "strokeWidth", "d", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/website kp/chatbot-unand/frontend/src/ChatInput.js"], "sourcesContent": ["import React, { useState } from \"react\";\n\nconst ChatInput = ({ onSendMessage, isLoading }) => {\n  const [message, setMessage] = useState(\"\");\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    if (message.trim() && !isLoading) {\n      onSendMessage(message);\n      setMessage(\"\");\n    }\n  };\n\n  return (\n    <form onSubmit={handleSubmit} className=\"flex p-4 border-t border-gray-300\">\n      <input\n        type=\"text\"\n        className=\"flex-grow rounded-full py-2 px-4 mr-2 bg-gray-100 border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n        placeholder=\"Tulis pesan Anda...\"\n        value={message}\n        onChange={(e) => setMessage(e.target.value)}\n        disabled={isLoading}\n      />\n      <button\n        type=\"submit\"\n        className=\"bg-blue-600 text-white rounded-full p-3 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50\"\n        disabled={isLoading}\n      >\n        {isLoading ? (\n          <svg\n            className=\"animate-spin h-5 w-5 text-white\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            ></circle>\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            ></path>\n          </svg>\n        ) : (\n          <svg\n            xmlns=\"http://www.w3.org/2000/svg\"\n            className=\"h-5 w-5\"\n            viewBox=\"0 0 20 20\"\n            fill=\"currentColor\"\n          >\n            <path d=\"M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l4.453-1.483a1 1 0 00.67-.099l2.258 2.258a1 1 0 001.414 0l2.258-2.258a1 1 0 00.67.099l4.453 1.483a1 1 0 001.169-1.409l-7-14z\" />\n          </svg>\n        )}\n      </button>\n    </form>\n  );\n};\n\nexport default ChatInput;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,SAAS,GAAGA,CAAC;EAAEC,aAAa;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EAClD,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EAE1C,MAAMS,YAAY,GAAIC,CAAC,IAAK;IAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAIJ,OAAO,CAACK,IAAI,CAAC,CAAC,IAAI,CAACP,SAAS,EAAE;MAChCD,aAAa,CAACG,OAAO,CAAC;MACtBC,UAAU,CAAC,EAAE,CAAC;IAChB;EACF,CAAC;EAED,oBACEN,OAAA;IAAMW,QAAQ,EAAEJ,YAAa;IAACK,SAAS,EAAC,mCAAmC;IAAAC,QAAA,gBACzEb,OAAA;MACEc,IAAI,EAAC,MAAM;MACXF,SAAS,EAAC,8HAA8H;MACxIG,WAAW,EAAC,qBAAqB;MACjCC,KAAK,EAAEX,OAAQ;MACfY,QAAQ,EAAGT,CAAC,IAAKF,UAAU,CAACE,CAAC,CAACU,MAAM,CAACF,KAAK,CAAE;MAC5CG,QAAQ,EAAEhB;IAAU;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB,CAAC,eACFvB,OAAA;MACEc,IAAI,EAAC,QAAQ;MACbF,SAAS,EAAC,mIAAmI;MAC7IO,QAAQ,EAAEhB,SAAU;MAAAU,QAAA,EAEnBV,SAAS,gBACRH,OAAA;QACEY,SAAS,EAAC,iCAAiC;QAC3CY,KAAK,EAAC,4BAA4B;QAClCC,IAAI,EAAC,MAAM;QACXC,OAAO,EAAC,WAAW;QAAAb,QAAA,gBAEnBb,OAAA;UACEY,SAAS,EAAC,YAAY;UACtBe,EAAE,EAAC,IAAI;UACPC,EAAE,EAAC,IAAI;UACPC,CAAC,EAAC,IAAI;UACNC,MAAM,EAAC,cAAc;UACrBC,WAAW,EAAC;QAAG;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eACVvB,OAAA;UACEY,SAAS,EAAC,YAAY;UACtBa,IAAI,EAAC,cAAc;UACnBO,CAAC,EAAC;QAAiH;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9G,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,gBAENvB,OAAA;QACEwB,KAAK,EAAC,4BAA4B;QAClCZ,SAAS,EAAC,SAAS;QACnBc,OAAO,EAAC,WAAW;QACnBD,IAAI,EAAC,cAAc;QAAAZ,QAAA,eAEnBb,OAAA;UAAMgC,CAAC,EAAC;QAAsL;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9L;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEX,CAAC;AAACnB,EAAA,CA5DIH,SAAS;AAAAgC,EAAA,GAAThC,SAAS;AA8Df,eAAeA,SAAS;AAAC,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}