[{"D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\src\\index.js": "1", "D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\src\\App.jsx": "2", "D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\src\\api.js": "3", "D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\src\\ChatSidebar.jsx": "4", "D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\src\\TelegramButton.jsx": "5", "D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\src\\components\\AdminLogin.jsx": "6", "D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\src\\components\\AdminUpload.jsx": "7", "D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\src\\components\\AdminDashboard.jsx": "8", "D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\src\\components\\Login.jsx": "9", "D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\src\\components\\ThemeToggle.jsx": "10", "D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\src\\contexts\\ThemeContext.jsx": "11", "D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\src\\components\\SessionGuard.jsx": "12", "D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\src\\contexts\\AuthContext.jsx": "13", "D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\src\\ChatInput.jsx": "14", "D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\src\\components\\UserProfile.jsx": "15", "D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\src\\Message.jsx": "16", "D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\src\\components\\AdminLayout.jsx": "17", "D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\src\\components\\ui\\switch.jsx": "18", "D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\src\\components\\ui\\accordion.jsx": "19", "D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\src\\lib\\utils.js": "20", "D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\src\\pages\\ChatWindow.jsx": "21"}, {"size": 254, "mtime": 1751292250750, "results": "22", "hashOfConfig": "23"}, {"size": 9951, "mtime": 1758180386387, "results": "24", "hashOfConfig": "23"}, {"size": 4983, "mtime": 1751292250730, "results": "25", "hashOfConfig": "23"}, {"size": 13560, "mtime": 1758219002618, "results": "26", "hashOfConfig": "23"}, {"size": 6075, "mtime": 1758180386473, "results": "27", "hashOfConfig": "23"}, {"size": 6460, "mtime": 1751292250768, "results": "28", "hashOfConfig": "23"}, {"size": 12317, "mtime": 1751292250772, "results": "29", "hashOfConfig": "23"}, {"size": 12380, "mtime": 1751292250763, "results": "30", "hashOfConfig": "23"}, {"size": 7246, "mtime": 1751292250775, "results": "31", "hashOfConfig": "23"}, {"size": 770, "mtime": 1758218587628, "results": "32", "hashOfConfig": "23"}, {"size": 1342, "mtime": 1751292250803, "results": "33", "hashOfConfig": "23"}, {"size": 2652, "mtime": 1751292250777, "results": "34", "hashOfConfig": "23"}, {"size": 15777, "mtime": 1751292250801, "results": "35", "hashOfConfig": "23"}, {"size": 2752, "mtime": 1758182283406, "results": "36", "hashOfConfig": "23"}, {"size": 3680, "mtime": 1751292250784, "results": "37", "hashOfConfig": "23"}, {"size": 11277, "mtime": 1751292250752, "results": "38", "hashOfConfig": "23"}, {"size": 6277, "mtime": 1751292250766, "results": "39", "hashOfConfig": "23"}, {"size": 1057, "mtime": 1751292250793, "results": "40", "hashOfConfig": "23"}, {"size": 1724, "mtime": 1751292250790, "results": "41", "hashOfConfig": "23"}, {"size": 135, "mtime": 1751292250810, "results": "42", "hashOfConfig": "23"}, {"size": 5149, "mtime": 1758218438494, "results": "43", "hashOfConfig": "23"}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1mhs8se", {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\src\\index.js", [], [], "D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\src\\App.jsx", [], [], "D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\src\\api.js", [], [], "D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\src\\ChatSidebar.jsx", [], [], "D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\src\\TelegramButton.jsx", [], [], "D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\src\\components\\AdminLogin.jsx", [], [], "D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\src\\components\\AdminUpload.jsx", [], [], "D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\src\\components\\AdminDashboard.jsx", [], [], "D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\src\\components\\Login.jsx", [], ["107"], "D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\src\\components\\ThemeToggle.jsx", [], [], "D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\src\\contexts\\ThemeContext.jsx", [], [], "D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\src\\components\\SessionGuard.jsx", [], [], "D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\src\\contexts\\AuthContext.jsx", [], [], "D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\src\\ChatInput.jsx", [], [], "D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\src\\components\\UserProfile.jsx", [], [], "D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\src\\Message.jsx", [], [], "D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\src\\components\\AdminLayout.jsx", [], [], "D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\src\\components\\ui\\switch.jsx", [], [], "D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\src\\components\\ui\\accordion.jsx", [], [], "D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\src\\lib\\utils.js", [], [], "D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\src\\pages\\ChatWindow.jsx", [], [], {"ruleId": "108", "severity": 1, "message": "109", "line": 145, "column": 6, "nodeType": "110", "endLine": 145, "endColumn": 8, "suggestions": "111", "suppressions": "112"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'initializeGoogleSignIn'. Either include it or remove the dependency array.", "ArrayExpression", ["113"], ["114"], {"desc": "115", "fix": "116"}, {"kind": "117", "justification": "118"}, "Update the dependencies array to be: [initializeGoogleSignIn]", {"range": "119", "text": "120"}, "directive", "", [4963, 4965], "[initializeGoogleSignIn]"]