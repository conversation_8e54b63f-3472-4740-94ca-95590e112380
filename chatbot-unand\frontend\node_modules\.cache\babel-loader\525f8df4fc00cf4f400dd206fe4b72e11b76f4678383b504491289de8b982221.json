{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\website kp\\\\chatbot-unand\\\\frontend\\\\src\\\\components\\\\Login.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Login = () => {\n  _s();\n  const {\n    login\n  } = useAuth();\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const handleGoogleLogin = async () => {\n    setLoading(true);\n    setError(null);\n    try {\n      // Load Google Identity Services\n      if (!window.google) {\n        throw new Error('Google Identity Services not loaded');\n      }\n\n      // Initialize Google OAuth\n      window.google.accounts.id.initialize({\n        client_id: '************-el6odh0npaqk6ga79stvvovfreevm19c.apps.googleusercontent.com',\n        callback: async response => {\n          try {\n            await login(response.credential);\n          } catch (error) {\n            setError('Login failed. Please try again.');\n            console.error('Login error:', error);\n          } finally {\n            setLoading(false);\n          }\n        }\n      });\n\n      // Prompt for login\n      window.google.accounts.id.prompt();\n    } catch (error) {\n      setError('Failed to initialize Google login');\n      console.error('Google login initialization error:', error);\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-r from-green-50 to-yellow-50 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-md w-full mx-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white dark:bg-gray-800 rounded-lg shadow-xl p-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mx-auto w-20 h-20 bg-gradient-to-r from-green-600 to-yellow-500 rounded-full flex items-center justify-center mb-4\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-white text-2xl font-bold\",\n              children: \"U\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-2xl font-bold text-gray-900 dark:text-white mb-2\",\n            children: \"Chatbot UNAND\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 dark:text-gray-400 text-sm\",\n            children: \"UNTUK KEDJAJAAN BANGSA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-500 dark:text-gray-500 text-sm mt-2\",\n            children: \"Masuk untuk mengakses chatbot peraturan kampus\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4 p-3 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-lg\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-red-700 dark:text-red-300 text-sm\",\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleGoogleLogin,\n          disabled: loading,\n          className: \"w-full flex items-center justify-center px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n          children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-green-600 mr-3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 17\n            }, this), \"Memproses...\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-5 h-5 mr-3\",\n              viewBox: \"0 0 24 24\",\n              children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                fill: \"currentColor\",\n                d: \"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 84,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                fill: \"currentColor\",\n                d: \"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 88,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                fill: \"currentColor\",\n                d: \"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 92,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                fill: \"currentColor\",\n                d: \"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 96,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 17\n            }, this), \"Masuk dengan Google\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-6 text-center\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs text-gray-500 dark:text-gray-400\",\n            children: \"Dengan masuk, Anda menyetujui penggunaan data untuk keperluan chatbot\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 44,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"bAQFmwhlx/LqLxhH1YIXa/4Tc1Y=\", false, function () {\n  return [useAuth];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "useAuth", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "_s", "login", "loading", "setLoading", "error", "setError", "handleGoogleLogin", "window", "google", "Error", "accounts", "id", "initialize", "client_id", "callback", "response", "credential", "console", "prompt", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "viewBox", "fill", "d", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/website kp/chatbot-unand/frontend/src/components/Login.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\n\nconst Login = () => {\n  const { login } = useAuth();\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n\n  const handleGoogleLogin = async () => {\n    setLoading(true);\n    setError(null);\n\n    try {\n      // Load Google Identity Services\n      if (!window.google) {\n        throw new Error('Google Identity Services not loaded');\n      }\n\n      // Initialize Google OAuth\n      window.google.accounts.id.initialize({\n        client_id: '************-el6odh0npaqk6ga79stvvovfreevm19c.apps.googleusercontent.com',\n        callback: async (response) => {\n          try {\n            await login(response.credential);\n          } catch (error) {\n            setError('Login failed. Please try again.');\n            console.error('Login error:', error);\n          } finally {\n            setLoading(false);\n          }\n        },\n      });\n\n      // Prompt for login\n      window.google.accounts.id.prompt();\n    } catch (error) {\n      setError('Failed to initialize Google login');\n      console.error('Google login initialization error:', error);\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-r from-green-50 to-yellow-50 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center\">\n      <div className=\"max-w-md w-full mx-4\">\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-xl p-8\">\n          {/* Logo and Title */}\n          <div className=\"text-center mb-8\">\n            <div className=\"mx-auto w-20 h-20 bg-gradient-to-r from-green-600 to-yellow-500 rounded-full flex items-center justify-center mb-4\">\n              <span className=\"text-white text-2xl font-bold\">U</span>\n            </div>\n            <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-2\">\n              Chatbot UNAND\n            </h1>\n            <p className=\"text-gray-600 dark:text-gray-400 text-sm\">\n              UNTUK KEDJAJAAN BANGSA\n            </p>\n            <p className=\"text-gray-500 dark:text-gray-500 text-sm mt-2\">\n              Masuk untuk mengakses chatbot peraturan kampus\n            </p>\n          </div>\n\n          {/* Error Message */}\n          {error && (\n            <div className=\"mb-4 p-3 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-lg\">\n              <p className=\"text-red-700 dark:text-red-300 text-sm\">{error}</p>\n            </div>\n          )}\n\n          {/* Google Login Button */}\n          <button\n            onClick={handleGoogleLogin}\n            disabled={loading}\n            className=\"w-full flex items-center justify-center px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n          >\n            {loading ? (\n              <div className=\"flex items-center\">\n                <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-green-600 mr-3\"></div>\n                Memproses...\n              </div>\n            ) : (\n              <div className=\"flex items-center\">\n                <svg className=\"w-5 h-5 mr-3\" viewBox=\"0 0 24 24\">\n                  <path\n                    fill=\"currentColor\"\n                    d=\"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\n                  />\n                  <path\n                    fill=\"currentColor\"\n                    d=\"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\n                  />\n                  <path\n                    fill=\"currentColor\"\n                    d=\"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\n                  />\n                  <path\n                    fill=\"currentColor\"\n                    d=\"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\n                  />\n                </svg>\n                Masuk dengan Google\n              </div>\n            )}\n          </button>\n\n          {/* Footer */}\n          <div className=\"mt-6 text-center\">\n            <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n              Dengan masuk, Anda menyetujui penggunaan data untuk keperluan chatbot\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Login;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM;IAAEC;EAAM,CAAC,GAAGL,OAAO,CAAC,CAAC;EAC3B,MAAM,CAACM,OAAO,EAAEC,UAAU,CAAC,GAAGR,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACS,KAAK,EAAEC,QAAQ,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EAExC,MAAMW,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpCH,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF;MACA,IAAI,CAACE,MAAM,CAACC,MAAM,EAAE;QAClB,MAAM,IAAIC,KAAK,CAAC,qCAAqC,CAAC;MACxD;;MAEA;MACAF,MAAM,CAACC,MAAM,CAACE,QAAQ,CAACC,EAAE,CAACC,UAAU,CAAC;QACnCC,SAAS,EAAE,0EAA0E;QACrFC,QAAQ,EAAE,MAAOC,QAAQ,IAAK;UAC5B,IAAI;YACF,MAAMd,KAAK,CAACc,QAAQ,CAACC,UAAU,CAAC;UAClC,CAAC,CAAC,OAAOZ,KAAK,EAAE;YACdC,QAAQ,CAAC,iCAAiC,CAAC;YAC3CY,OAAO,CAACb,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;UACtC,CAAC,SAAS;YACRD,UAAU,CAAC,KAAK,CAAC;UACnB;QACF;MACF,CAAC,CAAC;;MAEF;MACAI,MAAM,CAACC,MAAM,CAACE,QAAQ,CAACC,EAAE,CAACO,MAAM,CAAC,CAAC;IACpC,CAAC,CAAC,OAAOd,KAAK,EAAE;MACdC,QAAQ,CAAC,mCAAmC,CAAC;MAC7CY,OAAO,CAACb,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1DD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEL,OAAA;IAAKqB,SAAS,EAAC,+HAA+H;IAAAC,QAAA,eAC5ItB,OAAA;MAAKqB,SAAS,EAAC,sBAAsB;MAAAC,QAAA,eACnCtB,OAAA;QAAKqB,SAAS,EAAC,oDAAoD;QAAAC,QAAA,gBAEjEtB,OAAA;UAAKqB,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BtB,OAAA;YAAKqB,SAAS,EAAC,oHAAoH;YAAAC,QAAA,eACjItB,OAAA;cAAMqB,SAAS,EAAC,+BAA+B;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC,eACN1B,OAAA;YAAIqB,SAAS,EAAC,uDAAuD;YAAAC,QAAA,EAAC;UAEtE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL1B,OAAA;YAAGqB,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAExD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJ1B,OAAA;YAAGqB,SAAS,EAAC,+CAA+C;YAAAC,QAAA,EAAC;UAE7D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,EAGLpB,KAAK,iBACJN,OAAA;UAAKqB,SAAS,EAAC,yFAAyF;UAAAC,QAAA,eACtGtB,OAAA;YAAGqB,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAEhB;UAAK;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CACN,eAGD1B,OAAA;UACE2B,OAAO,EAAEnB,iBAAkB;UAC3BoB,QAAQ,EAAExB,OAAQ;UAClBiB,SAAS,EAAC,kWAAkW;UAAAC,QAAA,EAE3WlB,OAAO,gBACNJ,OAAA;YAAKqB,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCtB,OAAA;cAAKqB,SAAS,EAAC;YAAoE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,gBAE5F;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,gBAEN1B,OAAA;YAAKqB,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCtB,OAAA;cAAKqB,SAAS,EAAC,cAAc;cAACQ,OAAO,EAAC,WAAW;cAAAP,QAAA,gBAC/CtB,OAAA;gBACE8B,IAAI,EAAC,cAAc;gBACnBC,CAAC,EAAC;cAAyH;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5H,CAAC,eACF1B,OAAA;gBACE8B,IAAI,EAAC,cAAc;gBACnBC,CAAC,EAAC;cAAuI;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1I,CAAC,eACF1B,OAAA;gBACE8B,IAAI,EAAC,cAAc;gBACnBC,CAAC,EAAC;cAA+H;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClI,CAAC,eACF1B,OAAA;gBACE8B,IAAI,EAAC,cAAc;gBACnBC,CAAC,EAAC;cAAqI;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,uBAER;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,eAGT1B,OAAA;UAAKqB,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC/BtB,OAAA;YAAGqB,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAExD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACxB,EAAA,CAhHID,KAAK;EAAA,QACSH,OAAO;AAAA;AAAAkC,EAAA,GADrB/B,KAAK;AAkHX,eAAeA,KAAK;AAAC,IAAA+B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}