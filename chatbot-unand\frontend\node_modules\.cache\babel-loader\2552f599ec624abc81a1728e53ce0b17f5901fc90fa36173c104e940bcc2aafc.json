{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z\",\n  key: \"1rqfz7\"\n}], [\"rect\", {\n  width: \"8\",\n  height: \"6\",\n  x: \"8\",\n  y: \"12\",\n  rx: \"1\",\n  key: \"3yr8at\"\n}], [\"path\", {\n  d: \"M10 12v-2a2 2 0 1 1 4 0v2\",\n  key: \"j4i8d\"\n}]];\nconst FileLock = createLucideIcon(\"file-lock\", __iconNode);\nexport { __iconNode, FileLock as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "width", "height", "x", "y", "rx", "FileLock", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\node_modules\\lucide-react\\src\\icons\\file-lock.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z', key: '1rqfz7' }],\n  ['rect', { width: '8', height: '6', x: '8', y: '12', rx: '1', key: '3yr8at' }],\n  ['path', { d: 'M10 12v-2a2 2 0 1 1 4 0v2', key: 'j4i8d' }],\n];\n\n/**\n * @component @name FileLock\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTUgMkg2YTIgMiAwIDAgMC0yIDJ2MTZhMiAyIDAgMCAwIDIgMmgxMmEyIDIgMCAwIDAgMi0yVjdaIiAvPgogIDxyZWN0IHdpZHRoPSI4IiBoZWlnaHQ9IjYiIHg9IjgiIHk9IjEyIiByeD0iMSIgLz4KICA8cGF0aCBkPSJNMTAgMTJ2LTJhMiAyIDAgMSAxIDQgMHYyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/file-lock\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst FileLock = createLucideIcon('file-lock', __iconNode);\n\nexport default FileLock;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,4DAA8D;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3F,CAAC,QAAQ;EAAEC,KAAA,EAAO;EAAKC,MAAQ;EAAKC,CAAG;EAAKC,CAAA,EAAG,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAL,GAAA,EAAK;AAAA,CAAU,GAC7E,CAAC,MAAQ;EAAED,CAAA,EAAG,2BAA6B;EAAAC,GAAA,EAAK;AAAS,GAC3D;AAaM,MAAAM,QAAA,GAAWC,gBAAiB,cAAaT,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}