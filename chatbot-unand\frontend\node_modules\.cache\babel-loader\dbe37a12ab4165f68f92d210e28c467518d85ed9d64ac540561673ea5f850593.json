{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"18\",\n  cy: \"5\",\n  r: \"3\",\n  key: \"gq8acd\"\n}], [\"circle\", {\n  cx: \"6\",\n  cy: \"12\",\n  r: \"3\",\n  key: \"w7nqdw\"\n}], [\"circle\", {\n  cx: \"18\",\n  cy: \"19\",\n  r: \"3\",\n  key: \"1xt0gg\"\n}], [\"line\", {\n  x1: \"8.59\",\n  x2: \"15.42\",\n  y1: \"13.51\",\n  y2: \"17.49\",\n  key: \"47mynk\"\n}], [\"line\", {\n  x1: \"15.41\",\n  x2: \"8.59\",\n  y1: \"6.51\",\n  y2: \"10.49\",\n  key: \"1n3mei\"\n}]];\nconst Share2 = createLucideIcon(\"share-2\", __iconNode);\nexport { __iconNode, Share2 as default };", "map": {"version": 3, "names": ["__iconNode", "cx", "cy", "r", "key", "x1", "x2", "y1", "y2", "Share2", "createLucideIcon"], "sources": ["D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\node_modules\\lucide-react\\src\\icons\\share-2.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '18', cy: '5', r: '3', key: 'gq8acd' }],\n  ['circle', { cx: '6', cy: '12', r: '3', key: 'w7nqdw' }],\n  ['circle', { cx: '18', cy: '19', r: '3', key: '1xt0gg' }],\n  ['line', { x1: '8.59', x2: '15.42', y1: '13.51', y2: '17.49', key: '47mynk' }],\n  ['line', { x1: '15.41', x2: '8.59', y1: '6.51', y2: '10.49', key: '1n3mei' }],\n];\n\n/**\n * @component @name Share2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxOCIgY3k9IjUiIHI9IjMiIC8+CiAgPGNpcmNsZSBjeD0iNiIgY3k9IjEyIiByPSIzIiAvPgogIDxjaXJjbGUgY3g9IjE4IiBjeT0iMTkiIHI9IjMiIC8+CiAgPGxpbmUgeDE9IjguNTkiIHgyPSIxNS40MiIgeTE9IjEzLjUxIiB5Mj0iMTcuNDkiIC8+CiAgPGxpbmUgeDE9IjE1LjQxIiB4Mj0iOC41OSIgeTE9IjYuNTEiIHkyPSIxMC40OSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/share-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Share2 = createLucideIcon('share-2', __iconNode);\n\nexport default Share2;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAKC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACvD,CAAC,QAAU;EAAEH,EAAI;EAAKC,EAAI;EAAMC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACvD,CAAC,QAAU;EAAEH,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACxD,CAAC,QAAQ;EAAEC,EAAA,EAAI,MAAQ;EAAAC,EAAA,EAAI,OAAS;EAAAC,EAAA,EAAI,OAAS;EAAAC,EAAA,EAAI,OAAS;EAAAJ,GAAA,EAAK;AAAA,CAAU,GAC7E,CAAC,QAAQ;EAAEC,EAAA,EAAI,OAAS;EAAAC,EAAA,EAAI,MAAQ;EAAAC,EAAA,EAAI,MAAQ;EAAAC,EAAA,EAAI,OAAS;EAAAJ,GAAA,EAAK;AAAU,GAC9E;AAaM,MAAAK,MAAA,GAASC,gBAAiB,YAAWV,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}