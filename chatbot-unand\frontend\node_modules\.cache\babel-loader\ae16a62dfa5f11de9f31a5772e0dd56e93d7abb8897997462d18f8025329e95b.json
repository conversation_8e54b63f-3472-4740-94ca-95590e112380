{"ast": null, "code": "'use strict';\n\nvar fails = require('../internals/fails');\nvar isCallable = require('../internals/is-callable');\nvar replacement = /#|\\.prototype\\./;\nvar isForced = function (feature, detection) {\n  var value = data[normalize(feature)];\n  return value === POLYFILL ? true : value === NATIVE ? false : isCallable(detection) ? fails(detection) : !!detection;\n};\nvar normalize = isForced.normalize = function (string) {\n  return String(string).replace(replacement, '.').toLowerCase();\n};\nvar data = isForced.data = {};\nvar NATIVE = isForced.NATIVE = 'N';\nvar POLYFILL = isForced.POLYFILL = 'P';\nmodule.exports = isForced;", "map": {"version": 3, "names": ["fails", "require", "isCallable", "replacement", "isForced", "feature", "detection", "value", "data", "normalize", "POLYFILL", "NATIVE", "string", "String", "replace", "toLowerCase", "module", "exports"], "sources": ["C:/Users/<USER>/Downloads/website kp/chatbot-unand/frontend/node_modules/core-js-pure/internals/is-forced.js"], "sourcesContent": ["'use strict';\nvar fails = require('../internals/fails');\nvar isCallable = require('../internals/is-callable');\n\nvar replacement = /#|\\.prototype\\./;\n\nvar isForced = function (feature, detection) {\n  var value = data[normalize(feature)];\n  return value === POLYFILL ? true\n    : value === NATIVE ? false\n    : isCallable(detection) ? fails(detection)\n    : !!detection;\n};\n\nvar normalize = isForced.normalize = function (string) {\n  return String(string).replace(replacement, '.').toLowerCase();\n};\n\nvar data = isForced.data = {};\nvar NATIVE = isForced.NATIVE = 'N';\nvar POLYFILL = isForced.POLYFILL = 'P';\n\nmodule.exports = isForced;\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,oBAAoB,CAAC;AACzC,IAAIC,UAAU,GAAGD,OAAO,CAAC,0BAA0B,CAAC;AAEpD,IAAIE,WAAW,GAAG,iBAAiB;AAEnC,IAAIC,QAAQ,GAAG,SAAAA,CAAUC,OAAO,EAAEC,SAAS,EAAE;EAC3C,IAAIC,KAAK,GAAGC,IAAI,CAACC,SAAS,CAACJ,OAAO,CAAC,CAAC;EACpC,OAAOE,KAAK,KAAKG,QAAQ,GAAG,IAAI,GAC5BH,KAAK,KAAKI,MAAM,GAAG,KAAK,GACxBT,UAAU,CAACI,SAAS,CAAC,GAAGN,KAAK,CAACM,SAAS,CAAC,GACxC,CAAC,CAACA,SAAS;AACjB,CAAC;AAED,IAAIG,SAAS,GAAGL,QAAQ,CAACK,SAAS,GAAG,UAAUG,MAAM,EAAE;EACrD,OAAOC,MAAM,CAACD,MAAM,CAAC,CAACE,OAAO,CAACX,WAAW,EAAE,GAAG,CAAC,CAACY,WAAW,CAAC,CAAC;AAC/D,CAAC;AAED,IAAIP,IAAI,GAAGJ,QAAQ,CAACI,IAAI,GAAG,CAAC,CAAC;AAC7B,IAAIG,MAAM,GAAGP,QAAQ,CAACO,MAAM,GAAG,GAAG;AAClC,IAAID,QAAQ,GAAGN,QAAQ,CAACM,QAAQ,GAAG,GAAG;AAEtCM,MAAM,CAACC,OAAO,GAAGb,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}