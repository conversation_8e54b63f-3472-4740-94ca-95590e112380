{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10.5 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v5.5\",\n  key: \"1g2yzs\"\n}], [\"path\", {\n  d: \"m14.3 19.6 1-.4\",\n  key: \"11sv9r\"\n}], [\"path\", {\n  d: \"M15 3v7.5\",\n  key: \"7lm50a\"\n}], [\"path\", {\n  d: \"m15.2 16.9-.9-.3\",\n  key: \"1t7mvx\"\n}], [\"path\", {\n  d: \"m16.6 21.7.3-.9\",\n  key: \"1j67ps\"\n}], [\"path\", {\n  d: \"m16.8 15.3-.4-1\",\n  key: \"1ei7r6\"\n}], [\"path\", {\n  d: \"m19.1 15.2.3-.9\",\n  key: \"18r7jp\"\n}], [\"path\", {\n  d: \"m19.6 21.7-.4-1\",\n  key: \"z2vh2\"\n}], [\"path\", {\n  d: \"m20.7 16.8 1-.4\",\n  key: \"19m87a\"\n}], [\"path\", {\n  d: \"m21.7 19.4-.9-.3\",\n  key: \"1qgwi9\"\n}], [\"path\", {\n  d: \"M9 3v18\",\n  key: \"fh3hqa\"\n}], [\"circle\", {\n  cx: \"18\",\n  cy: \"18\",\n  r: \"3\",\n  key: \"1xkwt0\"\n}]];\nconst Columns3Cog = createLucideIcon(\"columns-3-cog\", __iconNode);\nexport { __iconNode, Columns3Cog as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "cx", "cy", "r", "Columns3Cog", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Downloads\\website kp\\chatbot-unand\\frontend\\node_modules\\lucide-react\\src\\icons\\columns-3-cog.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M10.5 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v5.5', key: '1g2yzs' }],\n  ['path', { d: 'm14.3 19.6 1-.4', key: '11sv9r' }],\n  ['path', { d: 'M15 3v7.5', key: '7lm50a' }],\n  ['path', { d: 'm15.2 16.9-.9-.3', key: '1t7mvx' }],\n  ['path', { d: 'm16.6 21.7.3-.9', key: '1j67ps' }],\n  ['path', { d: 'm16.8 15.3-.4-1', key: '1ei7r6' }],\n  ['path', { d: 'm19.1 15.2.3-.9', key: '18r7jp' }],\n  ['path', { d: 'm19.6 21.7-.4-1', key: 'z2vh2' }],\n  ['path', { d: 'm20.7 16.8 1-.4', key: '19m87a' }],\n  ['path', { d: 'm21.7 19.4-.9-.3', key: '1qgwi9' }],\n  ['path', { d: 'M9 3v18', key: 'fh3hqa' }],\n  ['circle', { cx: '18', cy: '18', r: '3', key: '1xkwt0' }],\n];\n\n/**\n * @component @name Columns3Cog\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAuNSAyMUg1YTIgMiAwIDAgMS0yLTJWNWEyIDIgMCAwIDEgMi0yaDE0YTIgMiAwIDAgMSAyIDJ2NS41IiAvPgogIDxwYXRoIGQ9Im0xNC4zIDE5LjYgMS0uNCIgLz4KICA8cGF0aCBkPSJNMTUgM3Y3LjUiIC8+CiAgPHBhdGggZD0ibTE1LjIgMTYuOS0uOS0uMyIgLz4KICA8cGF0aCBkPSJtMTYuNiAyMS43LjMtLjkiIC8+CiAgPHBhdGggZD0ibTE2LjggMTUuMy0uNC0xIiAvPgogIDxwYXRoIGQ9Im0xOS4xIDE1LjIuMy0uOSIgLz4KICA8cGF0aCBkPSJtMTkuNiAyMS43LS40LTEiIC8+CiAgPHBhdGggZD0ibTIwLjcgMTYuOCAxLS40IiAvPgogIDxwYXRoIGQ9Im0yMS43IDE5LjQtLjktLjMiIC8+CiAgPHBhdGggZD0iTTkgM3YxOCIgLz4KICA8Y2lyY2xlIGN4PSIxOCIgY3k9IjE4IiByPSIzIiAvPgo8L3N2Zz4=) - https://lucide.dev/icons/columns-3-cog\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Columns3Cog = createLucideIcon('columns-3-cog', __iconNode);\n\nexport default Columns3Cog;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,+DAAiE;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9F,CAAC,MAAQ;EAAED,CAAA,EAAG,iBAAmB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChD,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,kBAAoB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACjD,CAAC,MAAQ;EAAED,CAAA,EAAG,iBAAmB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChD,CAAC,MAAQ;EAAED,CAAA,EAAG,iBAAmB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChD,CAAC,MAAQ;EAAED,CAAA,EAAG,iBAAmB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChD,CAAC,MAAQ;EAAED,CAAA,EAAG,iBAAmB;EAAAC,GAAA,EAAK;AAAA,CAAS,GAC/C,CAAC,MAAQ;EAAED,CAAA,EAAG,iBAAmB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChD,CAAC,MAAQ;EAAED,CAAA,EAAG,kBAAoB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACjD,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKH,GAAK;AAAU,GAC1D;AAaM,MAAAI,WAAA,GAAcC,gBAAiB,kBAAiBP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}