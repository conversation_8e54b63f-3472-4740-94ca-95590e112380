# @babel/plugin-transform-block-scoped-functions

> Babel plugin to ensure function declarations at the block level are block scoped

See our website [@babel/plugin-transform-block-scoped-functions](https://babeljs.io/docs/babel-plugin-transform-block-scoped-functions) for more information.

## Install

Using npm:

```sh
npm install --save-dev @babel/plugin-transform-block-scoped-functions
```

or using yarn:

```sh
yarn add @babel/plugin-transform-block-scoped-functions --dev
```
