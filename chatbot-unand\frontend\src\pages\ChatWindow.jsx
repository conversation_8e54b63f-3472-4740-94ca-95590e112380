import { useState, useEffect, useRef } from "react";
import Message from "../Message";
import ChatInput from "../ChatInput";
import { sendMessageToChatbot } from "../api";

const ChatWindow = ({
  messages,
  setMessages,
  currentSessionId,
  setCurrentSessionId,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async (text) => {
    const userMessage = {
      id: Date.now(),
      text: text,
      isBot: false,
      timestamp: new Date(),
    };
    setMessages((prevMessages) => [...prevMessages, userMessage]);
    setIsLoading(true);

    try {
      const response = await sendMessageToChatbot(text, currentSessionId);

      // Update session ID if this is a new chat
      if (!currentSessionId && response.session_id) {
        setCurrentSessionId(response.session_id);
      }

      const botMessage = {
        id: Date.now() + 1,
        text: response.response,
        isBot: true,
        timestamp: new Date(),
        sources: response.sources,
        sources_count: response.sources_count,
        summary: response.summary,
        suggestions: response.suggestions,
        is_greeting: response.is_greeting,
      };
      setMessages((prevMessages) => [...prevMessages, botMessage]);
    } catch (error) {
      const errorMessage = {
        id: Date.now() + 1,
        text: `Maaf, terjadi kesalahan saat menghubungi chatbot: ${error.message}`,
        isBot: true,
        timestamp: new Date(),
        isError: true,
      };
      setMessages((prevMessages) => [...prevMessages, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex flex-col h-full bg-transparent font-sans">
      {/* Messages Area - Scrollable */}
      <div className="flex-1 overflow-y-auto scrollbar-hide">
        <div className="space-y-4 p-4 min-h-full flex flex-col">
          {/* Messages container that grows to fill space */}
          <div className="flex-1 space-y-4">
            {messages.map((message) => (
              <Message key={message.id} message={message} />
            ))}

            {isLoading && (
              <div className="flex justify-start">
                <div className="bg-gradient-to-r from-green-100 to-yellow-100 dark:from-gray-700 dark:to-gray-600 border border-green-300 dark:border-gray-500 rounded-lg p-3 max-w-xs shadow-md">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-green-600 dark:bg-green-400 rounded-full animate-bounce"></div>
                    <div
                      className="w-2 h-2 bg-yellow-600 dark:bg-yellow-400 rounded-full animate-bounce"
                      style={{ animationDelay: "0.1s" }}
                    ></div>
                    <div
                      className="w-2 h-2 bg-green-600 dark:bg-green-400 rounded-full animate-bounce"
                      style={{ animationDelay: "0.2s" }}
                    ></div>
                  </div>
                </div>
              </div>
            )}
          </div>
          <div ref={messagesEndRef} />
        </div>
      </div>

      {/* Input Area - Fixed at bottom */}
      <ChatInput onSendMessage={handleSendMessage} isLoading={isLoading} />
    </div>
  );
};

export default ChatWindow;
