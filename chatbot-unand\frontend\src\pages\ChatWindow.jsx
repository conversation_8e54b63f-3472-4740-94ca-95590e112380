import { useState, useEffect, useRef } from "react";
import Message from "../Message";
import ChatInput from "../ChatInput";
import { sendMessageToChatbot } from "../api";
import { useAuth } from "../contexts/AuthContext";

const ChatWindow = ({
  messages,
  setMessages,
  currentSessionId,
  setCurrentSessionId,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef(null);
  const { user } = useAuth();

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Welcome Screen Component
  const WelcomeScreen = () => {
    const Name = user?.name?.split(' ')[0] || 'User';

    return (
      <div className="flex flex-col items-center justify-center min-h-[70vh] px-4">
        {/* Greeting */}
        <div className="text-center mb-16">
          <h1 className="text-5xl md:text-6xl font-light text-green-400 mb-4">
            Halo, {Name}
          </h1>
          <p className="text-xl text-gray-400">
            Apa yang bisa saya membantu Anda hari ini?
          </p>
        </div>

        {/* Quick Action Buttons */}
        <div className="flex flex-wrap gap-3 mb-8 justify-center">
          <button
            onClick={() => handleSendMessage("Apa itu UNAND?")}
            className="flex items-center gap-2 px-4 py-2 bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-full text-sm text-gray-700 dark:text-gray-300 transition-colors"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            Tentang UNAND
          </button>

        </div>
      </div>
    );
  };

  const handleSendMessage = async (text) => {
    const userMessage = {
      id: Date.now(),
      text: text,
      isBot: false,
      timestamp: new Date(),
    };
    setMessages((prevMessages) => [...prevMessages, userMessage]);
    setIsLoading(true);

    try {
      const response = await sendMessageToChatbot(text, currentSessionId);

      // Update session ID if this is a new chat
      if (!currentSessionId && response.session_id) {
        setCurrentSessionId(response.session_id);
      }

      const botMessage = {
        id: Date.now() + 1,
        text: response.response,
        isBot: true,
        timestamp: new Date(),
        sources: response.sources,
        sources_count: response.sources_count,
        summary: response.summary,
        suggestions: response.suggestions,
        is_greeting: response.is_greeting,
      };
      setMessages((prevMessages) => [...prevMessages, botMessage]);
    } catch (error) {
      const errorMessage = {
        id: Date.now() + 1,
        text: `Maaf, terjadi kesalahan saat menghubungi chatbot: ${error.message}`,
        isBot: true,
        timestamp: new Date(),
        isError: true,
      };
      setMessages((prevMessages) => [...prevMessages, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex flex-col h-full bg-white dark:bg-gray-900 font-sans">
      {/* Messages Area - Scrollable */}
      <div className="flex-1 overflow-y-auto scrollbar-hide">
        <div className="min-h-full flex flex-col">
          {/* Show Welcome Screen if no messages */}
          {messages.length === 0 && !isLoading ? (
            <WelcomeScreen />
          ) : (
            <div className="space-y-4 p-4 flex-1">
              {/* Messages container */}
              <div className="space-y-4">
                {messages.map((message) => (
                  <Message key={message.id} message={message} />
                ))}

                {isLoading && (
                  <div className="flex justify-start">
                    <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-gray-700 dark:to-gray-600 border border-blue-200 dark:border-gray-500 rounded-lg p-3 max-w-xs shadow-md">
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full animate-bounce"></div>
                        <div
                          className="w-2 h-2 bg-purple-600 dark:bg-purple-400 rounded-full animate-bounce"
                          style={{ animationDelay: "0.1s" }}
                        ></div>
                        <div
                          className="w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full animate-bounce"
                          style={{ animationDelay: "0.2s" }}
                        ></div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
              <div ref={messagesEndRef} />
            </div>
          )}
        </div>
      </div>

      {/* Input Area - Fixed at bottom */}
      <ChatInput onSendMessage={handleSendMessage} isLoading={isLoading} />
    </div>
  );
};

export default ChatWindow;
