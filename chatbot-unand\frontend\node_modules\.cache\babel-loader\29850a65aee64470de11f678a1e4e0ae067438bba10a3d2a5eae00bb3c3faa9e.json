{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M9 9c-.64.64-1.521.954-2.402 1.165A6 6 0 0 0 8 22a13.96 13.96 0 0 0 9.9-4.1\",\n  key: \"bq3udt\"\n}], [\"path\", {\n  d: \"M10.75 5.093A6 6 0 0 1 22 8c0 2.411-.61 4.68-1.683 6.66\",\n  key: \"17ccse\"\n}], [\"path\", {\n  d: \"M5.341 10.62a4 4 0 0 0 6.487 1.208M10.62 5.341a4.015 4.015 0 0 1 2.039 2.04\",\n  key: \"18zqgq\"\n}], [\"line\", {\n  x1: \"2\",\n  x2: \"22\",\n  y1: \"2\",\n  y2: \"22\",\n  key: \"a6p6uj\"\n}]];\nconst BeanOff = createLucideIcon(\"bean-off\", __iconNode);\nexport { __iconNode, BeanOff as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "x1", "x2", "y1", "y2", "<PERSON><PERSON><PERSON>", "createLucideIcon"], "sources": ["D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\node_modules\\lucide-react\\src\\icons\\bean-off.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M9 9c-.64.64-1.521.954-2.402 1.165A6 6 0 0 0 8 22a13.96 13.96 0 0 0 9.9-4.1',\n      key: 'bq3udt',\n    },\n  ],\n  ['path', { d: 'M10.75 5.093A6 6 0 0 1 22 8c0 2.411-.61 4.68-1.683 6.66', key: '17ccse' }],\n  [\n    'path',\n    {\n      d: 'M5.341 10.62a4 4 0 0 0 6.487 1.208M10.62 5.341a4.015 4.015 0 0 1 2.039 2.04',\n      key: '18zqgq',\n    },\n  ],\n  ['line', { x1: '2', x2: '22', y1: '2', y2: '22', key: 'a6p6uj' }],\n];\n\n/**\n * @component @name BeanOff\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNOSA5Yy0uNjQuNjQtMS41MjEuOTU0LTIuNDAyIDEuMTY1QTYgNiAwIDAgMCA4IDIyYTEzLjk2IDEzLjk2IDAgMCAwIDkuOS00LjEiIC8+CiAgPHBhdGggZD0iTTEwLjc1IDUuMDkzQTYgNiAwIDAgMSAyMiA4YzAgMi40MTEtLjYxIDQuNjgtMS42ODMgNi42NiIgLz4KICA8cGF0aCBkPSJNNS4zNDEgMTAuNjJhNCA0IDAgMCAwIDYuNDg3IDEuMjA4TTEwLjYyIDUuMzQxYTQuMDE1IDQuMDE1IDAgMCAxIDIuMDM5IDIuMDQiIC8+CiAgPGxpbmUgeDE9IjIiIHgyPSIyMiIgeTE9IjIiIHkyPSIyMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/bean-off\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst BeanOff = createLucideIcon('bean-off', __iconNode);\n\nexport default BeanOff;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AAAA,EAET,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,yDAA2D;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxF,CACE,QACA;EACED,CAAG;EACHC,GAAK;AAAA,EAET,EACA,CAAC,QAAQ;EAAEC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAU,GAClE;AAaM,MAAAK,OAAA,GAAUC,gBAAiB,aAAYR,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}