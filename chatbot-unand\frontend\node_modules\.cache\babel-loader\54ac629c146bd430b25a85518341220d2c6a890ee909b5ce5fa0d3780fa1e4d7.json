{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M 20 4 A2 2 0 0 1 22 6\",\n  key: \"1g1fkt\"\n}], [\"path\", {\n  d: \"M 22 6 L 22 16.41\",\n  key: \"1qjg3w\"\n}], [\"path\", {\n  d: \"M 7 16 L 16 16\",\n  key: \"n0yqwb\"\n}], [\"path\", {\n  d: \"M 9.69 4 L 20 4\",\n  key: \"kbpcgx\"\n}], [\"path\", {\n  d: \"M14 8h.01\",\n  key: \"1primd\"\n}], [\"path\", {\n  d: \"M18 8h.01\",\n  key: \"emo2bl\"\n}], [\"path\", {\n  d: \"m2 2 20 20\",\n  key: \"1ooewy\"\n}], [\"path\", {\n  d: \"M20 20H4a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2\",\n  key: \"s23sx2\"\n}], [\"path\", {\n  d: \"M6 8h.01\",\n  key: \"x9i8wu\"\n}], [\"path\", {\n  d: \"M8 12h.01\",\n  key: \"czm47f\"\n}]];\nconst KeyboardOff = createLucideIcon(\"keyboard-off\", __iconNode);\nexport { __iconNode, KeyboardOff as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "KeyboardOff", "createLucideIcon"], "sources": ["D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\node_modules\\lucide-react\\src\\icons\\keyboard-off.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M 20 4 A2 2 0 0 1 22 6', key: '1g1fkt' }],\n  ['path', { d: 'M 22 6 L 22 16.41', key: '1qjg3w' }],\n  ['path', { d: 'M 7 16 L 16 16', key: 'n0yqwb' }],\n  ['path', { d: 'M 9.69 4 L 20 4', key: 'kbpcgx' }],\n  ['path', { d: 'M14 8h.01', key: '1primd' }],\n  ['path', { d: 'M18 8h.01', key: 'emo2bl' }],\n  ['path', { d: 'm2 2 20 20', key: '1ooewy' }],\n  ['path', { d: 'M20 20H4a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2', key: 's23sx2' }],\n  ['path', { d: 'M6 8h.01', key: 'x9i8wu' }],\n  ['path', { d: 'M8 12h.01', key: 'czm47f' }],\n];\n\n/**\n * @component @name KeyboardOff\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNIDIwIDQgQTIgMiAwIDAgMSAyMiA2IiAvPgogIDxwYXRoIGQ9Ik0gMjIgNiBMIDIyIDE2LjQxIiAvPgogIDxwYXRoIGQ9Ik0gNyAxNiBMIDE2IDE2IiAvPgogIDxwYXRoIGQ9Ik0gOS42OSA0IEwgMjAgNCIgLz4KICA8cGF0aCBkPSJNMTQgOGguMDEiIC8+CiAgPHBhdGggZD0iTTE4IDhoLjAxIiAvPgogIDxwYXRoIGQ9Im0yIDIgMjAgMjAiIC8+CiAgPHBhdGggZD0iTTIwIDIwSDRhMiAyIDAgMCAxLTItMlY2YTIgMiAwIDAgMSAyLTIiIC8+CiAgPHBhdGggZD0iTTYgOGguMDEiIC8+CiAgPHBhdGggZD0iTTggMTJoLjAxIiAvPgo8L3N2Zz4=) - https://lucide.dev/icons/keyboard-off\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst KeyboardOff = createLucideIcon('keyboard-off', __iconNode);\n\nexport default KeyboardOff;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,wBAA0B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAED,CAAA,EAAG,mBAAqB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAClD,CAAC,MAAQ;EAAED,CAAA,EAAG,gBAAkB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/C,CAAC,MAAQ;EAAED,CAAA,EAAG,iBAAmB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChD,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,wCAA0C;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvE,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAU,GAC5C;AAaM,MAAAC,WAAA,GAAcC,gBAAiB,iBAAgBJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}