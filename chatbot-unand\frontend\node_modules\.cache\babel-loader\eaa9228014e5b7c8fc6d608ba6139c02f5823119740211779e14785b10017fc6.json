{"ast": null, "code": "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"10\",\n  key: \"1mglay\"\n}], [\"path\", {\n  d: \"M10 9.3a2.8 2.8 0 0 0-3.5 1 3.1 3.1 0 0 0 0 3.4 2.7 2.7 0 0 0 3.5 1\",\n  key: \"1ss3eq\"\n}], [\"path\", {\n  d: \"M17 9.3a2.8 2.8 0 0 0-3.5 1 3.1 3.1 0 0 0 0 3.4 2.7 2.7 0 0 0 3.5 1\",\n  key: \"1od56t\"\n}]];\nconst CreativeCommons = createLucideIcon(\"creative-commons\", __iconNode);\nexport { __iconNode, CreativeCommons as default };", "map": {"version": 3, "names": ["__iconNode", "cx", "cy", "r", "key", "d", "CreativeCommons", "createLucideIcon"], "sources": ["D:\\KAMPUS\\Magang TA\\MAGANG\\Project\\website kp\\chatbot-unand\\frontend\\node_modules\\lucide-react\\src\\icons\\creative-commons.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  [\n    'path',\n    { d: 'M10 9.3a2.8 2.8 0 0 0-3.5 1 3.1 3.1 0 0 0 0 3.4 2.7 2.7 0 0 0 3.5 1', key: '1ss3eq' },\n  ],\n  [\n    'path',\n    { d: 'M17 9.3a2.8 2.8 0 0 0-3.5 1 3.1 3.1 0 0 0 0 3.4 2.7 2.7 0 0 0 3.5 1', key: '1od56t' },\n  ],\n];\n\n/**\n * @component @name CreativeCommons\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8cGF0aCBkPSJNMTAgOS4zYTIuOCAyLjggMCAwIDAtMy41IDEgMy4xIDMuMSAwIDAgMCAwIDMuNCAyLjcgMi43IDAgMCAwIDMuNSAxIiAvPgogIDxwYXRoIGQ9Ik0xNyA5LjNhMi44IDIuOCAwIDAgMC0zLjUgMSAzLjEgMy4xIDAgMCAwIDAgMy40IDIuNyAyLjcgMCAwIDAgMy41IDEiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/creative-commons\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CreativeCommons = createLucideIcon('creative-commons', __iconNode);\n\nexport default CreativeCommons;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAMC,GAAK;AAAA,CAAU,GACzD,CACE,QACA;EAAEC,CAAA,EAAG,qEAAuE;EAAAD,GAAA,EAAK;AAAS,EAC5F,EACA,CACE,QACA;EAAEC,CAAA,EAAG,qEAAuE;EAAAD,GAAA,EAAK;AAAS,GAE9F;AAaM,MAAAE,eAAA,GAAkBC,gBAAiB,qBAAoBP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}